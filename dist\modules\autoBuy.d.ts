import { SploopGameAPI } from '@/utils/gameApi';
import { ModConfig } from '@/config';
import { Resources } from '@/types/game';
import { EventEmitter } from 'eventemitter3';
export interface ShopItem {
    id: string;
    name: string;
    type: 'weapon' | 'hat' | 'upgrade' | 'consumable' | 'building';
    cost: Resources;
    unlockLevel: number;
    priority: number;
    description: string;
    effects: Record<string, number>;
}
export interface PurchaseHistory {
    itemId: string;
    itemName: string;
    cost: Resources;
    timestamp: number;
    success: boolean;
}
export declare class AutoBuyModule extends EventEmitter {
    private gameApi;
    private config;
    private enabled;
    private updateInterval;
    private lastBuyCheck;
    private purchaseHistory;
    private buyQueue;
    private readonly SHOP_ITEMS;
    constructor(gameApi: SploopGameAPI, config: ModConfig);
    start(): void;
    stop(): void;
    updateConfig(config: ModConfig): void;
    private onGameStateUpdate;
    private update;
    private processAutoBuy;
    private getAvailableItems;
    private canAffordItem;
    private shouldBuyItem;
    private shouldBuyConsumable;
    private shouldBuyWeapon;
    private shouldBuyUpgrade;
    private queuePurchase;
    private processBuyQueue;
    getShopItems(): ShopItem[];
    getPurchaseHistory(): PurchaseHistory[];
    clearPurchaseHistory(): void;
    addCustomItem(item: ShopItem): void;
    getStats(): any;
}
//# sourceMappingURL=autoBuy.d.ts.map