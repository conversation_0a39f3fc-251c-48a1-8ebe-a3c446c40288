import { SploopG<PERSON>API } from '@/utils/gameApi';
import { ModConfig } from '@/config';
import { Vector2, ResourceType } from '@/types/game';
import { EventEmitter } from 'eventemitter3';
export interface MillPlacement {
    position: Vector2;
    resourceType: ResourceType;
    priority: number;
    safety: number;
    efficiency: number;
}
export interface MillStats {
    id: string;
    position: Vector2;
    resourceType: ResourceType;
    level: number;
    production: number;
    storage: number;
    maxStorage: number;
    lastUpgrade: number;
    isProtected: boolean;
}
export declare class AutoMillsModule extends EventEmitter {
    private gameApi;
    private config;
    private enabled;
    private updateInterval;
    private lastMillCheck;
    private lastPlacementCheck;
    private ownedMills;
    private resourceNodes;
    private dangerZones;
    private readonly MILL_COSTS;
    private readonly UPGRADE_COSTS;
    constructor(gameApi: SploopGameAPI, config: ModConfig);
    start(): void;
    stop(): void;
    updateConfig(config: ModConfig): void;
    private onGameStateUpdate;
    private update;
    private updateMillTracking;
    private updateResourceNodes;
    private updateDangerZones;
    private processMillPlacement;
    private findOptimalMillPlacement;
    private getResourceTypeFromNodeId;
    private isValidMillPosition;
    private calculatePlacementPriority;
    private calculateSafety;
    private calculateEfficiency;
    private canAffordMill;
    private placeMill;
    private processMillUpgrades;
    private canAffordUpgrade;
    private upgradeMill;
    private isMillProtected;
    private protectMills;
    private canAffordSpike;
    private placeProtection;
    getOwnedMills(): MillStats[];
    getStats(): any;
}
//# sourceMappingURL=autoMills.d.ts.map