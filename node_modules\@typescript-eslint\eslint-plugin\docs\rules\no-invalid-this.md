---
description: 'Disallow `this` keywords outside of classes or class-like objects.'
---

> 🛑 This file is source code, not the primary documentation location! 🛑
>
> See **https://typescript-eslint.io/rules/no-invalid-this** for documentation.

import TypeScriptOverlap from "@site/src/components/TypeScriptOverlap";

<TypeScriptOverlap strict />

This rule extends the base [`eslint/no-invalid-this`](https://eslint.org/docs/rules/no-invalid-this) rule.
It adds support for TypeScript's `this` parameters.
