import { SploopGameAPI } from '@/utils/gameApi';
import { ModConfig } from '@/config';
import { Player, ActionType, Resources } from '@/types/game';
import { EventEmitter } from 'eventemitter3';

export interface ShopItem {
  id: string;
  name: string;
  type: 'weapon' | 'hat' | 'upgrade' | 'consumable' | 'building';
  cost: Resources;
  unlockLevel: number;
  priority: number;
  description: string;
  effects: Record<string, number>;
}

export interface PurchaseHistory {
  itemId: string;
  itemName: string;
  cost: Resources;
  timestamp: number;
  success: boolean;
}

export class AutoBuyModule extends EventEmitter {
  private gameApi: SploopGameAPI;
  private config: ModConfig;
  private enabled = false;
  private updateInterval: number | null = null;
  private lastBuyCheck = 0;
  private purchaseHistory: PurchaseHistory[] = [];
  private buyQueue: string[] = [];
  
  // Shop items database
  private readonly SHOP_ITEMS: Record<string, ShopItem> = {
    // Weapons
    'weapon_wood_sword': {
      id: 'weapon_wood_sword',
      name: 'Wood Sword',
      type: 'weapon',
      cost: { wood: 0, stone: 0, food: 0, gold: 100, points: 0 },
      unlockLevel: 1,
      priority: 5,
      description: 'Basic wooden sword',
      effects: { damage: 25 }
    },
    'weapon_stone_sword': {
      id: 'weapon_stone_sword',
      name: 'Stone Sword',
      type: 'weapon',
      cost: { wood: 0, stone: 0, food: 0, gold: 400, points: 0 },
      unlockLevel: 2,
      priority: 6,
      description: 'Stronger stone sword',
      effects: { damage: 35 }
    },
    'weapon_gold_sword': {
      id: 'weapon_gold_sword',
      name: 'Gold Sword',
      type: 'weapon',
      cost: { wood: 0, stone: 0, food: 0, gold: 3000, points: 0 },
      unlockLevel: 5,
      priority: 7,
      description: 'Powerful gold sword',
      effects: { damage: 50 }
    },
    'weapon_diamond_sword': {
      id: 'weapon_diamond_sword',
      name: 'Diamond Sword',
      type: 'weapon',
      cost: { wood: 0, stone: 0, food: 0, gold: 15000, points: 0 },
      unlockLevel: 10,
      priority: 8,
      description: 'Elite diamond sword',
      effects: { damage: 65 }
    },
    
    // Upgrades
    'upgrade_damage_1': {
      id: 'upgrade_damage_1',
      name: 'Damage Upgrade I',
      type: 'upgrade',
      cost: { wood: 0, stone: 0, food: 0, gold: 1000, points: 0 },
      unlockLevel: 2,
      priority: 9,
      description: 'Increases weapon damage',
      effects: { damageMultiplier: 1.1 }
    },
    'upgrade_damage_2': {
      id: 'upgrade_damage_2',
      name: 'Damage Upgrade II',
      type: 'upgrade',
      cost: { wood: 0, stone: 0, food: 0, gold: 3000, points: 0 },
      unlockLevel: 5,
      priority: 9,
      description: 'Further increases weapon damage',
      effects: { damageMultiplier: 1.2 }
    },
    'upgrade_speed_1': {
      id: 'upgrade_speed_1',
      name: 'Speed Upgrade I',
      type: 'upgrade',
      cost: { wood: 0, stone: 0, food: 0, gold: 1500, points: 0 },
      unlockLevel: 3,
      priority: 8,
      description: 'Increases movement speed',
      effects: { speedMultiplier: 1.15 }
    },
    'upgrade_health_1': {
      id: 'upgrade_health_1',
      name: 'Health Upgrade I',
      type: 'upgrade',
      cost: { wood: 0, stone: 0, food: 0, gold: 2000, points: 0 },
      unlockLevel: 4,
      priority: 8,
      description: 'Increases maximum health',
      effects: { healthBonus: 20 }
    },
    
    // Consumables
    'consumable_health_potion': {
      id: 'consumable_health_potion',
      name: 'Health Potion',
      type: 'consumable',
      cost: { wood: 0, stone: 0, food: 0, gold: 200, points: 0 },
      unlockLevel: 1,
      priority: 6,
      description: 'Restores health instantly',
      effects: { healthRestore: 50 }
    },
    'consumable_speed_boost': {
      id: 'consumable_speed_boost',
      name: 'Speed Boost',
      type: 'consumable',
      cost: { wood: 0, stone: 0, food: 0, gold: 300, points: 0 },
      unlockLevel: 2,
      priority: 5,
      description: 'Temporary speed increase',
      effects: { speedBoost: 30, duration: 10000 }
    },
    
    // Buildings
    'building_mill': {
      id: 'building_mill',
      name: 'Windmill',
      type: 'building',
      cost: { wood: 5, stone: 5, food: 0, gold: 0, points: 0 },
      unlockLevel: 1,
      priority: 10,
      description: 'Generates resources automatically',
      effects: { resourceGeneration: 1 }
    },
    'building_spike': {
      id: 'building_spike',
      name: 'Spike',
      type: 'building',
      cost: { wood: 10, stone: 0, food: 0, gold: 0, points: 0 },
      unlockLevel: 1,
      priority: 7,
      description: 'Defensive structure that damages enemies',
      effects: { damage: 25 }
    }
  };
  
  constructor(gameApi: SploopGameAPI, config: ModConfig) {
    super();
    this.gameApi = gameApi;
    this.config = config;
    
    this.gameApi.on('stateUpdate', this.onGameStateUpdate.bind(this));
  }
  
  public start(): void {
    if (this.enabled) return;
    
    this.enabled = true;
    this.updateInterval = window.setInterval(() => {
      this.update();
    }, this.config.updateInterval * 2); // Run less frequently than other modules
    
    this.emit('started');
    console.log('[AutoBuy] Module started');
  }
  
  public stop(): void {
    if (!this.enabled) return;
    
    this.enabled = false;
    if (this.updateInterval) {
      clearInterval(this.updateInterval);
      this.updateInterval = null;
    }
    
    this.emit('stopped');
    console.log('[AutoBuy] Module stopped');
  }
  
  public updateConfig(config: ModConfig): void {
    this.config = config;
  }
  
  private onGameStateUpdate(): void {
    if (!this.enabled || !this.config.autoBuy.enabled) return;
    
    // Process any queued purchases
    this.processBuyQueue();
  }
  
  private update(): void {
    if (!this.enabled || !this.config.autoBuy.enabled || !this.gameApi.isInGame()) return;
    
    const now = Date.now();
    if (now - this.lastBuyCheck < 5000) return; // Check every 5 seconds
    
    this.lastBuyCheck = now;
    
    try {
      this.processAutoBuy();
    } catch (error) {
      console.error('[AutoBuy] Error in update:', error);
    }
  }
  
  private processAutoBuy(): void {
    const player = this.gameApi.getPlayer();
    if (!player) return;
    
    // Get available items to buy
    const availableItems = this.getAvailableItems(player);
    
    // Sort by priority (higher priority first)
    availableItems.sort((a, b) => b.priority - a.priority);
    
    // Try to buy the highest priority item we can afford
    for (const item of availableItems) {
      if (this.canAffordItem(player, item) && this.shouldBuyItem(player, item)) {
        this.queuePurchase(item.id);
        break; // Only buy one item per cycle
      }
    }
  }
  
  private getAvailableItems(player: Player): ShopItem[] {
    return Object.values(this.SHOP_ITEMS).filter(item => {
      // Check if player has unlocked this item
      if (player.level < item.unlockLevel) return false;
      
      // Check if item type is enabled in config
      switch (item.type) {
        case 'hat':
          return this.config.autoBuy.buyHats;
        case 'weapon':
          return this.config.autoBuy.buyWeapons;
        case 'upgrade':
          return this.config.autoBuy.buyUpgrades;
        default:
          return true;
      }
    });
  }
  
  private canAffordItem(player: Player, item: ShopItem): boolean {
    const reserves = this.config.autoBuy.reserveResources;
    
    return (
      player.resources.wood >= item.cost.wood + reserves.wood &&
      player.resources.stone >= item.cost.stone + reserves.stone &&
      player.resources.food >= item.cost.food + reserves.food &&
      player.resources.gold >= item.cost.gold + reserves.gold
    );
  }
  
  private shouldBuyItem(player: Player, item: ShopItem): boolean {
    // Check if item is in priority list
    if (this.config.autoBuy.priorityItems.length > 0) {
      if (!this.config.autoBuy.priorityItems.includes(item.id)) {
        return false;
      }
    }
    
    // Check spending limits
    const totalResources = Object.values(player.resources).reduce((sum, val) => sum + val, 0);
    const itemCost = Object.values(item.cost).reduce((sum, val) => sum + val, 0);
    const spendPercentage = (itemCost / totalResources) * 100;
    
    if (spendPercentage > this.config.autoBuy.maxSpendPercentage) {
      return false;
    }
    
    // Check if we already own this item (for non-consumables)
    if (item.type !== 'consumable') {
      const alreadyOwned = player.inventory.some(invItem => invItem.id === item.id);
      if (alreadyOwned) return false;
    }
    
    // Additional logic for specific item types
    switch (item.type) {
      case 'consumable':
        return this.shouldBuyConsumable(player, item);
      case 'weapon':
        return this.shouldBuyWeapon(player, item);
      case 'upgrade':
        return this.shouldBuyUpgrade(player, item);
      default:
        return true;
    }
  }
  
  private shouldBuyConsumable(player: Player, item: ShopItem): boolean {
    // Buy health potions if health is low
    if (item.id === 'consumable_health_potion') {
      return player.health < player.maxHealth * 0.7;
    }
    
    // Buy speed boosts occasionally
    if (item.id === 'consumable_speed_boost') {
      return Math.random() < 0.1; // 10% chance
    }
    
    return true;
  }
  
  private shouldBuyWeapon(player: Player, item: ShopItem): boolean {
    // Only buy if it's better than current weapon
    if (!player.weapon) return true;
    
    const currentDamage = player.weapon.damage;
    const newDamage = item.effects.damage || 0;
    
    return newDamage > currentDamage;
  }
  
  private shouldBuyUpgrade(player: Player, item: ShopItem): boolean {
    // Always buy upgrades if we can afford them
    return true;
  }
  
  private queuePurchase(itemId: string): void {
    if (!this.buyQueue.includes(itemId)) {
      this.buyQueue.push(itemId);
    }
  }
  
  private processBuyQueue(): void {
    if (this.buyQueue.length === 0) return;
    
    const itemId = this.buyQueue.shift();
    if (!itemId) return;
    
    const item = this.SHOP_ITEMS[itemId];
    if (!item) return;
    
    const player = this.gameApi.getPlayer();
    if (!player || !this.canAffordItem(player, item)) {
      return; // Skip this purchase
    }
    
    // Execute the purchase
    this.gameApi.sendAction({
      type: ActionType.BUY,
      data: { itemId: item.id },
      timestamp: Date.now()
    });
    
    // Record the purchase
    const purchase: PurchaseHistory = {
      itemId: item.id,
      itemName: item.name,
      cost: { ...item.cost },
      timestamp: Date.now(),
      success: true // We'll assume success for now
    };
    
    this.purchaseHistory.push(purchase);
    
    // Keep only last 50 purchases
    if (this.purchaseHistory.length > 50) {
      this.purchaseHistory = this.purchaseHistory.slice(-50);
    }
    
    this.emit('itemPurchased', purchase);
    console.log(`[AutoBuy] Purchased ${item.name} for`, item.cost);
  }
  
  public getShopItems(): ShopItem[] {
    return Object.values(this.SHOP_ITEMS).map(item => ({ ...item }));
  }
  
  public getPurchaseHistory(): PurchaseHistory[] {
    return [...this.purchaseHistory];
  }
  
  public clearPurchaseHistory(): void {
    this.purchaseHistory = [];
  }
  
  public addCustomItem(item: ShopItem): void {
    this.SHOP_ITEMS[item.id] = { ...item };
  }
  
  public getStats(): any {
    const totalPurchases = this.purchaseHistory.length;
    const totalSpent = this.purchaseHistory.reduce((sum, purchase) => {
      return sum + Object.values(purchase.cost).reduce((costSum, val) => costSum + val, 0);
    }, 0);
    
    const recentPurchases = this.purchaseHistory.filter(p => 
      Date.now() - p.timestamp < 300000 // Last 5 minutes
    ).length;
    
    return {
      enabled: this.enabled,
      totalPurchases,
      totalSpent,
      recentPurchases,
      queueLength: this.buyQueue.length,
      availableItems: Object.keys(this.SHOP_ITEMS).length
    };
  }
}
