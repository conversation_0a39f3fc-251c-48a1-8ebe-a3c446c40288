import { SploopGameAPI } from '@/utils/gameApi';
import { ModConfig } from '@/config';
import { Player, Vector2, ActionType, ResourceType } from '@/types/game';
import { EventEmitter } from 'eventemitter3';

export interface BotInstance {
  id: string;
  name: string;
  position: Vector2;
  target: Vector2 | null;
  behavior: BotBehavior;
  state: BotState;
  lastAction: number;
  resources: { wood: number; stone: number; food: number; gold: number };
  health: number;
  level: number;
  task: BotTask | null;
}

export interface BotTask {
  type: 'farm' | 'build' | 'explore' | 'defend' | 'collect';
  target: Vector2;
  priority: number;
  startTime: number;
  estimatedDuration: number;
}

export enum BotBehavior {
  FARMING = 'farming',
  COMBAT = 'combat',
  BUILDING = 'building',
  EXPLORATION = 'exploration',
  PROTECTION = 'protection',
  IDLE = 'idle'
}

export enum BotState {
  SPAWNING = 'spawning',
  MOVING = 'moving',
  WORKING = 'working',
  FIGHTING = 'fighting',
  FLEEING = 'fleeing',
  DEAD = 'dead',
  IDLE = 'idle'
}

export class BotSystemModule extends EventEmitter {
  private gameApi: SploopGameAPI;
  private config: ModConfig;
  private enabled = false;
  private updateInterval: number | null = null;
  private bots: Map<string, BotInstance> = new Map();
  private taskQueue: BotTask[] = [];
  private lastBotSpawn = 0;
  private coordinationData = {
    sharedResources: { wood: 0, stone: 0, food: 0, gold: 0 },
    groupTarget: null as Vector2 | null,
    threatLevel: 0
  };
  
  constructor(gameApi: SploopGameAPI, config: ModConfig) {
    super();
    this.gameApi = gameApi;
    this.config = config;
    
    this.gameApi.on('stateUpdate', this.onGameStateUpdate.bind(this));
  }
  
  public start(): void {
    if (this.enabled) return;
    
    this.enabled = true;
    this.updateInterval = window.setInterval(() => {
      this.update();
    }, this.config.updateInterval);
    
    this.emit('started');
    console.log('[BotSystem] Module started');
  }
  
  public stop(): void {
    if (!this.enabled) return;
    
    this.enabled = false;
    if (this.updateInterval) {
      clearInterval(this.updateInterval);
      this.updateInterval = null;
    }
    
    // Despawn all bots
    this.bots.clear();
    
    this.emit('stopped');
    console.log('[BotSystem] Module stopped');
  }
  
  public updateConfig(config: ModConfig): void {
    this.config = config;
  }
  
  private onGameStateUpdate(): void {
    if (!this.enabled || !this.config.botSystem.enabled) return;
    
    this.updateBotStates();
    this.updateCoordination();
  }
  
  private update(): void {
    if (!this.enabled || !this.config.botSystem.enabled || !this.gameApi.isInGame()) return;
    
    try {
      this.manageBotSpawning();
      this.processBotActions();
      this.assignTasks();
      this.updateBotBehaviors();
    } catch (error) {
      console.error('[BotSystem] Error in update:', error);
    }
  }
  
  private manageBotSpawning(): void {
    const now = Date.now();
    const currentBotCount = this.bots.size;
    
    // Spawn new bots if needed
    if (currentBotCount < this.config.botSystem.maxBots && 
        now - this.lastBotSpawn > 10000) { // 10 second cooldown
      
      this.spawnBot();
      this.lastBotSpawn = now;
    }
    
    // Remove dead bots
    for (const [botId, bot] of this.bots.entries()) {
      if (bot.state === BotState.DEAD) {
        this.bots.delete(botId);
        this.emit('botDespawned', { botId, reason: 'death' });
      }
    }
  }
  
  private spawnBot(): void {
    const player = this.gameApi.getPlayer();
    if (!player) return;
    
    const botId = `bot_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    const spawnPosition = this.findSafeSpawnPosition(player.position);
    
    const bot: BotInstance = {
      id: botId,
      name: `Bot_${this.bots.size + 1}`,
      position: spawnPosition,
      target: null,
      behavior: this.selectBotBehavior(),
      state: BotState.SPAWNING,
      lastAction: Date.now(),
      resources: { wood: 0, stone: 0, food: 0, gold: 0 },
      health: 100,
      level: 1,
      task: null
    };
    
    this.bots.set(botId, bot);
    this.emit('botSpawned', bot);
    console.log(`[BotSystem] Spawned bot ${bot.name} with behavior ${bot.behavior}`);
  }
  
  private findSafeSpawnPosition(playerPos: Vector2): Vector2 {
    // Spawn bots near the player but not too close
    const angle = Math.random() * 2 * Math.PI;
    const distance = 100 + Math.random() * 100; // 100-200 units away
    
    return {
      x: playerPos.x + Math.cos(angle) * distance,
      y: playerPos.y + Math.sin(angle) * distance
    };
  }
  
  private selectBotBehavior(): BotBehavior {
    const behaviors = this.config.botSystem.botBehaviors;
    const availableBehaviors: BotBehavior[] = [];
    
    if (behaviors.farming) availableBehaviors.push(BotBehavior.FARMING);
    if (behaviors.combat) availableBehaviors.push(BotBehavior.COMBAT);
    if (behaviors.building) availableBehaviors.push(BotBehavior.BUILDING);
    if (behaviors.exploration) availableBehaviors.push(BotBehavior.EXPLORATION);
    if (behaviors.protection) availableBehaviors.push(BotBehavior.PROTECTION);
    
    if (availableBehaviors.length === 0) {
      return BotBehavior.IDLE;
    }
    
    return availableBehaviors[Math.floor(Math.random() * availableBehaviors.length)];
  }
  
  private updateBotStates(): void {
    const players = this.gameApi.getPlayers();
    
    for (const bot of this.bots.values()) {
      // Check for nearby threats
      const nearbyEnemies = Array.from(players.values()).filter(p => 
        !p.isBot && 
        this.gameApi.calculateDistance(bot.position, p.position) < 200
      );
      
      // Update bot state based on situation
      if (nearbyEnemies.length > 0 && this.config.botSystem.intelligence.avoidPlayers) {
        if (bot.behavior !== BotBehavior.COMBAT) {
          bot.state = BotState.FLEEING;
        } else {
          bot.state = BotState.FIGHTING;
        }
      } else if (bot.task) {
        bot.state = BotState.WORKING;
      } else {
        bot.state = BotState.IDLE;
      }
    }
  }
  
  private updateCoordination(): void {
    if (!this.config.botSystem.coordination.shareResources) return;
    
    // Calculate shared resources
    this.coordinationData.sharedResources = { wood: 0, stone: 0, food: 0, gold: 0 };
    
    for (const bot of this.bots.values()) {
      this.coordinationData.sharedResources.wood += bot.resources.wood;
      this.coordinationData.sharedResources.stone += bot.resources.stone;
      this.coordinationData.sharedResources.food += bot.resources.food;
      this.coordinationData.sharedResources.gold += bot.resources.gold;
    }
    
    // Update threat level
    const players = this.gameApi.getPlayers();
    const nearbyEnemies = Array.from(players.values()).filter(p => {
      if (p.isBot) return false;
      
      for (const bot of this.bots.values()) {
        if (this.gameApi.calculateDistance(bot.position, p.position) < 300) {
          return true;
        }
      }
      return false;
    });
    
    this.coordinationData.threatLevel = nearbyEnemies.length;
  }
  
  private assignTasks(): void {
    // Generate new tasks based on bot behaviors
    for (const bot of this.bots.values()) {
      if (bot.task && Date.now() - bot.task.startTime < bot.task.estimatedDuration) {
        continue; // Bot is still working on current task
      }
      
      bot.task = this.generateTaskForBot(bot);
    }
  }
  
  private generateTaskForBot(bot: BotInstance): BotTask | null {
    switch (bot.behavior) {
      case BotBehavior.FARMING:
        return this.generateFarmingTask(bot);
      case BotBehavior.BUILDING:
        return this.generateBuildingTask(bot);
      case BotBehavior.EXPLORATION:
        return this.generateExplorationTask(bot);
      case BotBehavior.PROTECTION:
        return this.generateProtectionTask(bot);
      default:
        return null;
    }
  }
  
  private generateFarmingTask(bot: BotInstance): BotTask {
    // Find nearest resource node
    const resourceTypes = [ResourceType.WOOD, ResourceType.STONE, ResourceType.FOOD];
    const targetResource = resourceTypes[Math.floor(Math.random() * resourceTypes.length)];
    
    // Generate a position near a resource (simplified)
    const angle = Math.random() * 2 * Math.PI;
    const distance = 50 + Math.random() * 100;
    const target = {
      x: bot.position.x + Math.cos(angle) * distance,
      y: bot.position.y + Math.sin(angle) * distance
    };
    
    return {
      type: 'farm',
      target,
      priority: 5,
      startTime: Date.now(),
      estimatedDuration: 15000 // 15 seconds
    };
  }
  
  private generateBuildingTask(bot: BotInstance): BotTask {
    // Find a good position to build
    const angle = Math.random() * 2 * Math.PI;
    const distance = 100 + Math.random() * 150;
    const target = {
      x: bot.position.x + Math.cos(angle) * distance,
      y: bot.position.y + Math.sin(angle) * distance
    };
    
    return {
      type: 'build',
      target,
      priority: 6,
      startTime: Date.now(),
      estimatedDuration: 20000 // 20 seconds
    };
  }
  
  private generateExplorationTask(bot: BotInstance): BotTask {
    // Generate random exploration target
    const angle = Math.random() * 2 * Math.PI;
    const distance = 200 + Math.random() * 300;
    const target = {
      x: bot.position.x + Math.cos(angle) * distance,
      y: bot.position.y + Math.sin(angle) * distance
    };
    
    return {
      type: 'explore',
      target,
      priority: 3,
      startTime: Date.now(),
      estimatedDuration: 25000 // 25 seconds
    };
  }
  
  private generateProtectionTask(bot: BotInstance): BotTask {
    const player = this.gameApi.getPlayer();
    if (!player) return this.generateExplorationTask(bot);
    
    // Protect the main player
    return {
      type: 'defend',
      target: player.position,
      priority: 8,
      startTime: Date.now(),
      estimatedDuration: 30000 // 30 seconds
    };
  }
  
  private processBotActions(): void {
    const now = Date.now();
    
    for (const bot of this.bots.values()) {
      if (now - bot.lastAction < 500) continue; // Rate limiting
      
      this.executeBotAction(bot);
      bot.lastAction = now;
    }
  }
  
  private executeBotAction(bot: BotInstance): void {
    if (!bot.task) return;
    
    switch (bot.state) {
      case BotState.MOVING:
        this.moveBotToTarget(bot);
        break;
      case BotState.WORKING:
        this.executeBotTask(bot);
        break;
      case BotState.FLEEING:
        this.fleeFromDanger(bot);
        break;
      case BotState.FIGHTING:
        this.engageInCombat(bot);
        break;
    }
  }
  
  private moveBotToTarget(bot: BotInstance): void {
    if (!bot.task) return;
    
    const distance = this.gameApi.calculateDistance(bot.position, bot.task.target);
    
    if (distance < 50) {
      bot.state = BotState.WORKING;
      return;
    }
    
    // Move towards target
    const angle = this.gameApi.calculateAngle(bot.position, bot.task.target);
    const speed = 2; // Bot movement speed
    
    bot.position.x += Math.cos(angle) * speed;
    bot.position.y += Math.sin(angle) * speed;
    
    // Send movement action (this would be sent to the game)
    this.gameApi.sendAction({
      type: ActionType.MOVE,
      data: { x: bot.position.x, y: bot.position.y },
      timestamp: Date.now()
    });
  }
  
  private executeBotTask(bot: BotInstance): void {
    if (!bot.task) return;
    
    switch (bot.task.type) {
      case 'farm':
        this.executeFarmingAction(bot);
        break;
      case 'build':
        this.executeBuildingAction(bot);
        break;
      case 'explore':
        this.executeExplorationAction(bot);
        break;
      case 'defend':
        this.executeDefenseAction(bot);
        break;
    }
  }
  
  private executeFarmingAction(bot: BotInstance): void {
    // Simulate resource gathering
    const resourceGain = Math.floor(Math.random() * 5) + 1;
    const resourceType = ['wood', 'stone', 'food'][Math.floor(Math.random() * 3)] as keyof typeof bot.resources;
    
    bot.resources[resourceType] += resourceGain;
    
    this.emit('botAction', { 
      botId: bot.id, 
      action: 'farm', 
      result: { [resourceType]: resourceGain } 
    });
  }
  
  private executeBuildingAction(bot: BotInstance): void {
    // Simulate building placement
    if (bot.resources.wood >= 5 && bot.resources.stone >= 5) {
      bot.resources.wood -= 5;
      bot.resources.stone -= 5;
      
      this.gameApi.sendAction({
        type: ActionType.BUILD,
        data: {
          type: 'mill',
          x: bot.task!.target.x,
          y: bot.task!.target.y
        },
        timestamp: Date.now()
      });
      
      this.emit('botAction', { 
        botId: bot.id, 
        action: 'build', 
        result: { building: 'mill' } 
      });
    }
  }
  
  private executeExplorationAction(bot: BotInstance): void {
    // Exploration increases bot level/experience
    bot.level += 0.1;
    
    this.emit('botAction', { 
      botId: bot.id, 
      action: 'explore', 
      result: { experience: 0.1 } 
    });
  }
  
  private executeDefenseAction(bot: BotInstance): void {
    // Stay near the target and be ready to defend
    const player = this.gameApi.getPlayer();
    if (player) {
      bot.task!.target = player.position;
    }
  }
  
  private fleeFromDanger(bot: BotInstance): void {
    const players = this.gameApi.getPlayers();
    
    // Find nearest enemy
    let nearestEnemy: Player | null = null;
    let minDistance = Infinity;
    
    for (const player of players.values()) {
      if (!player.isBot) {
        const distance = this.gameApi.calculateDistance(bot.position, player.position);
        if (distance < minDistance) {
          minDistance = distance;
          nearestEnemy = player;
        }
      }
    }
    
    if (nearestEnemy) {
      // Move away from enemy
      const angle = this.gameApi.calculateAngle(nearestEnemy.position, bot.position);
      const speed = 3; // Faster when fleeing
      
      bot.position.x += Math.cos(angle) * speed;
      bot.position.y += Math.sin(angle) * speed;
    }
  }
  
  private engageInCombat(bot: BotInstance): void {
    // Simple combat AI - attack nearest enemy
    const players = this.gameApi.getPlayers();
    
    for (const player of players.values()) {
      if (!player.isBot) {
        const distance = this.gameApi.calculateDistance(bot.position, player.position);
        if (distance < 100) {
          const angle = this.gameApi.calculateAngle(bot.position, player.position);
          
          this.gameApi.sendAction({
            type: ActionType.ATTACK,
            data: { angle },
            timestamp: Date.now()
          });
          
          break;
        }
      }
    }
  }
  
  private updateBotBehaviors(): void {
    if (!this.config.botSystem.intelligence.adaptToSituation) return;
    
    // Adapt bot behaviors based on current situation
    for (const bot of this.bots.values()) {
      if (this.coordinationData.threatLevel > 2) {
        // High threat - switch to defensive behaviors
        if (bot.behavior === BotBehavior.FARMING || bot.behavior === BotBehavior.EXPLORATION) {
          bot.behavior = BotBehavior.PROTECTION;
        }
      } else if (this.coordinationData.threatLevel === 0) {
        // No threats - focus on resource gathering
        if (bot.behavior === BotBehavior.PROTECTION) {
          bot.behavior = BotBehavior.FARMING;
        }
      }
    }
  }
  
  public getBots(): BotInstance[] {
    return Array.from(this.bots.values());
  }
  
  public getBotById(botId: string): BotInstance | null {
    return this.bots.get(botId) || null;
  }
  
  public getCoordinationData(): any {
    return { ...this.coordinationData };
  }
  
  public getStats(): any {
    const totalBots = this.bots.size;
    const activeBots = Array.from(this.bots.values()).filter(b => b.state !== BotState.DEAD).length;
    const behaviorCounts = {};
    
    for (const bot of this.bots.values()) {
      behaviorCounts[bot.behavior] = (behaviorCounts[bot.behavior] || 0) + 1;
    }
    
    return {
      enabled: this.enabled,
      totalBots,
      activeBots,
      maxBots: this.config.botSystem.maxBots,
      behaviorCounts,
      threatLevel: this.coordinationData.threatLevel,
      sharedResources: this.coordinationData.sharedResources
    };
  }
}
