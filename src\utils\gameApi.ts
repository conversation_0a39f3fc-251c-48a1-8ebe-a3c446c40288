import { Game<PERSON><PERSON>, GameState, Player, Building, GameAction, ActionType, Vector2 } from '@/types/game';
import { EventEmitter } from 'eventemitter3';

declare global {
  interface Window {
    game: any;
    io: any;
    player: any;
    players: any;
    buildings: any;
    items: any;
    socket: any;
  }
}

export class SploopGameAPI extends EventEmitter<string | symbol, any> implements GameAPI {
  private gameState: GameState | null = null;
  private updateInterval: number | null = null;
  private lastActionTime = 0;
  private actionQueue: GameAction[] = [];
  
  constructor() {
    super();
    this.initialize();
  }
  
  private initialize(): void {
    // Wait for game to load
    const checkGame = () => {
      if (this.isGameLoaded()) {
        this.startMonitoring();
        super.emit('gameLoaded');
      } else {
        setTimeout(checkGame, 1000);
      }
    };
    checkGame();
  }
  
  private isGameLoaded(): boolean {
    return typeof window !== 'undefined' && 
           window.game && 
           window.player && 
           window.socket;
  }
  
  private startMonitoring(): void {
    if (this.updateInterval) {
      clearInterval(this.updateInterval);
    }
    
    this.updateInterval = window.setInterval(() => {
      this.updateGameState();
      this.processActionQueue();
    }, 50);
  }
  
  private updateGameState(): void {
    if (!this.isGameLoaded()) return;
    
    try {
      const newState: GameState = {
        player: this.parsePlayer(window.player),
        players: this.parsePlayers(window.players),
        buildings: this.parseBuildings(window.buildings),
        items: new Map(),
        gameObjects: new Map(),
        isInGame: this.isInGame(),
        gameMode: window.game?.gameMode || 'unknown',
        serverInfo: {
          region: window.game?.region || 'unknown',
          playerCount: window.players?.length || 0,
          maxPlayers: window.game?.maxPlayers || 0,
          gameMode: window.game?.gameMode || 'unknown',
          mapSize: window.game?.mapSize || 0
        }
      };
      
      this.gameState = newState;
      super.emit('stateUpdate', newState);
    } catch (error) {
      console.error('Error updating game state:', error);
    }
  }
  
  private parsePlayer(playerData: any): Player {
    return {
      id: playerData?.id || '',
      name: playerData?.name || '',
      position: { x: playerData?.x || 0, y: playerData?.y || 0 },
      health: playerData?.health || 0,
      maxHealth: playerData?.maxHealth || 100,
      resources: {
        wood: playerData?.wood || 0,
        stone: playerData?.stone || 0,
        food: playerData?.food || 0,
        gold: playerData?.gold || 0,
        points: playerData?.points || 0
      },
      inventory: playerData?.inventory || [],
      hat: playerData?.hat || null,
      weapon: playerData?.weapon || null,
      level: playerData?.level || 1,
      experience: playerData?.experience || 0,
      clan: playerData?.clan || null,
      isBot: false
    };
  }
  
  private parsePlayers(playersData: any): Map<string, Player> {
    const players = new Map<string, Player>();
    
    if (Array.isArray(playersData)) {
      playersData.forEach((playerData: any) => {
        const player = this.parsePlayer(playerData);
        players.set(player.id, player);
      });
    }
    
    return players;
  }
  
  private parseBuildings(buildingsData: any): Map<string, Building> {
    const buildings = new Map<string, Building>();
    
    if (Array.isArray(buildingsData)) {
      buildingsData.forEach((buildingData: any) => {
        const building: Building = {
          id: buildingData?.id || '',
          type: buildingData?.type || 'unknown',
          position: { x: buildingData?.x || 0, y: buildingData?.y || 0 },
          health: buildingData?.health || 0,
          maxHealth: buildingData?.maxHealth || 100,
          owner: buildingData?.owner || '',
          level: buildingData?.level || 1,
          isActive: buildingData?.isActive || false
        };
        buildings.set(building.id, building);
      });
    }
    
    return buildings;
  }
  
  public getPlayer(): Player | null {
    return this.gameState?.player || null;
  }
  
  public getPlayers(): Map<string, Player> {
    return this.gameState?.players || new Map();
  }
  
  public getBuildings(): Map<string, Building> {
    return this.gameState?.buildings || new Map();
  }
  
  public getGameState(): GameState {
    return this.gameState || {
      player: {} as Player,
      players: new Map(),
      buildings: new Map(),
      items: new Map(),
      gameObjects: new Map(),
      isInGame: false,
      gameMode: 'unknown',
      serverInfo: {
        region: 'unknown',
        playerCount: 0,
        maxPlayers: 0,
        gameMode: 'unknown',
        mapSize: 0
      }
    };
  }
  
  public sendAction(action: GameAction): void {
    this.actionQueue.push(action);
  }
  
  private processActionQueue(): void {
    if (this.actionQueue.length === 0) return;
    
    const now = Date.now();
    if (now - this.lastActionTime < 200) return; // Rate limiting
    
    const action = this.actionQueue.shift();
    if (!action) return;
    
    try {
      this.executeAction(action);
      this.lastActionTime = now;
    } catch (error) {
      console.error('Error executing action:', error);
    }
  }
  
  private executeAction(action: GameAction): void {
    if (!this.isGameLoaded() || !window.socket) return;
    
    switch (action.type) {
      case ActionType.MOVE:
        this.move(action.data.x, action.data.y);
        break;
      case ActionType.ATTACK:
        this.attack(action.data.angle);
        break;
      case ActionType.BUILD:
        this.build(action.data.type, action.data.x, action.data.y);
        break;
      case ActionType.BUY:
        this.buy(action.data.itemId);
        break;
      case ActionType.EQUIP:
        this.equip(action.data.itemId);
        break;
      case ActionType.CHAT:
        this.sendChat(action.data.message);
        break;
      default:
        console.warn('Unknown action type:', action.type);
    }
  }
  
  private move(x: number, y: number): void {
    if (window.socket && window.socket.emit) {
      window.socket.emit('move', { x, y });
    }
  }
  
  private attack(angle: number): void {
    if (window.socket && window.socket.emit) {
      window.socket.emit('attack', { angle });
    }
  }
  
  private build(type: string, x: number, y: number): void {
    if (window.socket && window.socket.emit) {
      window.socket.emit('build', { type, x, y });
    }
  }
  
  private buy(itemId: string): void {
    if (window.socket && window.socket.emit) {
      window.socket.emit('buy', { itemId });
    }
  }
  
  private equip(itemId: string): void {
    if (window.socket && window.socket.emit) {
      window.socket.emit('equip', { itemId });
    }
  }
  
  public sendChat(message: string): void {
    if (window.socket && window.socket.emit) {
      window.socket.emit('chat', { message });
    }
  }
  
  public isInGame(): boolean {
    return this.isGameLoaded() && 
           window.game?.inGame === true && 
           this.gameState?.player?.id !== '';
  }
  
  public calculateDistance(pos1: Vector2, pos2: Vector2): number {
    const dx = pos1.x - pos2.x;
    const dy = pos1.y - pos2.y;
    return Math.sqrt(dx * dx + dy * dy);
  }
  
  public calculateAngle(from: Vector2, to: Vector2): number {
    return Math.atan2(to.y - from.y, to.x - from.x);
  }
  
  public destroy(): void {
    if (this.updateInterval) {
      clearInterval(this.updateInterval);
      this.updateInterval = null;
    } 
    super.removeAllListeners();
  }
}
