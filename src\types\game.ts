// Sploop.io Game Type Definitions

export interface Vector2 {
  x: number;
  y: number;
}

export interface Player {
  id: string;
  name: string;
  position: Vector2;
  health: number;
  maxHealth: number;
  resources: Resources;
  inventory: InventoryItem[];
  hat: Hat | null;
  weapon: Weapon | null;
  level: number;
  experience: number;
  clan: string | null;
  isBot: boolean;
}

export interface Resources {
  wood: number;
  stone: number;
  food: number;
  gold: number;
  points: number;
}

export interface InventoryItem {
  id: string;
  type: ItemType;
  quantity: number;
  durability?: number;
}

export interface Hat {
  id: string;
  name: string;
  type: HatType;
  stats: HatStats;
  cost: number;
  unlockLevel: number;
}

export interface HatStats {
  healthBonus?: number;
  speedBonus?: number;
  damageBonus?: number;
  resourceBonus?: number;
  experienceBonus?: number;
}

export interface Weapon {
  id: string;
  name: string;
  damage: number;
  range: number;
  speed: number;
  cost: number;
}

export interface Building {
  id: string;
  type: BuildingType;
  position: Vector2;
  health: number;
  maxHealth: number;
  owner: string;
  level: number;
  isActive: boolean;
}

export interface Mill extends Building {
  resourceType: ResourceType;
  productionRate: number;
  storage: number;
  maxStorage: number;
}

export enum ItemType {
  WEAPON = 'weapon',
  HAT = 'hat',
  FOOD = 'food',
  MATERIAL = 'material',
  TOOL = 'tool'
}

export enum HatType {
  NONE = 'none',
  MARKSMAN = 'marksman',
  BUSH = 'bush',
  BERSERKER = 'berserker',
  JUNGLE = 'jungle',
  CRYSTAL = 'crystal',
  SPACE = 'space',
  CYBORG = 'cyborg',
  MONKEY = 'monkey',
  ELF = 'elf',
  KNIGHT = 'knight',
  SAMURAI = 'samurai',
  ANGEL = 'angel',
  DEVIL = 'devil'
}

export enum BuildingType {
  MILL = 'mill',
  SPIKE = 'spike',
  WALL = 'wall',
  WINDMILL = 'windmill',
  MINE = 'mine',
  PIT_TRAP = 'pit_trap',
  BOOST_PAD = 'boost_pad',
  TURRET = 'turret',
  PLATFORM = 'platform',
  HEALING_PAD = 'healing_pad',
  SPAWN_PAD = 'spawn_pad',
  BLOCKER = 'blocker',
  TELEPORTER = 'teleporter'
}

export enum ResourceType {
  WOOD = 'wood',
  STONE = 'stone',
  FOOD = 'food',
  GOLD = 'gold'
}

export interface GameState {
  player: Player;
  players: Map<string, Player>;
  buildings: Map<string, Building>;
  items: Map<string, InventoryItem>;
  gameObjects: Map<string, GameObject>;
  isInGame: boolean;
  gameMode: string;
  serverInfo: ServerInfo;
}

export interface GameObject {
  id: string;
  type: string;
  position: Vector2;
  angle: number;
  scale: number;
  visible: boolean;
}

export interface ServerInfo {
  region: string;
  playerCount: number;
  maxPlayers: number;
  gameMode: string;
  mapSize: number;
}

// Game API interfaces
export interface GameAPI {
  getPlayer(): Player | null;
  getPlayers(): Map<string, Player>;
  getBuildings(): Map<string, Building>;
  getGameState(): GameState;
  sendAction(action: GameAction): void;
  sendChat(message: string): void;
  isInGame(): boolean;
}

export interface GameAction {
  type: ActionType;
  data: any;
  timestamp: number;
}

export enum ActionType {
  MOVE = 'move',
  ATTACK = 'attack',
  BUILD = 'build',
  UPGRADE = 'upgrade',
  BUY = 'buy',
  EQUIP = 'equip',
  USE_ITEM = 'use_item',
  CHAT = 'chat',
  JOIN_CLAN = 'join_clan',
  LEAVE_CLAN = 'leave_clan'
}
