import { SploopGameAPI } from '@/utils/gameApi';
import { ModConfig } from '@/config';
import { Player, Building, Mill, ActionType, Vector2, ResourceType, BuildingType } from '@/types/game';
import { EventEmitter } from 'eventemitter3';

export interface MillPlacement {
  position: Vector2;
  resourceType: ResourceType;
  priority: number;
  safety: number;
  efficiency: number;
}

export interface MillStats {
  id: string;
  position: Vector2;
  resourceType: ResourceType;
  level: number;
  production: number;
  storage: number;
  maxStorage: number;
  lastUpgrade: number;
  isProtected: boolean;
}

export class AutoMillsModule extends EventEmitter {
  private gameApi: SploopGameAPI;
  private config: ModConfig;
  private enabled = false;
  private updateInterval: number | null = null;
  private lastMillCheck = 0;
  private lastPlacementCheck = 0;
  private ownedMills: Map<string, MillStats> = new Map();
  private resourceNodes: Map<string, Vector2> = new Map();
  private dangerZones: Vector2[] = [];
  
  // Mill costs and stats
  private readonly MILL_COSTS = {
    [ResourceType.WOOD]: { wood: 5, stone: 5, food: 0, gold: 0 },
    [ResourceType.STONE]: { wood: 5, stone: 5, food: 0, gold: 0 },
    [ResourceType.FOOD]: { wood: 10, stone: 0, food: 0, gold: 0 },
    [ResourceType.GOLD]: { wood: 20, stone: 10, food: 0, gold: 0 }
  };
  
  private readonly UPGRADE_COSTS: Record<number, { wood: number; stone: number; food: number; gold: number }> = {
    1: { wood: 15, stone: 15, food: 0, gold: 0 },
    2: { wood: 25, stone: 25, food: 0, gold: 0 },
    3: { wood: 35, stone: 35, food: 0, gold: 0 }
  };
  
  constructor(gameApi: SploopGameAPI, config: ModConfig) {
    super();
    this.gameApi = gameApi;
    this.config = config;
    
    this.gameApi.on('stateUpdate', this.onGameStateUpdate.bind(this));
  }
  
  public start(): void {
    if (this.enabled) return;
    
    this.enabled = true;
    this.updateInterval = window.setInterval(() => {
      this.update();
    }, this.config.updateInterval);
    
    this.emit('started');
    console.log('[AutoMills] Module started');
  }
  
  public stop(): void {
    if (!this.enabled) return;
    
    this.enabled = false;
    if (this.updateInterval) {
      clearInterval(this.updateInterval);
      this.updateInterval = null;
    }
    
    this.emit('stopped');
    console.log('[AutoMills] Module stopped');
  }
  
  public updateConfig(config: ModConfig): void {
    this.config = config;
  }
  
  private onGameStateUpdate(): void {
    if (!this.enabled || !this.config.autoMills.enabled) return;
    
    this.updateMillTracking();
    this.updateResourceNodes();
    this.updateDangerZones();
  }
  
  private update(): void {
    if (!this.enabled || !this.config.autoMills.enabled || !this.gameApi.isInGame()) return;
    
    const now = Date.now();
    
    // Check for mill upgrades every 3 seconds
    if (now - this.lastMillCheck > 3000) {
      this.lastMillCheck = now;
      this.processMillUpgrades();
    }
    
    // Check for new mill placements every 10 seconds
    if (now - this.lastPlacementCheck > 10000) {
      this.lastPlacementCheck = now;
      this.processMillPlacement();
    }
    
    // Protect mills if enabled
    if (this.config.autoMills.protectMills) {
      this.protectMills();
    }
  }
  
  private updateMillTracking(): void {
    const buildings = this.gameApi.getBuildings();
    const player = this.gameApi.getPlayer();
    
    if (!player) return;
    
    // Update owned mills
    this.ownedMills.clear();
    
    buildings.forEach((building, id) => {
      if (building.type === BuildingType.MILL && building.owner === player.id) {
        const mill = building as Mill;
        const millStats: MillStats = {
          id: mill.id,
          position: mill.position,
          resourceType: mill.resourceType,
          level: mill.level,
          production: mill.productionRate,
          storage: mill.storage,
          maxStorage: mill.maxStorage,
          lastUpgrade: 0, // We'll track this separately
          isProtected: this.isMillProtected(mill.position)
        };
        
        this.ownedMills.set(id, millStats);
      }
    });
  }
  
  private updateResourceNodes(): void {
    // In a real implementation, this would scan the map for resource nodes
    // For now, we'll use some example positions
    this.resourceNodes.clear();
    
    // These would be detected from the game map
    const exampleNodes = [
      { type: ResourceType.WOOD, pos: { x: 100, y: 100 } },
      { type: ResourceType.STONE, pos: { x: 200, y: 150 } },
      { type: ResourceType.FOOD, pos: { x: 150, y: 200 } },
      { type: ResourceType.GOLD, pos: { x: 300, y: 250 } }
    ];
    
    exampleNodes.forEach((node, index) => {
      this.resourceNodes.set(`${node.type}_${index}`, node.pos);
    });
  }
  
  private updateDangerZones(): void {
    const players = this.gameApi.getPlayers();
    const player = this.gameApi.getPlayer();
    
    if (!player) return;
    
    this.dangerZones = [];
    
    // Mark areas near enemy players as dangerous
    players.forEach(otherPlayer => {
      if (otherPlayer.id !== player.id) {
        this.dangerZones.push(otherPlayer.position);
      }
    });
  }
  
  private processMillPlacement(): void {
    const player = this.gameApi.getPlayer();
    if (!player) return;
    
    const currentMillCount = this.ownedMills.size;
    
    if (currentMillCount >= this.config.autoMills.maxMills) {
      return; // Already at max mills
    }
    
    // Find optimal placement for next mill
    const optimalPlacement = this.findOptimalMillPlacement();
    
    if (optimalPlacement && this.canAffordMill(player, optimalPlacement.resourceType)) {
      this.placeMill(optimalPlacement);
    }
  }
  
  private findOptimalMillPlacement(): MillPlacement | null {
    const player = this.gameApi.getPlayer();
    if (!player) return null;
    
    const placements: MillPlacement[] = [];
    
    // Evaluate potential positions around resource nodes
    this.resourceNodes.forEach((nodePos, nodeId) => {
      const resourceType = this.getResourceTypeFromNodeId(nodeId);
      
      // Generate positions around the resource node
      for (let angle = 0; angle < 360; angle += 45) {
        const distance = this.config.autoMills.millSpacing;
        const x = nodePos.x + Math.cos(angle * Math.PI / 180) * distance;
        const y = nodePos.y + Math.sin(angle * Math.PI / 180) * distance;
        const position = { x, y };
        
        if (this.isValidMillPosition(position)) {
          const placement: MillPlacement = {
            position,
            resourceType,
            priority: this.calculatePlacementPriority(resourceType),
            safety: this.calculateSafety(position),
            efficiency: this.calculateEfficiency(position, resourceType)
          };
          
          placements.push(placement);
        }
      }
    });
    
    // Sort by overall score (priority + safety + efficiency)
    placements.sort((a, b) => {
      const scoreA = a.priority + a.safety + a.efficiency;
      const scoreB = b.priority + b.safety + b.efficiency;
      return scoreB - scoreA;
    });
    
    return placements[0] || null;
  }
  
  private getResourceTypeFromNodeId(nodeId: string): ResourceType {
    if (nodeId.includes('wood')) return ResourceType.WOOD;
    if (nodeId.includes('stone')) return ResourceType.STONE;
    if (nodeId.includes('food')) return ResourceType.FOOD;
    if (nodeId.includes('gold')) return ResourceType.GOLD;
    return ResourceType.WOOD; // Default
  }
  
  private isValidMillPosition(position: Vector2): boolean {
    // Check if position is too close to existing mills
    for (const mill of this.ownedMills.values()) {
      const distance = this.gameApi.calculateDistance(position, mill.position);
      if (distance < this.config.autoMills.millSpacing) {
        return false;
      }
    }
    
    // Check if position is in a danger zone
    for (const dangerPos of this.dangerZones) {
      const distance = this.gameApi.calculateDistance(position, dangerPos);
      if (distance < 150) { // Too close to enemies
        return false;
      }
    }
    
    return true;
  }
  
  private calculatePlacementPriority(resourceType: ResourceType): number {
    const priorityIndex = this.config.autoMills.resourcePriority.indexOf(resourceType);
    return priorityIndex >= 0 ? (10 - priorityIndex) : 1;
  }
  
  private calculateSafety(position: Vector2): number {
    let safety = 10;
    
    // Reduce safety based on proximity to danger zones
    for (const dangerPos of this.dangerZones) {
      const distance = this.gameApi.calculateDistance(position, dangerPos);
      if (distance < 300) {
        safety -= (300 - distance) / 30;
      }
    }
    
    return Math.max(0, safety);
  }
  
  private calculateEfficiency(position: Vector2, resourceType: ResourceType): number {
    let efficiency = 5;
    
    // Increase efficiency based on proximity to resource nodes
    this.resourceNodes.forEach((nodePos, nodeId) => {
      if (this.getResourceTypeFromNodeId(nodeId) === resourceType) {
        const distance = this.gameApi.calculateDistance(position, nodePos);
        if (distance < 200) {
          efficiency += (200 - distance) / 20;
        }
      }
    });
    
    return efficiency;
  }
  
  private canAffordMill(player: Player, resourceType: ResourceType): boolean {
    const cost = this.MILL_COSTS[resourceType];
    
    return (
      player.resources.wood >= cost.wood &&
      player.resources.stone >= cost.stone &&
      player.resources.food >= cost.food &&
      player.resources.gold >= cost.gold
    );
  }
  
  private placeMill(placement: MillPlacement): void {
    this.gameApi.sendAction({
      type: ActionType.BUILD,
      data: {
        type: 'mill',
        x: placement.position.x,
        y: placement.position.y,
        resourceType: placement.resourceType
      },
      timestamp: Date.now()
    });
    
    this.emit('millPlaced', placement);
    console.log(`[AutoMills] Placed ${placement.resourceType} mill at (${placement.position.x}, ${placement.position.y})`);
  }
  
  private processMillUpgrades(): void {
    if (!this.config.autoMills.autoUpgrade) return;
    
    const player = this.gameApi.getPlayer();
    if (!player) return;
    
    // Find mills that can be upgraded
    for (const mill of this.ownedMills.values()) {
      if (mill.level < 4 && this.canAffordUpgrade(player, mill.level)) {
        this.upgradeMill(mill);
        break; // Only upgrade one mill per cycle
      }
    }
  }
  
  private canAffordUpgrade(player: Player, currentLevel: number): boolean {
    const cost = this.UPGRADE_COSTS[currentLevel];
    if (!cost) return false;
    
    return (
      player.resources.wood >= cost.wood &&
      player.resources.stone >= cost.stone &&
      player.resources.food >= cost.food &&
      player.resources.gold >= cost.gold
    );
  }
  
  private upgradeMill(mill: MillStats): void {
    this.gameApi.sendAction({
      type: ActionType.UPGRADE,
      data: { buildingId: mill.id },
      timestamp: Date.now()
    });
    
    this.emit('millUpgraded', mill);
    console.log(`[AutoMills] Upgraded mill ${mill.id} to level ${mill.level + 1}`);
  }
  
  private isMillProtected(position: Vector2): boolean {
    // Check if there are defensive structures nearby
    const buildings = this.gameApi.getBuildings();
    
    for (const building of buildings.values()) {
      if (building.type === BuildingType.SPIKE || building.type === BuildingType.WALL) {
        const distance = this.gameApi.calculateDistance(position, building.position);
        if (distance < 100) {
          return true;
        }
      }
    }
    
    return false;
  }
  
  private protectMills(): void {
    const player = this.gameApi.getPlayer();
    if (!player) return;
    
    // Find unprotected mills
    for (const mill of this.ownedMills.values()) {
      if (!mill.isProtected && this.canAffordSpike(player)) {
        this.placeProtection(mill);
        break; // Only protect one mill per cycle
      }
    }
  }
  
  private canAffordSpike(player: Player): boolean {
    return player.resources.wood >= 10;
  }
  
  private placeProtection(mill: MillStats): void {
    // Place spikes around the mill
    const angles = [0, 90, 180, 270];
    const distance = 80;
    
    for (const angle of angles) {
      const x = mill.position.x + Math.cos(angle * Math.PI / 180) * distance;
      const y = mill.position.y + Math.sin(angle * Math.PI / 180) * distance;
      
      this.gameApi.sendAction({
        type: ActionType.BUILD,
        data: {
          type: 'spike',
          x,
          y
        },
        timestamp: Date.now()
      });
    }
    
    this.emit('millProtected', mill);
    console.log(`[AutoMills] Protected mill ${mill.id} with spikes`);
  }
  
  public getOwnedMills(): MillStats[] {
    return Array.from(this.ownedMills.values());
  }
  
  public getStats(): any {
    const totalMills = this.ownedMills.size;
    const protectedMills = Array.from(this.ownedMills.values()).filter(m => m.isProtected).length;
    const totalProduction = Array.from(this.ownedMills.values()).reduce((sum, mill) => sum + mill.production, 0);
    
    return {
      enabled: this.enabled,
      totalMills,
      maxMills: this.config.autoMills.maxMills,
      protectedMills,
      totalProduction,
      resourceNodes: this.resourceNodes.size,
      dangerZones: this.dangerZones.length
    };
  }
}
