import { SploopGameAPI } from '@/utils/gameApi';
import { ModConfig } from '@/config';
import { Vector2 } from '@/types/game';
import { EventEmitter } from 'eventemitter3';
export interface BotInstance {
    id: string;
    name: string;
    position: Vector2;
    target: Vector2 | null;
    behavior: BotBehavior;
    state: BotState;
    lastAction: number;
    resources: {
        wood: number;
        stone: number;
        food: number;
        gold: number;
    };
    health: number;
    level: number;
    task: BotTask | null;
}
export interface BotTask {
    type: 'farm' | 'build' | 'explore' | 'defend' | 'collect';
    target: Vector2;
    priority: number;
    startTime: number;
    estimatedDuration: number;
}
export declare enum BotBehavior {
    FARMING = "farming",
    COMBAT = "combat",
    BUILDING = "building",
    EXPLORATION = "exploration",
    PROTECTION = "protection",
    IDLE = "idle"
}
export declare enum BotState {
    SPAWNING = "spawning",
    MOVING = "moving",
    WORKING = "working",
    FIGHTING = "fighting",
    FLEEING = "fleeing",
    DEAD = "dead",
    IDLE = "idle"
}
export declare class BotSystemModule extends EventEmitter {
    private gameApi;
    private config;
    private enabled;
    private updateInterval;
    private bots;
    private taskQueue;
    private lastBotSpawn;
    private coordinationData;
    constructor(gameApi: SploopGameAPI, config: ModConfig);
    start(): void;
    stop(): void;
    updateConfig(config: ModConfig): void;
    private onGameStateUpdate;
    private update;
    private manageBotSpawning;
    private spawnBot;
    private findSafeSpawnPosition;
    private selectBotBehavior;
    private updateBotStates;
    private updateCoordination;
    private assignTasks;
    private generateTaskForBot;
    private generateFarmingTask;
    private generateBuildingTask;
    private generateExplorationTask;
    private generateProtectionTask;
    private processBotActions;
    private executeBotAction;
    private moveBotToTarget;
    private executeBotTask;
    private executeFarmingAction;
    private executeBuildingAction;
    private executeExplorationAction;
    private executeDefenseAction;
    private fleeFromDanger;
    private engageInCombat;
    private updateBotBehaviors;
    getBots(): BotInstance[];
    getBotById(botId: string): BotInstance | null;
    getCoordinationData(): any;
    getStats(): any;
}
//# sourceMappingURL=botSystem.d.ts.map