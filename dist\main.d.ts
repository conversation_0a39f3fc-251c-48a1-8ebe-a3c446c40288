import { ModConfig } from '@/config';
import { EventEmitter } from 'eventemitter3';
interface ModuleStats {
    autoHats: any;
    autoBuy: any;
    autoMills: any;
    botSystem: any;
}
declare class SploopAdvancedMod extends EventEmitter {
    private gameApi;
    private configManager;
    private autoHats;
    private autoBuy;
    private autoMills;
    private botSystem;
    private ui;
    private isInitialized;
    private emergencyStopActive;
    constructor();
    private initialize;
    private waitForGameLoad;
    private setupEventListeners;
    private setupEmergencyStop;
    private emergencyStop;
    private startModules;
    private stopModules;
    private createUI;
    private updateUI;
    private renderStats;
    private getModuleStats;
    private logAction;
    private showNotification;
    getConfig(): ModConfig;
    updateConfig(updates: Partial<ModConfig>): void;
    resetConfig(): void;
    getStats(): ModuleStats;
    toggleModule(moduleName: 'autoHats' | 'autoBuy' | 'autoMills' | 'botSystem'): void;
    destroy(): void;
}
export default SploopAdvancedMod;
//# sourceMappingURL=main.d.ts.map