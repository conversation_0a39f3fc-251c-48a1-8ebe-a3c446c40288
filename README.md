# 🚀 Sploop.io Advanced Mod

An advanced, feature-rich mod for sploop.io built with TypeScript. This mod provides automated gameplay features including auto hats, auto purchasing, auto mills management, and an intelligent bot system.

## ✨ Features

### 🎩 Auto Hats System
- **Automatic Hat Management**: Automatically equips the best available hats
- **Situational Switching**: Changes hats based on current situation (combat, farming, exploration)
- **Auto Purchase**: Automatically buys new hats when you have enough resources
- **Priority System**: Configurable hat priority list
- **Smart Detection**: Analyzes game state to determine optimal hat choice

### 💰 Auto Buy System
- **Intelligent Purchasing**: Automatically buys weapons, upgrades, and consumables
- **Resource Management**: Maintains reserve resources for safety
- **Priority Queue**: Configurable item priority system
- **Spending Limits**: Prevents overspending with percentage-based limits
- **Smart Logic**: Only buys items that provide actual benefits

### 🏭 Auto Mills System
- **Optimal Placement**: Finds the best locations for mills using advanced algorithms
- **Resource Priority**: Focuses on high-value resources first
- **Auto Upgrade**: Automatically upgrades mills when possible
- **Protection System**: Places defensive structures around mills
- **Safety Analysis**: Avoids dangerous areas when placing mills

### 🤖 Bot System
- **AI Companions**: Spawns intelligent bots to help with various tasks
- **Multiple Behaviors**: Farming, building, exploration, and protection bots
- **Coordination**: Bots work together and share resources
- **Adaptive Intelligence**: Learns from situations and adapts behavior
- **Threat Response**: Automatically responds to dangers

### ⚔️ Auto Combat System
- **Combat Strategies**: Multiple combat modes (defensive, aggressive, balanced, sniper)
- **Target Prioritization**: Smart target selection based on threat level and strategy
- **Tactical Movement**: Advanced kiting, retreating, and positioning
- **Situational Awareness**: Adapts to health, enemy proximity, and weapon types
- **Emergency Retreat**: Automatic retreat when health is critically low

### 📈 Resource Optimization
- **Route Planning**: Optimizes resource gathering routes for maximum efficiency
- **Node Discovery**: Automatically discovers and tracks resource nodes
- **Safety Analysis**: Evaluates safety of resource locations
- **Strategy Selection**: Multiple optimization strategies (balanced, gold focus, safe farming)
- **Pathfinding**: Advanced pathfinding with obstacle and enemy avoidance

### 🎮 Advanced Mod Menu
- **Tabbed Interface**: Organized settings across multiple categories
- **Real-time Controls**: Live toggles and sliders for all features
- **Configuration Presets**: Safe Mode, Balanced Mode, Aggressive Mode
- **Import/Export**: Save and share configuration profiles
- **Visual Feedback**: Real-time statistics and status indicators

### 🗂️ Complete Item Database
- **Comprehensive Catalog**: Every weapon, hat, building, and consumable
- **Detailed Stats**: Complete item statistics and unlock requirements
- **Smart Filtering**: Filter by type, rarity, cost, and unlock level
- **Cost Analysis**: Automatic affordability calculations
- **Rarity System**: Common, Uncommon, Rare, Epic, Legendary classifications

## 🛠️ Installation

### Method 1: Userscript (Recommended)
1. Install a userscript manager like [Tampermonkey](https://www.tampermonkey.net/)
2. Build the project: `npm run build`
3. Copy the contents of `dist/sploop-advanced-mod.user.js`
4. Create a new userscript in Tampermonkey and paste the code
5. Save and enable the script
6. Navigate to sploop.io and the mod will automatically load

### Method 2: Browser Console
1. Build the project: `npm run build`
2. Open sploop.io in your browser
3. Open developer console (F12)
4. Copy and paste the built code
5. Press Enter to execute

## 🔧 Development Setup

```bash
# Clone the repository
git clone <repository-url>
cd sploop-advanced-mod

# Install dependencies
npm install

# Start development mode (watches for changes)
npm run dev

# Build for production
npm run build

# Type checking
npm run type-check

# Linting
npm run lint
```

## ⚙️ Configuration

The mod is highly configurable. Access the configuration through the browser console:

```javascript
// Get current configuration
const config = window.SploopAdvancedMod.getConfig();

// Update specific settings
window.SploopAdvancedMod.updateConfig({
  autoHats: {
    enabled: true,
    switchBasedOnSituation: true
  },
  botSystem: {
    enabled: false, // Disable bots for safety
    maxBots: 2
  }
});

// Reset to defaults
window.SploopAdvancedMod.resetConfig();
```

### Key Configuration Options

#### Auto Hats
- `enabled`: Enable/disable auto hats
- `priority`: Array of hat types in order of preference
- `switchBasedOnSituation`: Automatically switch hats based on context
- `combatHat`, `farmingHat`, `explorationHat`: Specific hats for different situations

#### Auto Buy
- `enabled`: Enable/disable auto purchasing
- `maxSpendPercentage`: Maximum percentage of resources to spend
- `reserveResources`: Minimum resources to keep in reserve
- `priorityItems`: List of items to prioritize

#### Auto Mills
- `enabled`: Enable/disable auto mills
- `maxMills`: Maximum number of mills to build
- `resourcePriority`: Order of resource types to prioritize
- `protectMills`: Automatically protect mills with defenses

#### Bot System
- `enabled`: Enable/disable bot system (⚠️ Use with caution)
- `maxBots`: Maximum number of bots to spawn
- `botBehaviors`: Which behaviors to enable for bots
- `coordination`: Bot coordination settings

#### Safety
- `emergencyStop`: Key combination to stop all automation
- `antiDetection`: Enable anti-detection measures
- `maxActionsPerSecond`: Rate limiting for actions

## 🎮 Usage

### Basic Usage
1. Load the mod (it will start automatically)
2. The mod overlay will appear showing current status
3. All enabled features will work automatically
4. Use the emergency stop key (Ctrl+Shift+X by default) if needed

### Hotkey Controls
- **Ctrl+1**: Toggle Auto Hats
- **Ctrl+2**: Toggle Auto Buy
- **Ctrl+3**: Toggle Auto Mills
- **Ctrl+4**: Toggle Bot System
- **Ctrl+5**: Toggle Auto Combat
- **Ctrl+6**: Toggle Resource Optimizer
- **Ctrl+M**: Open Mod Menu (coming soon)
- **Ctrl+Shift+X**: Emergency Stop (default)

### Advanced Usage
```javascript
// Toggle specific modules
window.SploopAdvancedMod.toggleModule('autoHats');
window.SploopAdvancedMod.toggleModule('autoBuy');
window.SploopAdvancedMod.toggleModule('autoMills');
window.SploopAdvancedMod.toggleModule('botSystem');

// Control new advanced features
window.SploopAdvancedMod.toggleAutoCombat();
window.SploopAdvancedMod.toggleResourceOptimizer();

// Set combat strategies
window.SploopAdvancedMod.setAutoCombatMode('aggressive'); // defensive, aggressive, passive
window.SploopAdvancedMod.setAutoCombatStrategy('balanced'); // defensive, aggressive, balanced, sniper

// Set resource optimization strategies
window.SploopAdvancedMod.setResourceOptimizerStrategy('goldFocus'); // balanced, goldFocus, safeFarming, speedRun

// Get detailed statistics
const stats = window.SploopAdvancedMod.getStats();
console.log(stats);

// Access individual modules
const mod = window.SploopAdvancedMod;
console.log(mod.autoHats.getHatInfo('BERSERKER'));
console.log(mod.autoBuy.getPurchaseHistory());
console.log(mod.autoMills.getOwnedMills());
console.log(mod.botSystem.getBots());
console.log(mod.autoCombat.getCurrentTarget());
console.log(mod.resourceOptimizer.getCurrentRoute());

// Access item database
import { getItemById, getItemsByType, getAffordableItems } from './data/itemDatabase';
const angelHat = getItemById('hat_angel');
const allWeapons = getItemsByType('weapon');
const affordableItems = getAffordableItems({ gold: 10000, wood: 100 });
```

## 🔒 Safety Features

- **Emergency Stop**: Instantly disable all automation with a key combination
- **Rate Limiting**: Prevents actions from being performed too quickly
- **Anti-Detection**: Randomizes timing and behavior to appear more human-like
- **Safe Mode**: Conservative settings that reduce detection risk
- **Resource Reserves**: Maintains minimum resource levels for safety

## ⚠️ Important Notes

### Bot System Warning
The bot system is disabled by default as it may be more easily detected by anti-cheat systems. Use at your own risk and consider the following:
- Start with a small number of bots (1-2)
- Monitor for any unusual behavior
- Be prepared to disable immediately if issues arise

### Detection Risk
While this mod includes anti-detection features, no automation is 100% undetectable. Use responsibly:
- Don't use all features simultaneously
- Take breaks from automation
- Monitor for any warnings or unusual behavior
- Have an exit strategy ready

### Performance
The mod is optimized for performance but may impact game performance on slower devices:
- Reduce update intervals if experiencing lag
- Disable unused modules
- Monitor browser memory usage

## 🐛 Troubleshooting

### Common Issues

**Mod not loading:**
- Check browser console for errors
- Ensure userscript manager is enabled
- Verify the script is active for sploop.io

**Features not working:**
- Check if the specific module is enabled in config
- Verify you're in an active game (not lobby)
- Check browser console for error messages

**Performance issues:**
- Increase update intervals in config
- Disable unused modules
- Close other browser tabs

**Detection concerns:**
- Enable safe mode
- Reduce action frequency
- Use emergency stop if needed

### Debug Mode
Enable debug mode for detailed logging:
```javascript
window.SploopAdvancedMod.updateConfig({ debug: true });
```

## 📝 License

This project is licensed under the MIT License - see the LICENSE file for details.

## ⚖️ Disclaimer

This mod is for educational purposes only. Use at your own risk. The developers are not responsible for any consequences of using this mod, including but not limited to account bans or other penalties. Always respect the game's terms of service and play fairly.

## 🤝 Contributing

Contributions are welcome! Please read the contributing guidelines before submitting pull requests.

## 📞 Support

If you encounter issues or have questions:
1. Check the troubleshooting section
2. Search existing issues
3. Create a new issue with detailed information
4. Include browser console logs if applicable

---

**Happy gaming! 🎮**
