const path = require('path');

module.exports = {
  entry: './src/main.ts',
  module: {
    rules: [
      {
        test: /\.tsx?$/,
        use: 'ts-loader',
        exclude: /node_modules/,
      },
    ],
  },
  resolve: {
    extensions: ['.tsx', '.ts', '.js'],
    alias: {
      '@': path.resolve(__dirname, 'src'),
      '@/modules': path.resolve(__dirname, 'src/modules'),
      '@/utils': path.resolve(__dirname, 'src/utils'),
      '@/types': path.resolve(__dirname, 'src/types'),
    },
  },
  output: {
    filename: 'sploop-advanced-mod.user.js',
    path: path.resolve(__dirname, 'dist'),
    library: {
      type: 'umd',
      name: 'SploopAdvancedMod',
    },
    globalObject: 'this',
  },
  optimization: {
    minimize: false, // Keep readable for userscript
  },
  devtool: 'source-map',
  target: 'web',
};
