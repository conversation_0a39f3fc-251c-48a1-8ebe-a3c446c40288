{"version": 3, "file": "config.d.ts", "sourceRoot": "", "sources": ["../src/config.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,OAAO,EAAE,YAAY,EAAE,MAAM,cAAc,CAAC;AAErD,MAAM,WAAW,SAAS;IAExB,OAAO,EAAE,OAAO,CAAC;IACjB,KAAK,EAAE,OAAO,CAAC;IACf,QAAQ,EAAE,OAAO,CAAC;IAClB,cAAc,EAAE,MAAM,CAAC;IAGvB,QAAQ,EAAE;QACR,OAAO,EAAE,OAAO,CAAC;QACjB,QAAQ,EAAE,OAAO,EAAE,CAAC;QACpB,WAAW,EAAE,OAAO,CAAC;QACrB,sBAAsB,EAAE,OAAO,CAAC;QAChC,SAAS,EAAE,OAAO,CAAC;QACnB,UAAU,EAAE,OAAO,CAAC;QACpB,cAAc,EAAE,OAAO,CAAC;KACzB,CAAC;IAGF,OAAO,EAAE;QACP,OAAO,EAAE,OAAO,CAAC;QACjB,aAAa,EAAE,MAAM,EAAE,CAAC;QACxB,kBAAkB,EAAE,MAAM,CAAC;QAC3B,gBAAgB,EAAE;YAChB,IAAI,EAAE,MAAM,CAAC;YACb,KAAK,EAAE,MAAM,CAAC;YACd,IAAI,EAAE,MAAM,CAAC;YACb,IAAI,EAAE,MAAM,CAAC;SACd,CAAC;QACF,OAAO,EAAE,OAAO,CAAC;QACjB,UAAU,EAAE,OAAO,CAAC;QACpB,WAAW,EAAE,OAAO,CAAC;KACtB,CAAC;IAGF,SAAS,EAAE;QACT,OAAO,EAAE,OAAO,CAAC;QACjB,QAAQ,EAAE,MAAM,CAAC;QACjB,gBAAgB,EAAE,YAAY,EAAE,CAAC;QACjC,WAAW,EAAE,OAAO,CAAC;QACrB,gBAAgB,EAAE,OAAO,CAAC;QAC1B,YAAY,EAAE,OAAO,CAAC;QACtB,WAAW,EAAE,MAAM,CAAC;QACpB,kBAAkB,EAAE;YAClB,aAAa,EAAE,OAAO,CAAC;YACvB,QAAQ,EAAE,OAAO,CAAC;YAClB,WAAW,EAAE,OAAO,CAAC;SACtB,CAAC;KACH,CAAC;IAGF,SAAS,EAAE;QACT,OAAO,EAAE,OAAO,CAAC;QACjB,OAAO,EAAE,MAAM,CAAC;QAChB,YAAY,EAAE;YACZ,OAAO,EAAE,OAAO,CAAC;YACjB,MAAM,EAAE,OAAO,CAAC;YAChB,QAAQ,EAAE,OAAO,CAAC;YAClB,WAAW,EAAE,OAAO,CAAC;YACrB,UAAU,EAAE,OAAO,CAAC;SACrB,CAAC;QACF,YAAY,EAAE;YACZ,cAAc,EAAE,OAAO,CAAC;YACxB,aAAa,EAAE,OAAO,CAAC;YACvB,cAAc,EAAE,OAAO,CAAC;SACzB,CAAC;QACF,YAAY,EAAE;YACZ,YAAY,EAAE,OAAO,CAAC;YACtB,gBAAgB,EAAE,OAAO,CAAC;YAC1B,gBAAgB,EAAE,OAAO,CAAC;SAC3B,CAAC;KACH,CAAC;IAGF,MAAM,EAAE;QACN,aAAa,EAAE,OAAO,CAAC;QACvB,gBAAgB,EAAE,OAAO,CAAC;QAC1B,iBAAiB,EAAE,OAAO,CAAC;QAC3B,aAAa,EAAE,OAAO,CAAC;QACvB,aAAa,EAAE,MAAM,CAAC;QACtB,mBAAmB,EAAE,MAAM,CAAC;KAC7B,CAAC;IAGF,EAAE,EAAE;QACF,WAAW,EAAE,OAAO,CAAC;QACrB,SAAS,EAAE,OAAO,CAAC;QACnB,QAAQ,EAAE,OAAO,CAAC;QAClB,eAAe,EAAE,UAAU,GAAG,WAAW,GAAG,aAAa,GAAG,cAAc,CAAC;QAC3E,YAAY,EAAE,MAAM,CAAC;KACtB,CAAC;CACH;AAED,eAAO,MAAM,cAAc,EAAE,SAuG5B,CAAC;AAEF,qBAAa,aAAa;IACxB,OAAO,CAAC,MAAM,CAAY;IAC1B,OAAO,CAAC,QAAQ,CAAC,WAAW,CAAgC;;IAMrD,SAAS,IAAI,SAAS;IAItB,YAAY,CAAC,OAAO,EAAE,OAAO,CAAC,SAAS,CAAC,GAAG,IAAI;IAK/C,WAAW,IAAI,IAAI;IAK1B,OAAO,CAAC,UAAU;IAalB,OAAO,CAAC,UAAU;CAOnB"}