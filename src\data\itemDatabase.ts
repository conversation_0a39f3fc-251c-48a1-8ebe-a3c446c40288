import { ResourceType, HatType, BuildingType } from '@/types/game';

export interface ItemData {
  id: string;
  name: string;
  type: 'weapon' | 'hat' | 'building' | 'consumable' | 'upgrade' | 'resource' | 'tool';
  category: string;
  cost: {
    wood?: number;
    stone?: number;
    food?: number;
    gold?: number;
    points?: number;
  };
  unlockLevel: number;
  stats?: Record<string, number>;
  description: string;
  rarity: 'common' | 'uncommon' | 'rare' | 'epic' | 'legendary';
  stackable: boolean;
  durability?: number;
  effects?: Record<string, any>;
}

export const WEAPONS_DATABASE: Record<string, ItemData> = {
  // Primary Weapons
  'weapon_tool': {
    id: 'weapon_tool',
    name: 'Tool',
    type: 'weapon',
    category: 'primary',
    cost: {},
    unlockLevel: 0,
    stats: { damage: 20, range: 65, speed: 300 },
    description: 'Basic starting tool',
    rarity: 'common',
    stackable: false,
    durability: -1
  },
  'weapon_wood_sword': {
    id: 'weapon_wood_sword',
    name: 'Wood Sword',
    type: 'weapon',
    category: 'primary',
    cost: { gold: 100 },
    unlockLevel: 1,
    stats: { damage: 25, range: 68, speed: 300 },
    description: 'Basic wooden sword',
    rarity: 'common',
    stackable: false,
    durability: -1
  },
  'weapon_stone_sword': {
    id: 'weapon_stone_sword',
    name: 'Stone Sword',
    type: 'weapon',
    category: 'primary',
    cost: { gold: 400 },
    unlockLevel: 2,
    stats: { damage: 35, range: 68, speed: 300 },
    description: 'Stronger stone sword',
    rarity: 'common',
    stackable: false,
    durability: -1
  },
  'weapon_gold_sword': {
    id: 'weapon_gold_sword',
    name: 'Gold Sword',
    type: 'weapon',
    category: 'primary',
    cost: { gold: 3000 },
    unlockLevel: 5,
    stats: { damage: 50, range: 68, speed: 300 },
    description: 'Powerful gold sword',
    rarity: 'uncommon',
    stackable: false,
    durability: -1
  },
  'weapon_diamond_sword': {
    id: 'weapon_diamond_sword',
    name: 'Diamond Sword',
    type: 'weapon',
    category: 'primary',
    cost: { gold: 15000 },
    unlockLevel: 10,
    stats: { damage: 65, range: 68, speed: 300 },
    description: 'Elite diamond sword',
    rarity: 'rare',
    stackable: false,
    durability: -1
  },
  'weapon_ruby_sword': {
    id: 'weapon_ruby_sword',
    name: 'Ruby Sword',
    type: 'weapon',
    category: 'primary',
    cost: { gold: 50000 },
    unlockLevel: 15,
    stats: { damage: 75, range: 68, speed: 300 },
    description: 'Rare ruby sword with enhanced damage',
    rarity: 'epic',
    stackable: false,
    durability: -1
  },
  
  // Secondary Weapons
  'weapon_wood_bow': {
    id: 'weapon_wood_bow',
    name: 'Wood Bow',
    type: 'weapon',
    category: 'secondary',
    cost: { wood: 5, gold: 500 },
    unlockLevel: 3,
    stats: { damage: 25, range: 230, speed: 700 },
    description: 'Basic ranged weapon',
    rarity: 'common',
    stackable: false,
    durability: -1
  },
  'weapon_stone_bow': {
    id: 'weapon_stone_bow',
    name: 'Stone Bow',
    type: 'weapon',
    category: 'secondary',
    cost: { stone: 5, gold: 2000 },
    unlockLevel: 6,
    stats: { damage: 35, range: 240, speed: 700 },
    description: 'Improved stone bow',
    rarity: 'uncommon',
    stackable: false,
    durability: -1
  },
  'weapon_gold_bow': {
    id: 'weapon_gold_bow',
    name: 'Gold Bow',
    type: 'weapon',
    category: 'secondary',
    cost: { gold: 8000 },
    unlockLevel: 9,
    stats: { damage: 50, range: 250, speed: 600 },
    description: 'Powerful gold bow',
    rarity: 'rare',
    stackable: false,
    durability: -1
  },
  'weapon_diamond_bow': {
    id: 'weapon_diamond_bow',
    name: 'Diamond Bow',
    type: 'weapon',
    category: 'secondary',
    cost: { gold: 25000 },
    unlockLevel: 13,
    stats: { damage: 65, range: 260, speed: 500 },
    description: 'Elite diamond bow',
    rarity: 'epic',
    stackable: false,
    durability: -1
  }
};

export const HATS_DATABASE: Record<string, ItemData> = {
  'hat_none': {
    id: 'hat_none',
    name: 'No Hat',
    type: 'hat',
    category: 'head',
    cost: {},
    unlockLevel: 0,
    stats: {},
    description: 'No hat equipped',
    rarity: 'common',
    stackable: false
  },
  'hat_marksman': {
    id: 'hat_marksman',
    name: 'Marksman Hat',
    type: 'hat',
    category: 'head',
    cost: { gold: 7000 },
    unlockLevel: 1,
    stats: { damageBonus: 25 },
    description: 'Increases weapon damage by 25%',
    rarity: 'uncommon',
    stackable: false
  },
  'hat_bush': {
    id: 'hat_bush',
    name: 'Bush Hat',
    type: 'hat',
    category: 'head',
    cost: { gold: 3000 },
    unlockLevel: 1,
    stats: { healthBonus: 20 },
    description: 'Increases health by 20',
    rarity: 'common',
    stackable: false
  },
  'hat_berserker': {
    id: 'hat_berserker',
    name: 'Berserker Hat',
    type: 'hat',
    category: 'head',
    cost: { gold: 12000 },
    unlockLevel: 7,
    stats: { speedBonus: 15, damageBonus: 35 },
    description: 'Increases speed and damage',
    rarity: 'rare',
    stackable: false
  },
  'hat_jungle': {
    id: 'hat_jungle',
    name: 'Jungle Hat',
    type: 'hat',
    category: 'head',
    cost: { gold: 15000 },
    unlockLevel: 6,
    stats: { healthBonus: 30, resourceBonus: 15 },
    description: 'Increases health and resource gathering',
    rarity: 'rare',
    stackable: false
  },
  'hat_crystal': {
    id: 'hat_crystal',
    name: 'Crystal Hat',
    type: 'hat',
    category: 'head',
    cost: { gold: 25000 },
    unlockLevel: 12,
    stats: { resourceBonus: 25, experienceBonus: 25 },
    description: 'Increases resource gathering and experience',
    rarity: 'epic',
    stackable: false
  },
  'hat_space': {
    id: 'hat_space',
    name: 'Space Hat',
    type: 'hat',
    category: 'head',
    cost: { gold: 30000 },
    unlockLevel: 15,
    stats: { healthBonus: 40, speedBonus: 20 },
    description: 'Advanced space technology hat',
    rarity: 'epic',
    stackable: false
  },
  'hat_cyborg': {
    id: 'hat_cyborg',
    name: 'Cyborg Hat',
    type: 'hat',
    category: 'head',
    cost: { gold: 50000 },
    unlockLevel: 18,
    stats: { healthBonus: 25, damageBonus: 40, experienceBonus: 15 },
    description: 'Cybernetic enhancement hat',
    rarity: 'legendary',
    stackable: false
  },
  'hat_monkey': {
    id: 'hat_monkey',
    name: 'Monkey Hat',
    type: 'hat',
    category: 'head',
    cost: { gold: 8000 },
    unlockLevel: 4,
    stats: { speedBonus: 25, resourceBonus: 20 },
    description: 'Increases speed and resource gathering',
    rarity: 'uncommon',
    stackable: false
  },
  'hat_elf': {
    id: 'hat_elf',
    name: 'Elf Hat',
    type: 'hat',
    category: 'head',
    cost: { gold: 20000 },
    unlockLevel: 9,
    stats: { damageBonus: 20, resourceBonus: 30 },
    description: 'Magical elf hat with enhanced abilities',
    rarity: 'rare',
    stackable: false
  },
  'hat_knight': {
    id: 'hat_knight',
    name: 'Knight Hat',
    type: 'hat',
    category: 'head',
    cost: { gold: 35000 },
    unlockLevel: 14,
    stats: { healthBonus: 50, damageBonus: 15 },
    description: 'Heavy armor knight helmet',
    rarity: 'epic',
    stackable: false
  },
  'hat_samurai': {
    id: 'hat_samurai',
    name: 'Samurai Hat',
    type: 'hat',
    category: 'head',
    cost: { gold: 70000 },
    unlockLevel: 20,
    stats: { healthBonus: 35, speedBonus: 10, damageBonus: 50 },
    description: 'Traditional samurai helmet',
    rarity: 'legendary',
    stackable: false
  },
  'hat_angel': {
    id: 'hat_angel',
    name: 'Angel Hat',
    type: 'hat',
    category: 'head',
    cost: { gold: 100000 },
    unlockLevel: 25,
    stats: { healthBonus: 60, speedBonus: 15, damageBonus: 30, resourceBonus: 20, experienceBonus: 30 },
    description: 'Divine angel halo with all-around bonuses',
    rarity: 'legendary',
    stackable: false
  },
  'hat_devil': {
    id: 'hat_devil',
    name: 'Devil Hat',
    type: 'hat',
    category: 'head',
    cost: { gold: 150000 },
    unlockLevel: 30,
    stats: { healthBonus: 40, speedBonus: 20, damageBonus: 60, resourceBonus: 15, experienceBonus: 25 },
    description: 'Demonic horns with devastating power',
    rarity: 'legendary',
    stackable: false
  }
};
