import { ResourceType, HatType, BuildingType } from '@/types/game';

export interface ItemData {
  id: string;
  name: string;
  type: 'weapon' | 'hat' | 'building' | 'consumable' | 'upgrade' | 'resource' | 'tool';
  category: string;
  cost: {
    wood?: number;
    stone?: number;
    food?: number;
    gold?: number;
    points?: number;
  };
  unlockLevel: number;
  stats?: Record<string, number>;
  description: string;
  rarity: 'common' | 'uncommon' | 'rare' | 'epic' | 'legendary';
  stackable: boolean;
  durability?: number;
  effects?: Record<string, any>;
}

export const WEAPONS_DATABASE: Record<string, ItemData> = {
  // Primary Weapons
  'weapon_tool': {
    id: 'weapon_tool',
    name: 'Tool',
    type: 'weapon',
    category: 'primary',
    cost: {},
    unlockLevel: 0,
    stats: { damage: 20, range: 65, speed: 300 },
    description: 'Basic starting tool',
    rarity: 'common',
    stackable: false,
    durability: -1
  },
  'weapon_wood_sword': {
    id: 'weapon_wood_sword',
    name: 'Wood Sword',
    type: 'weapon',
    category: 'primary',
    cost: { gold: 100 },
    unlockLevel: 1,
    stats: { damage: 25, range: 68, speed: 300 },
    description: 'Basic wooden sword',
    rarity: 'common',
    stackable: false,
    durability: -1
  },
  'weapon_stone_sword': {
    id: 'weapon_stone_sword',
    name: 'Stone Sword',
    type: 'weapon',
    category: 'primary',
    cost: { gold: 400 },
    unlockLevel: 2,
    stats: { damage: 35, range: 68, speed: 300 },
    description: 'Stronger stone sword',
    rarity: 'common',
    stackable: false,
    durability: -1
  },
  'weapon_gold_sword': {
    id: 'weapon_gold_sword',
    name: 'Gold Sword',
    type: 'weapon',
    category: 'primary',
    cost: { gold: 3000 },
    unlockLevel: 5,
    stats: { damage: 50, range: 68, speed: 300 },
    description: 'Powerful gold sword',
    rarity: 'uncommon',
    stackable: false,
    durability: -1
  },
  'weapon_diamond_sword': {
    id: 'weapon_diamond_sword',
    name: 'Diamond Sword',
    type: 'weapon',
    category: 'primary',
    cost: { gold: 15000 },
    unlockLevel: 10,
    stats: { damage: 65, range: 68, speed: 300 },
    description: 'Elite diamond sword',
    rarity: 'rare',
    stackable: false,
    durability: -1
  },
  'weapon_ruby_sword': {
    id: 'weapon_ruby_sword',
    name: 'Ruby Sword',
    type: 'weapon',
    category: 'primary',
    cost: { gold: 50000 },
    unlockLevel: 15,
    stats: { damage: 75, range: 68, speed: 300 },
    description: 'Rare ruby sword with enhanced damage',
    rarity: 'epic',
    stackable: false,
    durability: -1
  },
  
  // Secondary Weapons
  'weapon_wood_bow': {
    id: 'weapon_wood_bow',
    name: 'Wood Bow',
    type: 'weapon',
    category: 'secondary',
    cost: { wood: 5, gold: 500 },
    unlockLevel: 3,
    stats: { damage: 25, range: 230, speed: 700 },
    description: 'Basic ranged weapon',
    rarity: 'common',
    stackable: false,
    durability: -1
  },
  'weapon_stone_bow': {
    id: 'weapon_stone_bow',
    name: 'Stone Bow',
    type: 'weapon',
    category: 'secondary',
    cost: { stone: 5, gold: 2000 },
    unlockLevel: 6,
    stats: { damage: 35, range: 240, speed: 700 },
    description: 'Improved stone bow',
    rarity: 'uncommon',
    stackable: false,
    durability: -1
  },
  'weapon_gold_bow': {
    id: 'weapon_gold_bow',
    name: 'Gold Bow',
    type: 'weapon',
    category: 'secondary',
    cost: { gold: 8000 },
    unlockLevel: 9,
    stats: { damage: 50, range: 250, speed: 600 },
    description: 'Powerful gold bow',
    rarity: 'rare',
    stackable: false,
    durability: -1
  },
  'weapon_diamond_bow': {
    id: 'weapon_diamond_bow',
    name: 'Diamond Bow',
    type: 'weapon',
    category: 'secondary',
    cost: { gold: 25000 },
    unlockLevel: 13,
    stats: { damage: 65, range: 260, speed: 500 },
    description: 'Elite diamond bow',
    rarity: 'epic',
    stackable: false,
    durability: -1
  }
};

export const HATS_DATABASE: Record<string, ItemData> = {
  'hat_none': {
    id: 'hat_none',
    name: 'No Hat',
    type: 'hat',
    category: 'head',
    cost: {},
    unlockLevel: 0,
    stats: {},
    description: 'No hat equipped',
    rarity: 'common',
    stackable: false
  },
  'hat_marksman': {
    id: 'hat_marksman',
    name: 'Marksman Hat',
    type: 'hat',
    category: 'head',
    cost: { gold: 7000 },
    unlockLevel: 1,
    stats: { damageBonus: 25 },
    description: 'Increases weapon damage by 25%',
    rarity: 'uncommon',
    stackable: false
  },
  'hat_bush': {
    id: 'hat_bush',
    name: 'Bush Hat',
    type: 'hat',
    category: 'head',
    cost: { gold: 3000 },
    unlockLevel: 1,
    stats: { healthBonus: 20 },
    description: 'Increases health by 20',
    rarity: 'common',
    stackable: false
  },
  'hat_berserker': {
    id: 'hat_berserker',
    name: 'Berserker Hat',
    type: 'hat',
    category: 'head',
    cost: { gold: 12000 },
    unlockLevel: 7,
    stats: { speedBonus: 15, damageBonus: 35 },
    description: 'Increases speed and damage',
    rarity: 'rare',
    stackable: false
  },
  'hat_jungle': {
    id: 'hat_jungle',
    name: 'Jungle Hat',
    type: 'hat',
    category: 'head',
    cost: { gold: 15000 },
    unlockLevel: 6,
    stats: { healthBonus: 30, resourceBonus: 15 },
    description: 'Increases health and resource gathering',
    rarity: 'rare',
    stackable: false
  },
  'hat_crystal': {
    id: 'hat_crystal',
    name: 'Crystal Hat',
    type: 'hat',
    category: 'head',
    cost: { gold: 25000 },
    unlockLevel: 12,
    stats: { resourceBonus: 25, experienceBonus: 25 },
    description: 'Increases resource gathering and experience',
    rarity: 'epic',
    stackable: false
  },
  'hat_space': {
    id: 'hat_space',
    name: 'Space Hat',
    type: 'hat',
    category: 'head',
    cost: { gold: 30000 },
    unlockLevel: 15,
    stats: { healthBonus: 40, speedBonus: 20 },
    description: 'Advanced space technology hat',
    rarity: 'epic',
    stackable: false
  },
  'hat_cyborg': {
    id: 'hat_cyborg',
    name: 'Cyborg Hat',
    type: 'hat',
    category: 'head',
    cost: { gold: 50000 },
    unlockLevel: 18,
    stats: { healthBonus: 25, damageBonus: 40, experienceBonus: 15 },
    description: 'Cybernetic enhancement hat',
    rarity: 'legendary',
    stackable: false
  },
  'hat_monkey': {
    id: 'hat_monkey',
    name: 'Monkey Hat',
    type: 'hat',
    category: 'head',
    cost: { gold: 8000 },
    unlockLevel: 4,
    stats: { speedBonus: 25, resourceBonus: 20 },
    description: 'Increases speed and resource gathering',
    rarity: 'uncommon',
    stackable: false
  },
  'hat_elf': {
    id: 'hat_elf',
    name: 'Elf Hat',
    type: 'hat',
    category: 'head',
    cost: { gold: 20000 },
    unlockLevel: 9,
    stats: { damageBonus: 20, resourceBonus: 30 },
    description: 'Magical elf hat with enhanced abilities',
    rarity: 'rare',
    stackable: false
  },
  'hat_knight': {
    id: 'hat_knight',
    name: 'Knight Hat',
    type: 'hat',
    category: 'head',
    cost: { gold: 35000 },
    unlockLevel: 14,
    stats: { healthBonus: 50, damageBonus: 15 },
    description: 'Heavy armor knight helmet',
    rarity: 'epic',
    stackable: false
  },
  'hat_samurai': {
    id: 'hat_samurai',
    name: 'Samurai Hat',
    type: 'hat',
    category: 'head',
    cost: { gold: 70000 },
    unlockLevel: 20,
    stats: { healthBonus: 35, speedBonus: 10, damageBonus: 50 },
    description: 'Traditional samurai helmet',
    rarity: 'legendary',
    stackable: false
  },
  'hat_angel': {
    id: 'hat_angel',
    name: 'Angel Hat',
    type: 'hat',
    category: 'head',
    cost: { gold: 100000 },
    unlockLevel: 25,
    stats: { healthBonus: 60, speedBonus: 15, damageBonus: 30, resourceBonus: 20, experienceBonus: 30 },
    description: 'Divine angel halo with all-around bonuses',
    rarity: 'legendary',
    stackable: false
  },
  'hat_devil': {
    id: 'hat_devil',
    name: 'Devil Hat',
    type: 'hat',
    category: 'head',
    cost: { gold: 150000 },
    unlockLevel: 30,
    stats: { healthBonus: 40, speedBonus: 20, damageBonus: 60, resourceBonus: 15, experienceBonus: 25 },
    description: 'Demonic horns with devastating power',
    rarity: 'legendary',
    stackable: false
  }
};

export const BUILDINGS_DATABASE: Record<string, ItemData> = {
  'building_windmill': {
    id: 'building_windmill',
    name: 'Windmill',
    type: 'building',
    category: 'resource',
    cost: { wood: 5, stone: 5 },
    unlockLevel: 1,
    stats: { production: 1, health: 400 },
    description: 'Generates resources automatically',
    rarity: 'common',
    stackable: false
  },
  'building_spike': {
    id: 'building_spike',
    name: 'Spike',
    type: 'building',
    category: 'defense',
    cost: { wood: 10 },
    unlockLevel: 1,
    stats: { damage: 25, health: 400 },
    description: 'Damages enemies who walk into it',
    rarity: 'common',
    stackable: false
  },
  'building_wall': {
    id: 'building_wall',
    name: 'Wood Wall',
    type: 'building',
    category: 'defense',
    cost: { wood: 10 },
    unlockLevel: 1,
    stats: { health: 380 },
    description: 'Basic wooden wall for protection',
    rarity: 'common',
    stackable: false
  },
  'building_turret': {
    id: 'building_turret',
    name: 'Turret',
    type: 'building',
    category: 'defense',
    cost: { wood: 50, stone: 50, gold: 400 },
    unlockLevel: 9,
    stats: { damage: 35, health: 800, range: 200 },
    description: 'Automated defense turret',
    rarity: 'rare',
    stackable: false
  }
};

export const CONSUMABLES_DATABASE: Record<string, ItemData> = {
  'consumable_apple': {
    id: 'consumable_apple',
    name: 'Apple',
    type: 'consumable',
    category: 'food',
    cost: { gold: 50 },
    unlockLevel: 1,
    stats: { healing: 20 },
    description: 'Restores 20 health',
    rarity: 'common',
    stackable: true
  },
  'consumable_bread': {
    id: 'consumable_bread',
    name: 'Bread',
    type: 'consumable',
    category: 'food',
    cost: { gold: 80 },
    unlockLevel: 2,
    stats: { healing: 30 },
    description: 'Restores 30 health',
    rarity: 'common',
    stackable: true
  },
  'consumable_cheese': {
    id: 'consumable_cheese',
    name: 'Cheese',
    type: 'consumable',
    category: 'food',
    cost: { gold: 120 },
    unlockLevel: 3,
    stats: { healing: 40 },
    description: 'Restores 40 health',
    rarity: 'uncommon',
    stackable: true
  },
  'consumable_health_potion': {
    id: 'consumable_health_potion',
    name: 'Health Potion',
    type: 'consumable',
    category: 'potion',
    cost: { gold: 200 },
    unlockLevel: 4,
    stats: { healing: 100 },
    description: 'Instantly restores 100 health',
    rarity: 'uncommon',
    stackable: true
  }
};

export const UPGRADES_DATABASE: Record<string, ItemData> = {
  'upgrade_damage_1': {
    id: 'upgrade_damage_1',
    name: 'Damage Upgrade I',
    type: 'upgrade',
    category: 'combat',
    cost: { gold: 1000 },
    unlockLevel: 2,
    stats: { damageMultiplier: 1.1 },
    description: 'Increases weapon damage by 10%',
    rarity: 'common',
    stackable: false
  },
  'upgrade_speed_1': {
    id: 'upgrade_speed_1',
    name: 'Speed Upgrade I',
    type: 'upgrade',
    category: 'movement',
    cost: { gold: 1500 },
    unlockLevel: 3,
    stats: { speedMultiplier: 1.15 },
    description: 'Increases movement speed by 15%',
    rarity: 'common',
    stackable: false
  },
  'upgrade_health_1': {
    id: 'upgrade_health_1',
    name: 'Health Upgrade I',
    type: 'upgrade',
    category: 'survival',
    cost: { gold: 2000 },
    unlockLevel: 4,
    stats: { healthBonus: 20 },
    description: 'Increases maximum health by 20',
    rarity: 'common',
    stackable: false
  }
};

// Master database combining all items
export const MASTER_ITEM_DATABASE: Record<string, ItemData> = {
  ...WEAPONS_DATABASE,
  ...HATS_DATABASE,
  ...BUILDINGS_DATABASE,
  ...CONSUMABLES_DATABASE,
  ...UPGRADES_DATABASE
};

// Helper functions
export function getItemById(id: string): ItemData | null {
  return MASTER_ITEM_DATABASE[id] || null;
}

export function getItemsByType(type: ItemData['type']): ItemData[] {
  return Object.values(MASTER_ITEM_DATABASE).filter(item => item.type === type);
}

export function getItemsByCategory(category: string): ItemData[] {
  return Object.values(MASTER_ITEM_DATABASE).filter(item => item.category === category);
}

export function getItemsByRarity(rarity: ItemData['rarity']): ItemData[] {
  return Object.values(MASTER_ITEM_DATABASE).filter(item => item.rarity === rarity);
}

export function getAffordableItems(resources: { wood?: number; stone?: number; food?: number; gold?: number; points?: number }): ItemData[] {
  return Object.values(MASTER_ITEM_DATABASE).filter(item => {
    return Object.entries(item.cost).every(([resource, cost]) => {
      const available = resources[resource as keyof typeof resources] || 0;
      return available >= (cost || 0);
    });
  });
}

export const BUILDINGS_DATABASE_EXTENDED: Record<string, ItemData> = {
  'building_windmill': {
    id: 'building_windmill',
    name: 'Windmill',
    type: 'building',
    category: 'resource',
    cost: { wood: 5, stone: 5 },
    unlockLevel: 1,
    stats: { production: 1, health: 400 },
    description: 'Generates resources automatically',
    rarity: 'common',
    stackable: false
  },
  'building_spike': {
    id: 'building_spike',
    name: 'Spike',
    type: 'building',
    category: 'defense',
    cost: { wood: 10 },
    unlockLevel: 1,
    stats: { damage: 25, health: 400 },
    description: 'Damages enemies who walk into it',
    rarity: 'common',
    stackable: false
  },
  'building_wall': {
    id: 'building_wall',
    name: 'Wood Wall',
    type: 'building',
    category: 'defense',
    cost: { wood: 10 },
    unlockLevel: 1,
    stats: { health: 380 },
    description: 'Basic wooden wall for protection',
    rarity: 'common',
    stackable: false
  },
  'building_turret': {
    id: 'building_turret',
    name: 'Turret',
    type: 'building',
    category: 'defense',
    cost: { wood: 50, stone: 50, gold: 400 },
    unlockLevel: 9,
    stats: { damage: 35, health: 800, range: 200 },
    description: 'Automated defense turret',
    rarity: 'rare',
    stackable: false
  },
  'building_mine': {
    id: 'building_mine',
    name: 'Mine',
    type: 'building',
    category: 'resource',
    cost: { wood: 20, stone: 20, gold: 100 },
    unlockLevel: 5,
    stats: { production: 2, health: 600 },
    description: 'Advanced resource generator',
    rarity: 'uncommon',
    stackable: false
  },
  'building_pit_trap': {
    id: 'building_pit_trap',
    name: 'Pit Trap',
    type: 'building',
    category: 'trap',
    cost: { wood: 30, stone: 30 },
    unlockLevel: 6,
    stats: { damage: 45, health: 500 },
    description: 'Hidden trap that damages enemies',
    rarity: 'uncommon',
    stackable: false
  },
  'building_boost_pad': {
    id: 'building_boost_pad',
    name: 'Boost Pad',
    type: 'building',
    category: 'utility',
    cost: { stone: 20, gold: 150 },
    unlockLevel: 4,
    stats: { health: 400 },
    description: 'Gives speed boost to players',
    rarity: 'uncommon',
    stackable: false
  },
  'building_healing_pad': {
    id: 'building_healing_pad',
    name: 'Healing Pad',
    type: 'building',
    category: 'utility',
    cost: { wood: 20, food: 10, gold: 100 },
    unlockLevel: 5,
    stats: { health: 400 },
    description: 'Heals players who stand on it',
    rarity: 'uncommon',
    stackable: false
  },
  'building_spawn_pad': {
    id: 'building_spawn_pad',
    name: 'Spawn Pad',
    type: 'building',
    category: 'utility',
    cost: { wood: 100, stone: 100, gold: 1000 },
    unlockLevel: 15,
    stats: { health: 400 },
    description: 'Allows respawning at this location',
    rarity: 'epic',
    stackable: false
  },
  'building_platform': {
    id: 'building_platform',
    name: 'Platform',
    type: 'building',
    category: 'utility',
    cost: { wood: 20 },
    unlockLevel: 2,
    stats: { health: 300 },
    description: 'Elevated platform for strategic positioning',
    rarity: 'common',
    stackable: false
  },
  'building_teleporter': {
    id: 'building_teleporter',
    name: 'Teleporter',
    type: 'building',
    category: 'utility',
    cost: { wood: 200, stone: 200, gold: 2000 },
    unlockLevel: 20,
    stats: { health: 1000 },
    description: 'Teleports players to linked teleporter',
    rarity: 'legendary',
    stackable: false
  },
  'building_blocker': {
    id: 'building_blocker',
    name: 'Blocker',
    type: 'building',
    category: 'utility',
    cost: { stone: 15, gold: 80 },
    unlockLevel: 4,
    stats: { health: 480 },
    description: 'Blocks projectiles but allows players to pass',
    rarity: 'uncommon',
    stackable: false
  }
};
