import { ModConfig } from '@/config';
import { EventEmitter } from 'eventemitter3';

export interface MenuTab {
  id: string;
  name: string;
  icon: string;
  content: HTMLElement;
}

export interface ConfigPreset {
  name: string;
  description: string;
  config: Partial<ModConfig>;
}

export class ModMenuSystem extends EventEmitter {
  private container: HTMLElement | null = null;
  private isVisible = false;
  private currentTab = 'general';
  private tabs: Map<string, MenuTab> = new Map();
  private config: ModConfig;
  
  // Configuration presets
  private readonly PRESETS: ConfigPreset[] = [
    {
      name: 'Safe Mode',
      description: 'Conservative settings with minimal detection risk',
      config: {
        autoHats: { enabled: true, autoUpgrade: false, switchBasedOnSituation: false },
        autoBuy: { enabled: false },
        autoMills: { enabled: true, maxMills: 3, protectMills: false },
        botSystem: { enabled: false },
        safety: { antiDetection: true, randomizeActions: true, maxActionsPerSecond: 2 }
      }
    },
    {
      name: 'Balanced Mode',
      description: 'Moderate automation with good performance',
      config: {
        autoHats: { enabled: true, autoUpgrade: true, switchBasedOnSituation: true },
        autoBuy: { enabled: true, maxSpendPercentage: 60 },
        autoMills: { enabled: true, maxMills: 6, protectMills: true },
        botSystem: { enabled: false },
        safety: { antiDetection: true, randomizeActions: true, maxActionsPerSecond: 4 }
      }
    },
    {
      name: 'Aggressive Mode',
      description: 'Maximum automation and performance (higher risk)',
      config: {
        autoHats: { enabled: true, autoUpgrade: true, switchBasedOnSituation: true },
        autoBuy: { enabled: true, maxSpendPercentage: 80 },
        autoMills: { enabled: true, maxMills: 10, protectMills: true },
        botSystem: { enabled: true, maxBots: 3 },
        safety: { antiDetection: false, randomizeActions: false, maxActionsPerSecond: 8 }
      }
    }
  ];
  
  constructor(config: ModConfig) {
    super();
    this.config = config;
    this.createMenu();
    this.setupEventListeners();
  }
  
  private createMenu(): void {
    // Create main container
    this.container = document.createElement('div');
    this.container.id = 'sploop-mod-menu';
    this.container.style.cssText = `
      position: fixed;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      width: 800px;
      height: 600px;
      background: linear-gradient(135deg, #1a1a2e 0%, #16213e 100%);
      border: 2px solid #4CAF50;
      border-radius: 12px;
      box-shadow: 0 20px 40px rgba(0,0,0,0.5);
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
      color: white;
      z-index: 50000;
      display: none;
      overflow: hidden;
    `;
    
    // Create header
    const header = this.createHeader();
    this.container.appendChild(header);
    
    // Create tab navigation
    const tabNav = this.createTabNavigation();
    this.container.appendChild(tabNav);
    
    // Create content area
    const contentArea = this.createContentArea();
    this.container.appendChild(contentArea);
    
    // Create tabs
    this.createTabs();
    
    document.body.appendChild(this.container);
  }
  
  private createHeader(): HTMLElement {
    const header = document.createElement('div');
    header.style.cssText = `
      padding: 15px 20px;
      background: rgba(76, 175, 80, 0.1);
      border-bottom: 1px solid #4CAF50;
      display: flex;
      justify-content: space-between;
      align-items: center;
    `;
    
    const title = document.createElement('h2');
    title.textContent = '🚀 Sploop Advanced Mod - Control Panel';
    title.style.cssText = `
      margin: 0;
      font-size: 18px;
      font-weight: 600;
      color: #4CAF50;
    `;
    
    const closeButton = document.createElement('button');
    closeButton.textContent = '✕';
    closeButton.style.cssText = `
      background: #f44336;
      border: none;
      color: white;
      width: 30px;
      height: 30px;
      border-radius: 50%;
      cursor: pointer;
      font-size: 16px;
      font-weight: bold;
    `;
    closeButton.onclick = () => this.hide();
    
    header.appendChild(title);
    header.appendChild(closeButton);
    
    return header;
  }
  
  private createTabNavigation(): HTMLElement {
    const tabNav = document.createElement('div');
    tabNav.id = 'tab-navigation';
    tabNav.style.cssText = `
      display: flex;
      background: rgba(0,0,0,0.3);
      border-bottom: 1px solid #333;
    `;
    
    return tabNav;
  }
  
  private createContentArea(): HTMLElement {
    const contentArea = document.createElement('div');
    contentArea.id = 'content-area';
    contentArea.style.cssText = `
      flex: 1;
      padding: 20px;
      overflow-y: auto;
      height: calc(100% - 120px);
    `;
    
    return contentArea;
  }
  
  private createTabs(): void {
    // General Settings Tab
    this.addTab('general', 'General', '⚙️', this.createGeneralTab());
    
    // Auto Hats Tab
    this.addTab('autoHats', 'Auto Hats', '🎩', this.createAutoHatsTab());
    
    // Auto Buy Tab
    this.addTab('autoBuy', 'Auto Buy', '💰', this.createAutoBuyTab());
    
    // Auto Mills Tab
    this.addTab('autoMills', 'Auto Mills', '🏭', this.createAutoMillsTab());
    
    // Bot System Tab
    this.addTab('botSystem', 'Bot System', '🤖', this.createBotSystemTab());
    
    // Safety Tab
    this.addTab('safety', 'Safety', '🛡️', this.createSafetyTab());
    
    // Presets Tab
    this.addTab('presets', 'Presets', '📋', this.createPresetsTab());
    
    // Show first tab
    this.showTab('general');
  }
  
  private addTab(id: string, name: string, icon: string, content: HTMLElement): void {
    const tab: MenuTab = { id, name, icon, content };
    this.tabs.set(id, tab);
    
    // Create tab button
    const tabButton = document.createElement('button');
    tabButton.id = `tab-${id}`;
    tabButton.innerHTML = `${icon} ${name}`;
    tabButton.style.cssText = `
      padding: 12px 20px;
      background: transparent;
      border: none;
      color: #ccc;
      cursor: pointer;
      font-size: 14px;
      transition: all 0.3s ease;
      border-bottom: 3px solid transparent;
    `;
    
    tabButton.onclick = () => this.showTab(id);
    
    const tabNav = this.container?.querySelector('#tab-navigation');
    if (tabNav) {
      tabNav.appendChild(tabButton);
    }
  }
  
  private showTab(tabId: string): void {
    this.currentTab = tabId;
    
    // Update tab buttons
    const tabNav = this.container?.querySelector('#tab-navigation');
    if (tabNav) {
      const buttons = tabNav.querySelectorAll('button');
      buttons.forEach(button => {
        if (button.id === `tab-${tabId}`) {
          button.style.color = '#4CAF50';
          button.style.borderBottomColor = '#4CAF50';
          button.style.background = 'rgba(76, 175, 80, 0.1)';
        } else {
          button.style.color = '#ccc';
          button.style.borderBottomColor = 'transparent';
          button.style.background = 'transparent';
        }
      });
    }
    
    // Update content
    const contentArea = this.container?.querySelector('#content-area');
    if (contentArea) {
      contentArea.innerHTML = '';
      const tab = this.tabs.get(tabId);
      if (tab) {
        contentArea.appendChild(tab.content);
      }
    }
  }
  
  private createGeneralTab(): HTMLElement {
    const container = document.createElement('div');
    
    container.innerHTML = `
      <h3 style="color: #4CAF50; margin-bottom: 20px;">🎮 General Settings</h3>
      
      <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px;">
        <div class="setting-group">
          <label class="setting-label">Master Enable/Disable</label>
          <label class="toggle-switch">
            <input type="checkbox" id="master-enabled" ${this.config.enabled ? 'checked' : ''}>
            <span class="toggle-slider"></span>
          </label>
        </div>
        
        <div class="setting-group">
          <label class="setting-label">Debug Mode</label>
          <label class="toggle-switch">
            <input type="checkbox" id="debug-mode" ${this.config.debug ? 'checked' : ''}>
            <span class="toggle-slider"></span>
          </label>
        </div>
        
        <div class="setting-group">
          <label class="setting-label">Safe Mode</label>
          <label class="toggle-switch">
            <input type="checkbox" id="safe-mode" ${this.config.safeMode ? 'checked' : ''}>
            <span class="toggle-slider"></span>
          </label>
        </div>
        
        <div class="setting-group">
          <label class="setting-label">Update Interval (ms)</label>
          <input type="range" id="update-interval" min="50" max="1000" value="${this.config.updateInterval}" 
                 style="width: 100%; margin-top: 5px;">
          <span class="range-value">${this.config.updateInterval}ms</span>
        </div>
      </div>
      
      <div style="margin-top: 30px;">
        <h4 style="color: #4CAF50;">UI Settings</h4>
        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 15px; margin-top: 15px;">
          <div class="setting-group">
            <label class="setting-label">Show Overlay</label>
            <label class="toggle-switch">
              <input type="checkbox" id="show-overlay" ${this.config.ui.showOverlay ? 'checked' : ''}>
              <span class="toggle-slider"></span>
            </label>
          </div>
          
          <div class="setting-group">
            <label class="setting-label">Show Statistics</label>
            <label class="toggle-switch">
              <input type="checkbox" id="show-stats" ${this.config.ui.showStats ? 'checked' : ''}>
              <span class="toggle-slider"></span>
            </label>
          </div>
          
          <div class="setting-group">
            <label class="setting-label">Overlay Position</label>
            <select id="overlay-position" style="width: 100%; padding: 5px; background: #333; color: white; border: 1px solid #555;">
              <option value="top-left" ${this.config.ui.overlayPosition === 'top-left' ? 'selected' : ''}>Top Left</option>
              <option value="top-right" ${this.config.ui.overlayPosition === 'top-right' ? 'selected' : ''}>Top Right</option>
              <option value="bottom-left" ${this.config.ui.overlayPosition === 'bottom-left' ? 'selected' : ''}>Bottom Left</option>
              <option value="bottom-right" ${this.config.ui.overlayPosition === 'bottom-right' ? 'selected' : ''}>Bottom Right</option>
            </select>
          </div>
          
          <div class="setting-group">
            <label class="setting-label">Transparency</label>
            <input type="range" id="transparency" min="0.1" max="1" step="0.1" value="${this.config.ui.transparency}">
            <span class="range-value">${Math.round(this.config.ui.transparency * 100)}%</span>
          </div>
        </div>
      </div>
    `;
    
    this.addStyles(container);
    this.bindGeneralEvents(container);
    
    return container;
  }

  private createAutoHatsTab(): HTMLElement {
    const container = document.createElement('div');

    container.innerHTML = `
      <h3 style="color: #4CAF50; margin-bottom: 20px;">🎩 Auto Hats Configuration</h3>

      <div class="setting-group">
        <label class="setting-label">Enable Auto Hats</label>
        <label class="toggle-switch">
          <input type="checkbox" id="auto-hats-enabled" ${this.config.autoHats.enabled ? 'checked' : ''}>
          <span class="toggle-slider"></span>
        </label>
      </div>

      <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px; margin-top: 20px;">
        <div class="setting-group">
          <label class="setting-label">Auto Upgrade Hats</label>
          <label class="toggle-switch">
            <input type="checkbox" id="auto-upgrade-hats" ${this.config.autoHats.autoUpgrade ? 'checked' : ''}>
            <span class="toggle-slider"></span>
          </label>
        </div>

        <div class="setting-group">
          <label class="setting-label">Situational Switching</label>
          <label class="toggle-switch">
            <input type="checkbox" id="situational-switching" ${this.config.autoHats.switchBasedOnSituation ? 'checked' : ''}>
            <span class="toggle-slider"></span>
          </label>
        </div>
      </div>

      <div style="margin-top: 30px;">
        <h4 style="color: #4CAF50;">Hat Priority Order</h4>
        <p style="color: #ccc; font-size: 12px; margin-bottom: 15px;">Drag to reorder hat priorities (highest priority first)</p>
        <div id="hat-priority-list" style="max-height: 200px; overflow-y: auto; border: 1px solid #555; border-radius: 5px; padding: 10px;">
          ${this.config.autoHats.priority.map((hat, index) => `
            <div class="priority-item" data-hat="${hat}" style="padding: 8px; margin: 5px 0; background: #333; border-radius: 3px; cursor: move; display: flex; justify-content: space-between; align-items: center;">
              <span>${hat}</span>
              <span style="color: #4CAF50;">#${index + 1}</span>
            </div>
          `).join('')}
        </div>
      </div>

      <div style="margin-top: 30px;">
        <h4 style="color: #4CAF50;">Situational Hats</h4>
        <div style="display: grid; grid-template-columns: 1fr 1fr 1fr; gap: 15px;">
          <div class="setting-group">
            <label class="setting-label">Combat Hat</label>
            <select id="combat-hat" style="width: 100%; padding: 5px; background: #333; color: white; border: 1px solid #555;">
              ${this.config.autoHats.priority.map(hat => `
                <option value="${hat}" ${this.config.autoHats.combatHat === hat ? 'selected' : ''}>${hat}</option>
              `).join('')}
            </select>
          </div>

          <div class="setting-group">
            <label class="setting-label">Farming Hat</label>
            <select id="farming-hat" style="width: 100%; padding: 5px; background: #333; color: white; border: 1px solid #555;">
              ${this.config.autoHats.priority.map(hat => `
                <option value="${hat}" ${this.config.autoHats.farmingHat === hat ? 'selected' : ''}>${hat}</option>
              `).join('')}
            </select>
          </div>

          <div class="setting-group">
            <label class="setting-label">Exploration Hat</label>
            <select id="exploration-hat" style="width: 100%; padding: 5px; background: #333; color: white; border: 1px solid #555;">
              ${this.config.autoHats.priority.map(hat => `
                <option value="${hat}" ${this.config.autoHats.explorationHat === hat ? 'selected' : ''}>${hat}</option>
              `).join('')}
            </select>
          </div>
        </div>
      </div>
    `;

    this.addStyles(container);
    this.bindAutoHatsEvents(container);

    return container;
  }

  private createAutoBuyTab(): HTMLElement {
    const container = document.createElement('div');

    container.innerHTML = `
      <h3 style="color: #4CAF50; margin-bottom: 20px;">💰 Auto Buy Configuration</h3>

      <div class="setting-group">
        <label class="setting-label">Enable Auto Buy</label>
        <label class="toggle-switch">
          <input type="checkbox" id="auto-buy-enabled" ${this.config.autoBuy.enabled ? 'checked' : ''}>
          <span class="toggle-slider"></span>
        </label>
      </div>

      <div style="display: grid; grid-template-columns: 1fr 1fr 1fr; gap: 20px; margin-top: 20px;">
        <div class="setting-group">
          <label class="setting-label">Buy Hats</label>
          <label class="toggle-switch">
            <input type="checkbox" id="buy-hats" ${this.config.autoBuy.buyHats ? 'checked' : ''}>
            <span class="toggle-slider"></span>
          </label>
        </div>

        <div class="setting-group">
          <label class="setting-label">Buy Weapons</label>
          <label class="toggle-switch">
            <input type="checkbox" id="buy-weapons" ${this.config.autoBuy.buyWeapons ? 'checked' : ''}>
            <span class="toggle-slider"></span>
          </label>
        </div>

        <div class="setting-group">
          <label class="setting-label">Buy Upgrades</label>
          <label class="toggle-switch">
            <input type="checkbox" id="buy-upgrades" ${this.config.autoBuy.buyUpgrades ? 'checked' : ''}>
            <span class="toggle-slider"></span>
          </label>
        </div>
      </div>

      <div style="margin-top: 30px;">
        <h4 style="color: #4CAF50;">Spending Limits</h4>
        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px;">
          <div class="setting-group">
            <label class="setting-label">Max Spend Percentage</label>
            <input type="range" id="max-spend-percentage" min="10" max="100" value="${this.config.autoBuy.maxSpendPercentage}">
            <span class="range-value">${this.config.autoBuy.maxSpendPercentage}%</span>
          </div>
        </div>
      </div>

      <div style="margin-top: 30px;">
        <h4 style="color: #4CAF50;">Resource Reserves</h4>
        <div style="display: grid; grid-template-columns: 1fr 1fr 1fr 1fr; gap: 15px;">
          <div class="setting-group">
            <label class="setting-label">Wood Reserve</label>
            <input type="number" id="wood-reserve" value="${this.config.autoBuy.reserveResources.wood}" min="0" max="1000" style="width: 100%; padding: 5px; background: #333; color: white; border: 1px solid #555;">
          </div>

          <div class="setting-group">
            <label class="setting-label">Stone Reserve</label>
            <input type="number" id="stone-reserve" value="${this.config.autoBuy.reserveResources.stone}" min="0" max="1000" style="width: 100%; padding: 5px; background: #333; color: white; border: 1px solid #555;">
          </div>

          <div class="setting-group">
            <label class="setting-label">Food Reserve</label>
            <input type="number" id="food-reserve" value="${this.config.autoBuy.reserveResources.food}" min="0" max="1000" style="width: 100%; padding: 5px; background: #333; color: white; border: 1px solid #555;">
          </div>

          <div class="setting-group">
            <label class="setting-label">Gold Reserve</label>
            <input type="number" id="gold-reserve" value="${this.config.autoBuy.reserveResources.gold}" min="0" max="1000" style="width: 100%; padding: 5px; background: #333; color: white; border: 1px solid #555;">
          </div>
        </div>
      </div>
    `;

    this.addStyles(container);
    this.bindAutoBuyEvents(container);

    return container;
  }

  private createAutoMillsTab(): HTMLElement {
    const container = document.createElement('div');
    container.innerHTML = `<h3 style="color: #4CAF50;">🏭 Auto Mills - Coming Soon</h3>`;
    return container;
  }

  private createBotSystemTab(): HTMLElement {
    const container = document.createElement('div');
    container.innerHTML = `<h3 style="color: #4CAF50;">🤖 Bot System - Coming Soon</h3>`;
    return container;
  }

  private createSafetyTab(): HTMLElement {
    const container = document.createElement('div');
    container.innerHTML = `<h3 style="color: #4CAF50;">🛡️ Safety - Coming Soon</h3>`;
    return container;
  }

  private createPresetsTab(): HTMLElement {
    const container = document.createElement('div');
    container.innerHTML = `<h3 style="color: #4CAF50;">📋 Presets - Coming Soon</h3>`;
    return container;
  }

  private addStyles(container: HTMLElement): void {
    const style = document.createElement('style');
    style.textContent = `
      .setting-group {
        margin-bottom: 15px;
      }

      .setting-label {
        display: block;
        margin-bottom: 5px;
        font-weight: 500;
        color: #ccc;
      }

      .toggle-switch {
        position: relative;
        display: inline-block;
        width: 50px;
        height: 24px;
      }

      .toggle-switch input {
        opacity: 0;
        width: 0;
        height: 0;
      }

      .toggle-slider {
        position: absolute;
        cursor: pointer;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background-color: #333;
        transition: .4s;
        border-radius: 24px;
      }

      .toggle-slider:before {
        position: absolute;
        content: "";
        height: 18px;
        width: 18px;
        left: 3px;
        bottom: 3px;
        background-color: white;
        transition: .4s;
        border-radius: 50%;
      }

      input:checked + .toggle-slider {
        background-color: #4CAF50;
      }

      input:checked + .toggle-slider:before {
        transform: translateX(26px);
      }

      .range-value {
        color: #4CAF50;
        font-weight: bold;
        margin-left: 10px;
      }

      .priority-item {
        user-select: none;
      }

      .priority-item:hover {
        background: #444 !important;
      }
    `;

    if (!document.head.querySelector('#mod-menu-styles')) {
      style.id = 'mod-menu-styles';
      document.head.appendChild(style);
    }
  }

  private bindGeneralEvents(container: HTMLElement): void {
    // Add event listeners for general settings
    const masterEnabled = container.querySelector('#master-enabled') as HTMLInputElement;
    if (masterEnabled) {
      masterEnabled.addEventListener('change', () => {
        this.emit('configChanged', { enabled: masterEnabled.checked });
      });
    }
  }

  private bindAutoHatsEvents(container: HTMLElement): void {
    // Add event listeners for auto hats settings
  }

  private bindAutoBuyEvents(container: HTMLElement): void {
    // Add event listeners for auto buy settings
  }

  public show(): void {
    if (this.container) {
      this.container.style.display = 'block';
      this.isVisible = true;
    }
  }

  public hide(): void {
    if (this.container) {
      this.container.style.display = 'none';
      this.isVisible = false;
    }
  }

  public toggle(): void {
    if (this.isVisible) {
      this.hide();
    } else {
      this.show();
    }
  }

  public updateConfig(config: ModConfig): void {
    this.config = config;
    // Update UI elements to reflect new config
  }

  private setupEventListeners(): void {
    // Global event listeners
    document.addEventListener('keydown', (event) => {
      if (event.key === 'Escape' && this.isVisible) {
        this.hide();
      }
    });
  }
}
