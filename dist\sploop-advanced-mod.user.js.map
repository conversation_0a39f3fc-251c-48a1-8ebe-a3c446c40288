{"version": 3, "file": "sploop-advanced-mod.user.js", "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD,O;;;;;;;ACVa;;AAEb;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,WAAW,UAAU;AACrB,WAAW,GAAG;AACd,WAAW,SAAS;AACpB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,WAAW,cAAc;AACzB,WAAW,iBAAiB;AAC5B,WAAW,UAAU;AACrB,WAAW,GAAG;AACd,WAAW,SAAS;AACpB,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA,WAAW,cAAc;AACzB,WAAW,iBAAiB;AAC5B;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA,WAAW,iBAAiB;AAC5B,aAAa,OAAO;AACpB;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA,0DAA0D,OAAO;AACjE;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA,WAAW,iBAAiB;AAC5B,aAAa,QAAQ;AACrB;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,WAAW,iBAAiB;AAC5B,aAAa,SAAS;AACtB;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,0CAA0C,SAAS;AACnD;AACA;;AAEA;AACA,IAAI;AACJ;AACA;;AAEA,gBAAgB,YAAY;AAC5B;;AAEA;AACA,4DAA4D;AAC5D,gEAAgE;AAChE,oEAAoE;AACpE,wEAAwE;AACxE;AACA,2DAA2D,SAAS;AACpE;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA,WAAW,iBAAiB;AAC5B,WAAW,UAAU;AACrB,WAAW,GAAG;AACd,aAAa,cAAc;AAC3B;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,WAAW,iBAAiB;AAC5B,WAAW,UAAU;AACrB,WAAW,GAAG;AACd,aAAa,cAAc;AAC3B;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,WAAW,iBAAiB;AAC5B,WAAW,UAAU;AACrB,WAAW,GAAG;AACd,WAAW,SAAS;AACpB,aAAa,cAAc;AAC3B;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAI;AACJ,4DAA4D,YAAY;AACxE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA,WAAW,iBAAiB;AAC5B,aAAa,cAAc;AAC3B;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,IAAI;AACJ;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,IAAI,IAA6B;AACjC;AACA;;;;;;;UC/UA;UACA;;UAEA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;;UAEA;UACA;;UAEA;UACA;UACA;;;;;WCtBA;WACA;WACA;WACA;WACA,yCAAyC,wCAAwC;WACjF;WACA;WACA,E;;;;;WCPA,wF;;;;;WCAA;WACA;WACA;WACA,uDAAuD,iBAAiB;WACxE;WACA,gDAAgD,aAAa;WAC7D,E;;;;;;;;;;;;;;ACNA,kCAAkC;AAkFlC,IAAY,QAMX;AAND,WAAY,QAAQ;IAClB,6BAAiB;IACjB,uBAAW;IACX,yBAAa;IACb,iCAAqB;IACrB,yBAAa;AACf,CAAC,EANW,QAAQ,KAAR,QAAQ,QAMnB;AAED,IAAY,OAeX;AAfD,WAAY,OAAO;IACjB,wBAAa;IACb,gCAAqB;IACrB,wBAAa;IACb,kCAAuB;IACvB,4BAAiB;IACjB,8BAAmB;IACnB,0BAAe;IACf,4BAAiB;IACjB,4BAAiB;IACjB,sBAAW;IACX,4BAAiB;IACjB,8BAAmB;IACnB,0BAAe;IACf,0BAAe;AACjB,CAAC,EAfW,OAAO,KAAP,OAAO,QAelB;AAED,IAAY,YAcX;AAdD,WAAY,YAAY;IACtB,6BAAa;IACb,+BAAe;IACf,6BAAa;IACb,qCAAqB;IACrB,6BAAa;IACb,qCAAqB;IACrB,uCAAuB;IACvB,iCAAiB;IACjB,qCAAqB;IACrB,2CAA2B;IAC3B,uCAAuB;IACvB,mCAAmB;IACnB,yCAAyB;AAC3B,CAAC,EAdW,YAAY,KAAZ,YAAY,QAcvB;AAED,IAAY,YAKX;AALD,WAAY,YAAY;IACtB,6BAAa;IACb,+BAAe;IACf,6BAAa;IACb,6BAAa;AACf,CAAC,EALW,YAAY,KAAZ,YAAY,QAKvB;AA+CD,IAAY,UAWX;AAXD,WAAY,UAAU;IACpB,2BAAa;IACb,+BAAiB;IACjB,6BAAe;IACf,iCAAmB;IACnB,yBAAW;IACX,6BAAe;IACf,mCAAqB;IACrB,2BAAa;IACb,qCAAuB;IACvB,uCAAyB;AAC3B,CAAC,EAXW,UAAU,KAAV,UAAU,QAWrB;;;;;AC1LoC;;AAEd;AACvB,iEAAe,4DAAY;;;ACH0E;AACxD;AActC,MAAM,aAAc,SAAQ,aAAkC;IAMnE;QACE,KAAK,EAAE,CAAC;QANF,cAAS,GAAqB,IAAI,CAAC;QACnC,mBAAc,GAAkB,IAAI,CAAC;QACrC,mBAAc,GAAG,CAAC,CAAC;QACnB,gBAAW,GAAiB,EAAE,CAAC;QAIrC,IAAI,CAAC,UAAU,EAAE,CAAC;IACpB,CAAC;IAEO,UAAU;QAChB,wBAAwB;QACxB,MAAM,SAAS,GAAG,GAAG,EAAE;YACrB,IAAI,IAAI,CAAC,YAAY,EAAE,EAAE,CAAC;gBACxB,IAAI,CAAC,eAAe,EAAE,CAAC;gBACvB,KAAK,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;YAC3B,CAAC;iBAAM,CAAC;gBACN,UAAU,CAAC,SAAS,EAAE,IAAI,CAAC,CAAC;YAC9B,CAAC;QACH,CAAC,CAAC;QACF,SAAS,EAAE,CAAC;IACd,CAAC;IAEO,YAAY;QAClB,OAAO,OAAO,MAAM,KAAK,WAAW;YAC7B,MAAM,CAAC,IAAI;YACX,MAAM,CAAC,MAAM;YACb,MAAM,CAAC,MAAM,CAAC;IACvB,CAAC;IAEO,eAAe;QACrB,IAAI,IAAI,CAAC,cAAc,EAAE,CAAC;YACxB,aAAa,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;QACrC,CAAC;QAED,IAAI,CAAC,cAAc,GAAG,MAAM,CAAC,WAAW,CAAC,GAAG,EAAE;YAC5C,IAAI,CAAC,eAAe,EAAE,CAAC;YACvB,IAAI,CAAC,kBAAkB,EAAE,CAAC;QAC5B,CAAC,EAAE,EAAE,CAAC,CAAC;IACT,CAAC;IAEO,eAAe;QACrB,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE;YAAE,OAAO;QAEjC,IAAI,CAAC;YACH,MAAM,QAAQ,GAAc;gBAC1B,MAAM,EAAE,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC,MAAM,CAAC;gBACvC,OAAO,EAAE,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,OAAO,CAAC;gBAC1C,SAAS,EAAE,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,SAAS,CAAC;gBAChD,KAAK,EAAE,IAAI,GAAG,EAAE;gBAChB,WAAW,EAAE,IAAI,GAAG,EAAE;gBACtB,QAAQ,EAAE,IAAI,CAAC,QAAQ,EAAE;gBACzB,QAAQ,EAAE,MAAM,CAAC,IAAI,EAAE,QAAQ,IAAI,SAAS;gBAC5C,UAAU,EAAE;oBACV,MAAM,EAAE,MAAM,CAAC,IAAI,EAAE,MAAM,IAAI,SAAS;oBACxC,WAAW,EAAE,MAAM,CAAC,OAAO,EAAE,MAAM,IAAI,CAAC;oBACxC,UAAU,EAAE,MAAM,CAAC,IAAI,EAAE,UAAU,IAAI,CAAC;oBACxC,QAAQ,EAAE,MAAM,CAAC,IAAI,EAAE,QAAQ,IAAI,SAAS;oBAC5C,OAAO,EAAE,MAAM,CAAC,IAAI,EAAE,OAAO,IAAI,CAAC;iBACnC;aACF,CAAC;YAEF,IAAI,CAAC,SAAS,GAAG,QAAQ,CAAC;YAC1B,KAAK,CAAC,IAAI,CAAC,aAAa,EAAE,QAAQ,CAAC,CAAC;QACtC,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,4BAA4B,EAAE,KAAK,CAAC,CAAC;QACrD,CAAC;IACH,CAAC;IAEO,WAAW,CAAC,UAAe;QACjC,OAAO;YACL,EAAE,EAAE,UAAU,EAAE,EAAE,IAAI,EAAE;YACxB,IAAI,EAAE,UAAU,EAAE,IAAI,IAAI,EAAE;YAC5B,QAAQ,EAAE,EAAE,CAAC,EAAE,UAAU,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,UAAU,EAAE,CAAC,IAAI,CAAC,EAAE;YAC1D,MAAM,EAAE,UAAU,EAAE,MAAM,IAAI,CAAC;YAC/B,SAAS,EAAE,UAAU,EAAE,SAAS,IAAI,GAAG;YACvC,SAAS,EAAE;gBACT,IAAI,EAAE,UAAU,EAAE,IAAI,IAAI,CAAC;gBAC3B,KAAK,EAAE,UAAU,EAAE,KAAK,IAAI,CAAC;gBAC7B,IAAI,EAAE,UAAU,EAAE,IAAI,IAAI,CAAC;gBAC3B,IAAI,EAAE,UAAU,EAAE,IAAI,IAAI,CAAC;gBAC3B,MAAM,EAAE,UAAU,EAAE,MAAM,IAAI,CAAC;aAChC;YACD,SAAS,EAAE,UAAU,EAAE,SAAS,IAAI,EAAE;YACtC,GAAG,EAAE,UAAU,EAAE,GAAG,IAAI,IAAI;YAC5B,MAAM,EAAE,UAAU,EAAE,MAAM,IAAI,IAAI;YAClC,KAAK,EAAE,UAAU,EAAE,KAAK,IAAI,CAAC;YAC7B,UAAU,EAAE,UAAU,EAAE,UAAU,IAAI,CAAC;YACvC,IAAI,EAAE,UAAU,EAAE,IAAI,IAAI,IAAI;YAC9B,KAAK,EAAE,KAAK;SACb,CAAC;IACJ,CAAC;IAEO,YAAY,CAAC,WAAgB;QACnC,MAAM,OAAO,GAAG,IAAI,GAAG,EAAkB,CAAC;QAE1C,IAAI,KAAK,CAAC,OAAO,CAAC,WAAW,CAAC,EAAE,CAAC;YAC/B,WAAW,CAAC,OAAO,CAAC,CAAC,UAAe,EAAE,EAAE;gBACtC,MAAM,MAAM,GAAG,IAAI,CAAC,WAAW,CAAC,UAAU,CAAC,CAAC;gBAC5C,OAAO,CAAC,GAAG,CAAC,MAAM,CAAC,EAAE,EAAE,MAAM,CAAC,CAAC;YACjC,CAAC,CAAC,CAAC;QACL,CAAC;QAED,OAAO,OAAO,CAAC;IACjB,CAAC;IAEO,cAAc,CAAC,aAAkB;QACvC,MAAM,SAAS,GAAG,IAAI,GAAG,EAAoB,CAAC;QAE9C,IAAI,KAAK,CAAC,OAAO,CAAC,aAAa,CAAC,EAAE,CAAC;YACjC,aAAa,CAAC,OAAO,CAAC,CAAC,YAAiB,EAAE,EAAE;gBAC1C,MAAM,QAAQ,GAAa;oBACzB,EAAE,EAAE,YAAY,EAAE,EAAE,IAAI,EAAE;oBAC1B,IAAI,EAAE,YAAY,EAAE,IAAI,IAAI,SAAS;oBACrC,QAAQ,EAAE,EAAE,CAAC,EAAE,YAAY,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,YAAY,EAAE,CAAC,IAAI,CAAC,EAAE;oBAC9D,MAAM,EAAE,YAAY,EAAE,MAAM,IAAI,CAAC;oBACjC,SAAS,EAAE,YAAY,EAAE,SAAS,IAAI,GAAG;oBACzC,KAAK,EAAE,YAAY,EAAE,KAAK,IAAI,EAAE;oBAChC,KAAK,EAAE,YAAY,EAAE,KAAK,IAAI,CAAC;oBAC/B,QAAQ,EAAE,YAAY,EAAE,QAAQ,IAAI,KAAK;iBAC1C,CAAC;gBACF,SAAS,CAAC,GAAG,CAAC,QAAQ,CAAC,EAAE,EAAE,QAAQ,CAAC,CAAC;YACvC,CAAC,CAAC,CAAC;QACL,CAAC;QAED,OAAO,SAAS,CAAC;IACnB,CAAC;IAEM,SAAS;QACd,OAAO,IAAI,CAAC,SAAS,EAAE,MAAM,IAAI,IAAI,CAAC;IACxC,CAAC;IAEM,UAAU;QACf,OAAO,IAAI,CAAC,SAAS,EAAE,OAAO,IAAI,IAAI,GAAG,EAAE,CAAC;IAC9C,CAAC;IAEM,YAAY;QACjB,OAAO,IAAI,CAAC,SAAS,EAAE,SAAS,IAAI,IAAI,GAAG,EAAE,CAAC;IAChD,CAAC;IAEM,YAAY;QACjB,OAAO,IAAI,CAAC,SAAS,IAAI;YACvB,MAAM,EAAE,EAAY;YACpB,OAAO,EAAE,IAAI,GAAG,EAAE;YAClB,SAAS,EAAE,IAAI,GAAG,EAAE;YACpB,KAAK,EAAE,IAAI,GAAG,EAAE;YAChB,WAAW,EAAE,IAAI,GAAG,EAAE;YACtB,QAAQ,EAAE,KAAK;YACf,QAAQ,EAAE,SAAS;YACnB,UAAU,EAAE;gBACV,MAAM,EAAE,SAAS;gBACjB,WAAW,EAAE,CAAC;gBACd,UAAU,EAAE,CAAC;gBACb,QAAQ,EAAE,SAAS;gBACnB,OAAO,EAAE,CAAC;aACX;SACF,CAAC;IACJ,CAAC;IAEM,UAAU,CAAC,MAAkB;QAClC,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;IAChC,CAAC;IAEO,kBAAkB;QACxB,IAAI,IAAI,CAAC,WAAW,CAAC,MAAM,KAAK,CAAC;YAAE,OAAO;QAE1C,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QACvB,IAAI,GAAG,GAAG,IAAI,CAAC,cAAc,GAAG,GAAG;YAAE,OAAO,CAAC,gBAAgB;QAE7D,MAAM,MAAM,GAAG,IAAI,CAAC,WAAW,CAAC,KAAK,EAAE,CAAC;QACxC,IAAI,CAAC,MAAM;YAAE,OAAO;QAEpB,IAAI,CAAC;YACH,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC;YAC3B,IAAI,CAAC,cAAc,GAAG,GAAG,CAAC;QAC5B,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,yBAAyB,EAAE,KAAK,CAAC,CAAC;QAClD,CAAC;IACH,CAAC;IAEO,aAAa,CAAC,MAAkB;QACtC,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE,IAAI,CAAC,MAAM,CAAC,MAAM;YAAE,OAAO;QAEnD,QAAQ,MAAM,CAAC,IAAI,EAAE,CAAC;YACpB,KAAK,UAAU,CAAC,IAAI;gBAClB,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,EAAE,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;gBACxC,MAAM;YACR,KAAK,UAAU,CAAC,MAAM;gBACpB,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;gBAC/B,MAAM;YACR,KAAK,UAAU,CAAC,KAAK;gBACnB,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,EAAE,MAAM,CAAC,IAAI,CAAC,CAAC,EAAE,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;gBAC3D,MAAM;YACR,KAAK,UAAU,CAAC,GAAG;gBACjB,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;gBAC7B,MAAM;YACR,KAAK,UAAU,CAAC,KAAK;gBACnB,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;gBAC/B,MAAM;YACR,KAAK,UAAU,CAAC,IAAI;gBAClB,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;gBACnC,MAAM;YACR;gBACE,OAAO,CAAC,IAAI,CAAC,sBAAsB,EAAE,MAAM,CAAC,IAAI,CAAC,CAAC;QACtD,CAAC;IACH,CAAC;IAEO,IAAI,CAAC,CAAS,EAAE,CAAS;QAC/B,IAAI,MAAM,CAAC,MAAM,IAAI,MAAM,CAAC,MAAM,CAAC,IAAI,EAAE,CAAC;YACxC,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;QACvC,CAAC;IACH,CAAC;IAEO,MAAM,CAAC,KAAa;QAC1B,IAAI,MAAM,CAAC,MAAM,IAAI,MAAM,CAAC,MAAM,CAAC,IAAI,EAAE,CAAC;YACxC,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,QAAQ,EAAE,EAAE,KAAK,EAAE,CAAC,CAAC;QAC1C,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,IAAY,EAAE,CAAS,EAAE,CAAS;QAC9C,IAAI,MAAM,CAAC,MAAM,IAAI,MAAM,CAAC,MAAM,CAAC,IAAI,EAAE,CAAC;YACxC,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,OAAO,EAAE,EAAE,IAAI,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;QAC9C,CAAC;IACH,CAAC;IAEO,GAAG,CAAC,MAAc;QACxB,IAAI,MAAM,CAAC,MAAM,IAAI,MAAM,CAAC,MAAM,CAAC,IAAI,EAAE,CAAC;YACxC,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,EAAE,EAAE,MAAM,EAAE,CAAC,CAAC;QACxC,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,MAAc;QAC1B,IAAI,MAAM,CAAC,MAAM,IAAI,MAAM,CAAC,MAAM,CAAC,IAAI,EAAE,CAAC;YACxC,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,OAAO,EAAE,EAAE,MAAM,EAAE,CAAC,CAAC;QAC1C,CAAC;IACH,CAAC;IAEM,QAAQ,CAAC,OAAe;QAC7B,IAAI,MAAM,CAAC,MAAM,IAAI,MAAM,CAAC,MAAM,CAAC,IAAI,EAAE,CAAC;YACxC,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,EAAE,EAAE,OAAO,EAAE,CAAC,CAAC;QAC1C,CAAC;IACH,CAAC;IAEM,QAAQ;QACb,OAAO,IAAI,CAAC,YAAY,EAAE;YACnB,MAAM,CAAC,IAAI,EAAE,MAAM,KAAK,IAAI;YAC5B,IAAI,CAAC,SAAS,EAAE,MAAM,EAAE,EAAE,KAAK,EAAE,CAAC;IAC3C,CAAC;IAEM,iBAAiB,CAAC,IAAa,EAAE,IAAa;QACnD,MAAM,EAAE,GAAG,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC;QAC3B,MAAM,EAAE,GAAG,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC;QAC3B,OAAO,IAAI,CAAC,IAAI,CAAC,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC;IACtC,CAAC;IAEM,cAAc,CAAC,IAAa,EAAE,EAAW;QAC9C,OAAO,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC;IAClD,CAAC;IAEM,OAAO;QACZ,IAAI,IAAI,CAAC,cAAc,EAAE,CAAC;YACxB,aAAa,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;YACnC,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC;QAC7B,CAAC;QACD,KAAK,CAAC,kBAAkB,EAAE,CAAC;IAC7B,CAAC;CACF;;;AC1RoD;AA+F9C,MAAM,cAAc,GAAc;IACvC,OAAO,EAAE,IAAI;IACb,KAAK,EAAE,KAAK;IACZ,QAAQ,EAAE,IAAI;IACd,cAAc,EAAE,GAAG;IAEnB,QAAQ,EAAE;QACR,OAAO,EAAE,IAAI;QACb,QAAQ,EAAE;YACR,OAAO,CAAC,KAAK;YACb,OAAO,CAAC,KAAK;YACb,OAAO,CAAC,OAAO;YACf,OAAO,CAAC,MAAM;YACd,OAAO,CAAC,MAAM;YACd,OAAO,CAAC,OAAO;YACf,OAAO,CAAC,SAAS;YACjB,OAAO,CAAC,QAAQ;SACjB;QACD,WAAW,EAAE,IAAI;QACjB,sBAAsB,EAAE,IAAI;QAC5B,SAAS,EAAE,OAAO,CAAC,SAAS;QAC5B,UAAU,EAAE,OAAO,CAAC,MAAM;QAC1B,cAAc,EAAE,OAAO,CAAC,IAAI;KAC7B;IAED,OAAO,EAAE;QACP,OAAO,EAAE,IAAI;QACb,aAAa,EAAE;YACb,gBAAgB;YAChB,aAAa;YACb,eAAe;YACf,aAAa;SACd;QACD,kBAAkB,EAAE,EAAE;QACtB,gBAAgB,EAAE;YAChB,IAAI,EAAE,GAAG;YACT,KAAK,EAAE,GAAG;YACV,IAAI,EAAE,EAAE;YACR,IAAI,EAAE,EAAE;SACT;QACD,OAAO,EAAE,IAAI;QACb,UAAU,EAAE,IAAI;QAChB,WAAW,EAAE,IAAI;KAClB;IAED,SAAS,EAAE;QACT,OAAO,EAAE,IAAI;QACb,QAAQ,EAAE,CAAC;QACX,gBAAgB,EAAE;YAChB,YAAY,CAAC,IAAI;YACjB,YAAY,CAAC,KAAK;YAClB,YAAY,CAAC,IAAI;YACjB,YAAY,CAAC,IAAI;SAClB;QACD,WAAW,EAAE,IAAI;QACjB,gBAAgB,EAAE,IAAI;QACtB,YAAY,EAAE,IAAI;QAClB,WAAW,EAAE,GAAG;QAChB,kBAAkB,EAAE;YAClB,aAAa,EAAE,IAAI;YACnB,QAAQ,EAAE,KAAK;YACf,WAAW,EAAE,IAAI;SAClB;KACF;IAED,SAAS,EAAE;QACT,OAAO,EAAE,KAAK,EAAE,iCAAiC;QACjD,OAAO,EAAE,CAAC;QACV,YAAY,EAAE;YACZ,OAAO,EAAE,IAAI;YACb,MAAM,EAAE,KAAK;YACb,QAAQ,EAAE,IAAI;YACd,WAAW,EAAE,IAAI;YACjB,UAAU,EAAE,IAAI;SACjB;QACD,YAAY,EAAE;YACZ,cAAc,EAAE,IAAI;YACpB,aAAa,EAAE,KAAK;YACpB,cAAc,EAAE,IAAI;SACrB;QACD,YAAY,EAAE;YACZ,YAAY,EAAE,IAAI;YAClB,gBAAgB,EAAE,IAAI;YACtB,gBAAgB,EAAE,IAAI;SACvB;KACF;IAED,MAAM,EAAE;QACN,aAAa,EAAE,IAAI;QACnB,gBAAgB,EAAE,IAAI;QACtB,iBAAiB,EAAE,IAAI;QACvB,aAAa,EAAE,IAAI;QACnB,aAAa,EAAE,cAAc;QAC7B,mBAAmB,EAAE,CAAC;KACvB;IAED,EAAE,EAAE;QACF,WAAW,EAAE,IAAI;QACjB,SAAS,EAAE,IAAI;QACf,QAAQ,EAAE,KAAK;QACf,eAAe,EAAE,WAAW;QAC5B,YAAY,EAAE,GAAG;KAClB;CACF,CAAC;AAEK,MAAM,aAAa;IAIxB;QAFiB,gBAAW,GAAG,4BAA4B,CAAC;QAG1D,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,UAAU,EAAE,CAAC;IAClC,CAAC;IAEM,SAAS;QACd,OAAO,EAAE,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC;IAC5B,CAAC;IAEM,YAAY,CAAC,OAA2B;QAC7C,IAAI,CAAC,MAAM,GAAG,EAAE,GAAG,IAAI,CAAC,MAAM,EAAE,GAAG,OAAO,EAAE,CAAC;QAC7C,IAAI,CAAC,UAAU,EAAE,CAAC;IACpB,CAAC;IAEM,WAAW;QAChB,IAAI,CAAC,MAAM,GAAG,EAAE,GAAG,cAAc,EAAE,CAAC;QACpC,IAAI,CAAC,UAAU,EAAE,CAAC;IACpB,CAAC;IAEO,UAAU;QAChB,IAAI,CAAC;YACH,MAAM,MAAM,GAAG,YAAY,CAAC,OAAO,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;YACtD,IAAI,MAAM,EAAE,CAAC;gBACX,MAAM,MAAM,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC;gBAClC,OAAO,EAAE,GAAG,cAAc,EAAE,GAAG,MAAM,EAAE,CAAC;YAC1C,CAAC;QACH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,IAAI,CAAC,wCAAwC,EAAE,KAAK,CAAC,CAAC;QAChE,CAAC;QACD,OAAO,EAAE,GAAG,cAAc,EAAE,CAAC;IAC/B,CAAC;IAEO,UAAU;QAChB,IAAI,CAAC;YACH,YAAY,CAAC,OAAO,CAAC,IAAI,CAAC,WAAW,EAAE,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC;QACtE,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,wBAAwB,EAAE,KAAK,CAAC,CAAC;QACjD,CAAC;IACH,CAAC;CACF;;;AChP+D;AACnB;AAmBtC,MAAM,cAAe,SAAQ,aAAY;IAwJ9C,YAAY,OAAsB,EAAE,MAAiB;QACnD,KAAK,EAAE,CAAC;QAtJF,YAAO,GAAG,KAAK,CAAC;QAChB,mBAAc,GAAkB,IAAI,CAAC;QACrC,iBAAY,GAAG,CAAC,CAAC;QACjB,qBAAgB,GAAkD,MAAM,CAAC;QAEjF,oCAAoC;QACnB,iBAAY,GAA6B;YACxD,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE;gBACd,EAAE,EAAE,UAAU;gBACd,IAAI,EAAE,OAAO,CAAC,IAAI;gBAClB,IAAI,EAAE,QAAQ;gBACd,IAAI,EAAE,CAAC;gBACP,WAAW,EAAE,CAAC;gBACd,KAAK,EAAE,IAAI;gBACX,QAAQ,EAAE,KAAK;gBACf,KAAK,EAAE,EAAE,WAAW,EAAE,CAAC,EAAE,UAAU,EAAE,CAAC,EAAE,WAAW,EAAE,CAAC,EAAE,aAAa,EAAE,CAAC,EAAE,eAAe,EAAE,CAAC,EAAE;aAC/F;YACD,CAAC,OAAO,CAAC,QAAQ,CAAC,EAAE;gBAClB,EAAE,EAAE,cAAc;gBAClB,IAAI,EAAE,OAAO,CAAC,QAAQ;gBACtB,IAAI,EAAE,cAAc;gBACpB,IAAI,EAAE,IAAI;gBACV,WAAW,EAAE,CAAC;gBACd,KAAK,EAAE,KAAK;gBACZ,QAAQ,EAAE,KAAK;gBACf,KAAK,EAAE,EAAE,WAAW,EAAE,CAAC,EAAE,UAAU,EAAE,CAAC,EAAE,WAAW,EAAE,EAAE,EAAE,aAAa,EAAE,CAAC,EAAE,eAAe,EAAE,CAAC,EAAE;aAChG;YACD,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE;gBACd,EAAE,EAAE,UAAU;gBACd,IAAI,EAAE,OAAO,CAAC,IAAI;gBAClB,IAAI,EAAE,UAAU;gBAChB,IAAI,EAAE,IAAI;gBACV,WAAW,EAAE,CAAC;gBACd,KAAK,EAAE,KAAK;gBACZ,QAAQ,EAAE,KAAK;gBACf,KAAK,EAAE,EAAE,WAAW,EAAE,EAAE,EAAE,UAAU,EAAE,CAAC,EAAE,WAAW,EAAE,CAAC,EAAE,aAAa,EAAE,CAAC,EAAE,eAAe,EAAE,CAAC,EAAE;aAChG;YACD,CAAC,OAAO,CAAC,SAAS,CAAC,EAAE;gBACnB,EAAE,EAAE,eAAe;gBACnB,IAAI,EAAE,OAAO,CAAC,SAAS;gBACvB,IAAI,EAAE,eAAe;gBACrB,IAAI,EAAE,KAAK;gBACX,WAAW,EAAE,CAAC;gBACd,KAAK,EAAE,KAAK;gBACZ,QAAQ,EAAE,KAAK;gBACf,KAAK,EAAE,EAAE,WAAW,EAAE,CAAC,EAAE,UAAU,EAAE,EAAE,EAAE,WAAW,EAAE,EAAE,EAAE,aAAa,EAAE,CAAC,EAAE,eAAe,EAAE,CAAC,EAAE;aACjG;YACD,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE;gBAChB,EAAE,EAAE,YAAY;gBAChB,IAAI,EAAE,OAAO,CAAC,MAAM;gBACpB,IAAI,EAAE,YAAY;gBAClB,IAAI,EAAE,KAAK;gBACX,WAAW,EAAE,CAAC;gBACd,KAAK,EAAE,KAAK;gBACZ,QAAQ,EAAE,KAAK;gBACf,KAAK,EAAE,EAAE,WAAW,EAAE,EAAE,EAAE,UAAU,EAAE,CAAC,EAAE,WAAW,EAAE,CAAC,EAAE,aAAa,EAAE,EAAE,EAAE,eAAe,EAAE,CAAC,EAAE;aACjG;YACD,CAAC,OAAO,CAAC,OAAO,CAAC,EAAE;gBACjB,EAAE,EAAE,aAAa;gBACjB,IAAI,EAAE,OAAO,CAAC,OAAO;gBACrB,IAAI,EAAE,aAAa;gBACnB,IAAI,EAAE,KAAK;gBACX,WAAW,EAAE,EAAE;gBACf,KAAK,EAAE,KAAK;gBACZ,QAAQ,EAAE,KAAK;gBACf,KAAK,EAAE,EAAE,WAAW,EAAE,CAAC,EAAE,UAAU,EAAE,CAAC,EAAE,WAAW,EAAE,CAAC,EAAE,aAAa,EAAE,EAAE,EAAE,eAAe,EAAE,EAAE,EAAE;aACjG;YACD,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE;gBACf,EAAE,EAAE,WAAW;gBACf,IAAI,EAAE,OAAO,CAAC,KAAK;gBACnB,IAAI,EAAE,WAAW;gBACjB,IAAI,EAAE,KAAK;gBACX,WAAW,EAAE,EAAE;gBACf,KAAK,EAAE,KAAK;gBACZ,QAAQ,EAAE,KAAK;gBACf,KAAK,EAAE,EAAE,WAAW,EAAE,EAAE,EAAE,UAAU,EAAE,EAAE,EAAE,WAAW,EAAE,CAAC,EAAE,aAAa,EAAE,CAAC,EAAE,eAAe,EAAE,CAAC,EAAE;aACjG;YACD,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE;gBAChB,EAAE,EAAE,YAAY;gBAChB,IAAI,EAAE,OAAO,CAAC,MAAM;gBACpB,IAAI,EAAE,YAAY;gBAClB,IAAI,EAAE,KAAK;gBACX,WAAW,EAAE,EAAE;gBACf,KAAK,EAAE,KAAK;gBACZ,QAAQ,EAAE,KAAK;gBACf,KAAK,EAAE,EAAE,WAAW,EAAE,EAAE,EAAE,UAAU,EAAE,CAAC,EAAE,WAAW,EAAE,EAAE,EAAE,aAAa,EAAE,CAAC,EAAE,eAAe,EAAE,EAAE,EAAE;aAClG;YACD,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE;gBAChB,EAAE,EAAE,YAAY;gBAChB,IAAI,EAAE,OAAO,CAAC,MAAM;gBACpB,IAAI,EAAE,YAAY;gBAClB,IAAI,EAAE,IAAI;gBACV,WAAW,EAAE,CAAC;gBACd,KAAK,EAAE,KAAK;gBACZ,QAAQ,EAAE,KAAK;gBACf,KAAK,EAAE,EAAE,WAAW,EAAE,CAAC,EAAE,UAAU,EAAE,EAAE,EAAE,WAAW,EAAE,CAAC,EAAE,aAAa,EAAE,EAAE,EAAE,eAAe,EAAE,CAAC,EAAE;aACjG;YACD,CAAC,OAAO,CAAC,GAAG,CAAC,EAAE;gBACb,EAAE,EAAE,SAAS;gBACb,IAAI,EAAE,OAAO,CAAC,GAAG;gBACjB,IAAI,EAAE,SAAS;gBACf,IAAI,EAAE,KAAK;gBACX,WAAW,EAAE,CAAC;gBACd,KAAK,EAAE,KAAK;gBACZ,QAAQ,EAAE,KAAK;gBACf,KAAK,EAAE,EAAE,WAAW,EAAE,CAAC,EAAE,UAAU,EAAE,CAAC,EAAE,WAAW,EAAE,EAAE,EAAE,aAAa,EAAE,EAAE,EAAE,eAAe,EAAE,CAAC,EAAE;aACjG;YACD,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE;gBAChB,EAAE,EAAE,YAAY;gBAChB,IAAI,EAAE,OAAO,CAAC,MAAM;gBACpB,IAAI,EAAE,YAAY;gBAClB,IAAI,EAAE,KAAK;gBACX,WAAW,EAAE,EAAE;gBACf,KAAK,EAAE,KAAK;gBACZ,QAAQ,EAAE,KAAK;gBACf,KAAK,EAAE,EAAE,WAAW,EAAE,EAAE,EAAE,UAAU,EAAE,CAAC,EAAE,WAAW,EAAE,EAAE,EAAE,aAAa,EAAE,CAAC,EAAE,eAAe,EAAE,CAAC,EAAE;aACjG;YACD,CAAC,OAAO,CAAC,OAAO,CAAC,EAAE;gBACjB,EAAE,EAAE,aAAa;gBACjB,IAAI,EAAE,OAAO,CAAC,OAAO;gBACrB,IAAI,EAAE,aAAa;gBACnB,IAAI,EAAE,KAAK;gBACX,WAAW,EAAE,EAAE;gBACf,KAAK,EAAE,KAAK;gBACZ,QAAQ,EAAE,KAAK;gBACf,KAAK,EAAE,EAAE,WAAW,EAAE,EAAE,EAAE,UAAU,EAAE,EAAE,EAAE,WAAW,EAAE,EAAE,EAAE,aAAa,EAAE,CAAC,EAAE,eAAe,EAAE,CAAC,EAAE;aAClG;YACD,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE;gBACf,EAAE,EAAE,WAAW;gBACf,IAAI,EAAE,OAAO,CAAC,KAAK;gBACnB,IAAI,EAAE,WAAW;gBACjB,IAAI,EAAE,MAAM;gBACZ,WAAW,EAAE,EAAE;gBACf,KAAK,EAAE,KAAK;gBACZ,QAAQ,EAAE,KAAK;gBACf,KAAK,EAAE,EAAE,WAAW,EAAE,EAAE,EAAE,UAAU,EAAE,EAAE,EAAE,WAAW,EAAE,EAAE,EAAE,aAAa,EAAE,EAAE,EAAE,eAAe,EAAE,EAAE,EAAE;aACpG;YACD,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE;gBACf,EAAE,EAAE,WAAW;gBACf,IAAI,EAAE,OAAO,CAAC,KAAK;gBACnB,IAAI,EAAE,WAAW;gBACjB,IAAI,EAAE,MAAM;gBACZ,WAAW,EAAE,EAAE;gBACf,KAAK,EAAE,KAAK;gBACZ,QAAQ,EAAE,KAAK;gBACf,KAAK,EAAE,EAAE,WAAW,EAAE,EAAE,EAAE,UAAU,EAAE,EAAE,EAAE,WAAW,EAAE,EAAE,EAAE,aAAa,EAAE,EAAE,EAAE,eAAe,EAAE,EAAE,EAAE;aACpG;SACF,CAAC;QAIA,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;QACvB,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;QAErB,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC,aAAa,EAAE,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;IACpE,CAAC;IAEM,KAAK;QACV,IAAI,IAAI,CAAC,OAAO;YAAE,OAAO;QAEzB,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC;QACpB,IAAI,CAAC,cAAc,GAAG,MAAM,CAAC,WAAW,CAAC,GAAG,EAAE;YAC5C,IAAI,CAAC,MAAM,EAAE,CAAC;QAChB,CAAC,EAAE,IAAI,CAAC,MAAM,CAAC,cAAc,CAAC,CAAC;QAE/B,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;QACrB,OAAO,CAAC,GAAG,CAAC,2BAA2B,CAAC,CAAC;IAC3C,CAAC;IAEM,IAAI;QACT,IAAI,CAAC,IAAI,CAAC,OAAO;YAAE,OAAO;QAE1B,IAAI,CAAC,OAAO,GAAG,KAAK,CAAC;QACrB,IAAI,IAAI,CAAC,cAAc,EAAE,CAAC;YACxB,aAAa,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;YACnC,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC;QAC7B,CAAC;QAED,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;QACrB,OAAO,CAAC,GAAG,CAAC,2BAA2B,CAAC,CAAC;IAC3C,CAAC;IAEM,YAAY,CAAC,MAAiB;QACnC,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;IACvB,CAAC;IAEO,iBAAiB;QACvB,IAAI,CAAC,IAAI,CAAC,OAAO,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,OAAO;YAAE,OAAO;QAE3D,IAAI,CAAC,kBAAkB,EAAE,CAAC;QAC1B,IAAI,CAAC,gBAAgB,EAAE,CAAC;IAC1B,CAAC;IAEO,MAAM;QACZ,IAAI,CAAC,IAAI,CAAC,OAAO,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,OAAO,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,QAAQ,EAAE;YAAE,OAAO;QAEvF,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QACvB,IAAI,GAAG,GAAG,IAAI,CAAC,YAAY,GAAG,IAAI;YAAE,OAAO,CAAC,wBAAwB;QAEpE,IAAI,CAAC,YAAY,GAAG,GAAG,CAAC;QAExB,IAAI,CAAC;YACH,IAAI,CAAC,eAAe,EAAE,CAAC;QACzB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,6BAA6B,EAAE,KAAK,CAAC,CAAC;QACtD,CAAC;IACH,CAAC;IAEO,kBAAkB;QACxB,MAAM,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC,SAAS,EAAE,CAAC;QACxC,IAAI,CAAC,MAAM;YAAE,OAAO;QAEpB,8CAA8C;QAC9C,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC,OAAO,CAAC,GAAG,CAAC,EAAE;YAC7C,GAAG,CAAC,KAAK,GAAG,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC;YAC9C,GAAG,CAAC,QAAQ,GAAG,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC;QACtD,CAAC,CAAC,CAAC;IACL,CAAC;IAEO,UAAU,CAAC,OAAgB,EAAE,MAAc;QACjD,IAAI,OAAO,KAAK,OAAO,CAAC,IAAI;YAAE,OAAO,IAAI,CAAC;QAE1C,+BAA+B;QAC/B,OAAO,MAAM,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAClC,IAAI,CAAC,IAAI,KAAK,KAAK,IAAI,IAAI,CAAC,EAAE,CAAC,QAAQ,CAAC,OAAO,CAAC,CACjD,CAAC;IACJ,CAAC;IAEO,aAAa,CAAC,OAAgB,EAAE,MAAc;QACpD,IAAI,CAAC,MAAM,CAAC,GAAG;YAAE,OAAO,OAAO,KAAK,OAAO,CAAC,IAAI,CAAC;QACjD,OAAO,MAAM,CAAC,GAAG,CAAC,IAAI,KAAK,OAAO,CAAC;IACrC,CAAC;IAEO,gBAAgB;QACtB,MAAM,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC,SAAS,EAAE,CAAC;QACxC,MAAM,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,UAAU,EAAE,CAAC;QAE1C,IAAI,CAAC,MAAM;YAAE,OAAO;QAEpB,kDAAkD;QAClD,MAAM,aAAa,GAAG,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAC5D,CAAC,CAAC,EAAE,KAAK,MAAM,CAAC,EAAE;YAClB,IAAI,CAAC,OAAO,CAAC,iBAAiB,CAAC,MAAM,CAAC,QAAQ,EAAE,CAAC,CAAC,QAAQ,CAAC,GAAG,GAAG,CAClE,CAAC;QAEF,MAAM,WAAW,GAAG,MAAM,CAAC,MAAM,GAAG,MAAM,CAAC,SAAS,GAAG,GAAG,CAAC;QAC3D,MAAM,YAAY,GAAG,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC;QAExE,IAAI,aAAa,CAAC,MAAM,GAAG,CAAC,IAAI,WAAW,EAAE,CAAC;YAC5C,IAAI,CAAC,gBAAgB,GAAG,QAAQ,CAAC;QACnC,CAAC;aAAM,IAAI,YAAY,EAAE,CAAC;YACxB,IAAI,CAAC,gBAAgB,GAAG,SAAS,CAAC;QACpC,CAAC;aAAM,CAAC;YACN,IAAI,CAAC,gBAAgB,GAAG,aAAa,CAAC;QACxC,CAAC;IACH,CAAC;IAEO,eAAe;QACrB,MAAM,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC,SAAS,EAAE,CAAC;QACxC,IAAI,CAAC,MAAM;YAAE,OAAO;QAEpB,IAAI,SAAkB,CAAC;QAEvB,IAAI,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,sBAAsB,EAAE,CAAC;YAChD,SAAS,GAAG,IAAI,CAAC,yBAAyB,EAAE,CAAC;QAC/C,CAAC;aAAM,CAAC;YACN,SAAS,GAAG,IAAI,CAAC,mBAAmB,EAAE,CAAC;QACzC,CAAC;QAED,yCAAyC;QACzC,IAAI,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,WAAW,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,SAAS,CAAC,CAAC,KAAK,EAAE,CAAC;YAC5E,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC;QAC5B,CAAC;QAED,kEAAkE;QAClE,IAAI,IAAI,CAAC,YAAY,CAAC,SAAS,CAAC,CAAC,KAAK,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,SAAS,CAAC,CAAC,QAAQ,EAAE,CAAC;YACjF,IAAI,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAC;QAC3B,CAAC;IACH,CAAC;IAEO,yBAAyB;QAC/B,QAAQ,IAAI,CAAC,gBAAgB,EAAE,CAAC;YAC9B,KAAK,QAAQ;gBACX,OAAO,IAAI,CAAC,eAAe,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAC;oBACtD,IAAI,CAAC,mBAAmB,EAAE,CAAC;YACpC,KAAK,SAAS;gBACZ,OAAO,IAAI,CAAC,eAAe,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC;oBACvD,IAAI,CAAC,mBAAmB,EAAE,CAAC;YACpC,KAAK,aAAa;gBAChB,OAAO,IAAI,CAAC,eAAe,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,cAAc,CAAC,CAAC;oBAC3D,IAAI,CAAC,mBAAmB,EAAE,CAAC;YACpC;gBACE,OAAO,IAAI,CAAC,mBAAmB,EAAE,CAAC;QACtC,CAAC;IACH,CAAC;IAEO,eAAe,CAAC,YAAuB,EAAE;QAC/C,6BAA6B;QAC7B,KAAK,MAAM,OAAO,IAAI,SAAS,EAAE,CAAC;YAChC,IAAI,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,CAAC,KAAK,EAAE,CAAC;gBACrC,OAAO,OAAO,CAAC;YACjB,CAAC;QACH,CAAC;QAED,2BAA2B;QAC3B,KAAK,MAAM,OAAO,IAAI,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,QAAQ,EAAE,CAAC;YACpD,IAAI,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,CAAC,KAAK,EAAE,CAAC;gBACrC,OAAO,OAAO,CAAC;YACjB,CAAC;QACH,CAAC;QAED,OAAO,IAAI,CAAC;IACd,CAAC;IAEO,mBAAmB;QACzB,MAAM,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC,SAAS,EAAE,CAAC;QACxC,IAAI,CAAC,MAAM;YAAE,OAAO,OAAO,CAAC,IAAI,CAAC;QAEjC,oDAAoD;QACpD,KAAK,MAAM,OAAO,IAAI,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,QAAQ,EAAE,CAAC;YACpD,MAAM,GAAG,GAAG,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,CAAC;YACvC,IAAI,GAAG,CAAC,KAAK;gBACT,CAAC,MAAM,CAAC,KAAK,IAAI,GAAG,CAAC,WAAW,IAAI,MAAM,CAAC,SAAS,CAAC,IAAI,IAAI,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC;gBAC3E,OAAO,OAAO,CAAC;YACjB,CAAC;QACH,CAAC;QAED,OAAO,OAAO,CAAC,IAAI,CAAC;IACtB,CAAC;IAEO,SAAS,CAAC,OAAgB;QAChC,MAAM,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC,SAAS,EAAE,CAAC;QACxC,MAAM,GAAG,GAAG,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,CAAC;QAEvC,IAAI,CAAC,MAAM,IAAI,GAAG,CAAC,KAAK;YAAE,OAAO;QAEjC,IAAI,MAAM,CAAC,KAAK,IAAI,GAAG,CAAC,WAAW,IAAI,MAAM,CAAC,SAAS,CAAC,IAAI,IAAI,GAAG,CAAC,IAAI,EAAE,CAAC;YACzE,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC;gBACtB,IAAI,EAAE,UAAU,CAAC,GAAG;gBACpB,IAAI,EAAE,EAAE,MAAM,EAAE,GAAG,CAAC,EAAE,EAAE;gBACxB,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE;aACtB,CAAC,CAAC;YAEH,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE,EAAE,OAAO,EAAE,IAAI,EAAE,GAAG,CAAC,IAAI,EAAE,CAAC,CAAC;YACvD,OAAO,CAAC,GAAG,CAAC,wBAAwB,GAAG,CAAC,IAAI,QAAQ,GAAG,CAAC,IAAI,OAAO,CAAC,CAAC;QACvE,CAAC;IACH,CAAC;IAEO,QAAQ,CAAC,OAAgB;QAC/B,MAAM,GAAG,GAAG,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,CAAC;QAEvC,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC;YACtB,IAAI,EAAE,UAAU,CAAC,KAAK;YACtB,IAAI,EAAE,EAAE,MAAM,EAAE,GAAG,CAAC,EAAE,EAAE;YACxB,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE;SACtB,CAAC,CAAC;QAEH,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE,EAAE,OAAO,EAAE,CAAC,CAAC;QACtC,OAAO,CAAC,GAAG,CAAC,uBAAuB,GAAG,CAAC,IAAI,EAAE,CAAC,CAAC;IACjD,CAAC;IAEM,UAAU,CAAC,OAAgB;QAChC,OAAO,EAAE,GAAG,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,EAAE,CAAC;IAC3C,CAAC;IAEM,UAAU;QACf,OAAO,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,EAAE,GAAG,GAAG,EAAE,CAAC,CAAC,CAAC;IACnE,CAAC;IAEM,mBAAmB;QACxB,OAAO,IAAI,CAAC,gBAAgB,CAAC;IAC/B,CAAC;IAEM,QAAQ;QACb,MAAM,SAAS,GAAG,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,MAAM,CAAC;QAC/E,MAAM,SAAS,GAAG,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC,MAAM,CAAC;QAE1D,OAAO;YACL,OAAO,EAAE,IAAI,CAAC,OAAO;YACrB,SAAS;YACT,SAAS;YACT,gBAAgB,EAAE,IAAI,CAAC,gBAAgB;YACvC,WAAW,EAAE,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,QAAQ,CAAC,EAAE,IAAI,IAAI,MAAM;SACpF,CAAC;IACJ,CAAC;CACF;;;ACxZ4D;AAChB;AAqBtC,MAAM,aAAc,SAAQ,aAAY;IA4I7C,YAAY,OAAsB,EAAE,MAAiB;QACnD,KAAK,EAAE,CAAC;QA1IF,YAAO,GAAG,KAAK,CAAC;QAChB,mBAAc,GAAkB,IAAI,CAAC;QACrC,iBAAY,GAAG,CAAC,CAAC;QACjB,oBAAe,GAAsB,EAAE,CAAC;QACxC,aAAQ,GAAa,EAAE,CAAC;QAEhC,sBAAsB;QACL,eAAU,GAA6B;YACtD,UAAU;YACV,mBAAmB,EAAE;gBACnB,EAAE,EAAE,mBAAmB;gBACvB,IAAI,EAAE,YAAY;gBAClB,IAAI,EAAE,QAAQ;gBACd,IAAI,EAAE,EAAE,IAAI,EAAE,CAAC,EAAE,KAAK,EAAE,CAAC,EAAE,IAAI,EAAE,CAAC,EAAE,IAAI,EAAE,GAAG,EAAE,MAAM,EAAE,CAAC,EAAE;gBAC1D,WAAW,EAAE,CAAC;gBACd,QAAQ,EAAE,CAAC;gBACX,WAAW,EAAE,oBAAoB;gBACjC,OAAO,EAAE,EAAE,MAAM,EAAE,EAAE,EAAE;aACxB;YACD,oBAAoB,EAAE;gBACpB,EAAE,EAAE,oBAAoB;gBACxB,IAAI,EAAE,aAAa;gBACnB,IAAI,EAAE,QAAQ;gBACd,IAAI,EAAE,EAAE,IAAI,EAAE,CAAC,EAAE,KAAK,EAAE,CAAC,EAAE,IAAI,EAAE,CAAC,EAAE,IAAI,EAAE,GAAG,EAAE,MAAM,EAAE,CAAC,EAAE;gBAC1D,WAAW,EAAE,CAAC;gBACd,QAAQ,EAAE,CAAC;gBACX,WAAW,EAAE,sBAAsB;gBACnC,OAAO,EAAE,EAAE,MAAM,EAAE,EAAE,EAAE;aACxB;YACD,mBAAmB,EAAE;gBACnB,EAAE,EAAE,mBAAmB;gBACvB,IAAI,EAAE,YAAY;gBAClB,IAAI,EAAE,QAAQ;gBACd,IAAI,EAAE,EAAE,IAAI,EAAE,CAAC,EAAE,KAAK,EAAE,CAAC,EAAE,IAAI,EAAE,CAAC,EAAE,IAAI,EAAE,IAAI,EAAE,MAAM,EAAE,CAAC,EAAE;gBAC3D,WAAW,EAAE,CAAC;gBACd,QAAQ,EAAE,CAAC;gBACX,WAAW,EAAE,qBAAqB;gBAClC,OAAO,EAAE,EAAE,MAAM,EAAE,EAAE,EAAE;aACxB;YACD,sBAAsB,EAAE;gBACtB,EAAE,EAAE,sBAAsB;gBAC1B,IAAI,EAAE,eAAe;gBACrB,IAAI,EAAE,QAAQ;gBACd,IAAI,EAAE,EAAE,IAAI,EAAE,CAAC,EAAE,KAAK,EAAE,CAAC,EAAE,IAAI,EAAE,CAAC,EAAE,IAAI,EAAE,KAAK,EAAE,MAAM,EAAE,CAAC,EAAE;gBAC5D,WAAW,EAAE,EAAE;gBACf,QAAQ,EAAE,CAAC;gBACX,WAAW,EAAE,qBAAqB;gBAClC,OAAO,EAAE,EAAE,MAAM,EAAE,EAAE,EAAE;aACxB;YAED,WAAW;YACX,kBAAkB,EAAE;gBAClB,EAAE,EAAE,kBAAkB;gBACtB,IAAI,EAAE,kBAAkB;gBACxB,IAAI,EAAE,SAAS;gBACf,IAAI,EAAE,EAAE,IAAI,EAAE,CAAC,EAAE,KAAK,EAAE,CAAC,EAAE,IAAI,EAAE,CAAC,EAAE,IAAI,EAAE,IAAI,EAAE,MAAM,EAAE,CAAC,EAAE;gBAC3D,WAAW,EAAE,CAAC;gBACd,QAAQ,EAAE,CAAC;gBACX,WAAW,EAAE,yBAAyB;gBACtC,OAAO,EAAE,EAAE,gBAAgB,EAAE,GAAG,EAAE;aACnC;YACD,kBAAkB,EAAE;gBAClB,EAAE,EAAE,kBAAkB;gBACtB,IAAI,EAAE,mBAAmB;gBACzB,IAAI,EAAE,SAAS;gBACf,IAAI,EAAE,EAAE,IAAI,EAAE,CAAC,EAAE,KAAK,EAAE,CAAC,EAAE,IAAI,EAAE,CAAC,EAAE,IAAI,EAAE,IAAI,EAAE,MAAM,EAAE,CAAC,EAAE;gBAC3D,WAAW,EAAE,CAAC;gBACd,QAAQ,EAAE,CAAC;gBACX,WAAW,EAAE,iCAAiC;gBAC9C,OAAO,EAAE,EAAE,gBAAgB,EAAE,GAAG,EAAE;aACnC;YACD,iBAAiB,EAAE;gBACjB,EAAE,EAAE,iBAAiB;gBACrB,IAAI,EAAE,iBAAiB;gBACvB,IAAI,EAAE,SAAS;gBACf,IAAI,EAAE,EAAE,IAAI,EAAE,CAAC,EAAE,KAAK,EAAE,CAAC,EAAE,IAAI,EAAE,CAAC,EAAE,IAAI,EAAE,IAAI,EAAE,MAAM,EAAE,CAAC,EAAE;gBAC3D,WAAW,EAAE,CAAC;gBACd,QAAQ,EAAE,CAAC;gBACX,WAAW,EAAE,0BAA0B;gBACvC,OAAO,EAAE,EAAE,eAAe,EAAE,IAAI,EAAE;aACnC;YACD,kBAAkB,EAAE;gBAClB,EAAE,EAAE,kBAAkB;gBACtB,IAAI,EAAE,kBAAkB;gBACxB,IAAI,EAAE,SAAS;gBACf,IAAI,EAAE,EAAE,IAAI,EAAE,CAAC,EAAE,KAAK,EAAE,CAAC,EAAE,IAAI,EAAE,CAAC,EAAE,IAAI,EAAE,IAAI,EAAE,MAAM,EAAE,CAAC,EAAE;gBAC3D,WAAW,EAAE,CAAC;gBACd,QAAQ,EAAE,CAAC;gBACX,WAAW,EAAE,0BAA0B;gBACvC,OAAO,EAAE,EAAE,WAAW,EAAE,EAAE,EAAE;aAC7B;YAED,cAAc;YACd,0BAA0B,EAAE;gBAC1B,EAAE,EAAE,0BAA0B;gBAC9B,IAAI,EAAE,eAAe;gBACrB,IAAI,EAAE,YAAY;gBAClB,IAAI,EAAE,EAAE,IAAI,EAAE,CAAC,EAAE,KAAK,EAAE,CAAC,EAAE,IAAI,EAAE,CAAC,EAAE,IAAI,EAAE,GAAG,EAAE,MAAM,EAAE,CAAC,EAAE;gBAC1D,WAAW,EAAE,CAAC;gBACd,QAAQ,EAAE,CAAC;gBACX,WAAW,EAAE,2BAA2B;gBACxC,OAAO,EAAE,EAAE,aAAa,EAAE,EAAE,EAAE;aAC/B;YACD,wBAAwB,EAAE;gBACxB,EAAE,EAAE,wBAAwB;gBAC5B,IAAI,EAAE,aAAa;gBACnB,IAAI,EAAE,YAAY;gBAClB,IAAI,EAAE,EAAE,IAAI,EAAE,CAAC,EAAE,KAAK,EAAE,CAAC,EAAE,IAAI,EAAE,CAAC,EAAE,IAAI,EAAE,GAAG,EAAE,MAAM,EAAE,CAAC,EAAE;gBAC1D,WAAW,EAAE,CAAC;gBACd,QAAQ,EAAE,CAAC;gBACX,WAAW,EAAE,0BAA0B;gBACvC,OAAO,EAAE,EAAE,UAAU,EAAE,EAAE,EAAE,QAAQ,EAAE,KAAK,EAAE;aAC7C;YAED,YAAY;YACZ,eAAe,EAAE;gBACf,EAAE,EAAE,eAAe;gBACnB,IAAI,EAAE,UAAU;gBAChB,IAAI,EAAE,UAAU;gBAChB,IAAI,EAAE,EAAE,IAAI,EAAE,CAAC,EAAE,KAAK,EAAE,CAAC,EAAE,IAAI,EAAE,CAAC,EAAE,IAAI,EAAE,CAAC,EAAE,MAAM,EAAE,CAAC,EAAE;gBACxD,WAAW,EAAE,CAAC;gBACd,QAAQ,EAAE,EAAE;gBACZ,WAAW,EAAE,mCAAmC;gBAChD,OAAO,EAAE,EAAE,kBAAkB,EAAE,CAAC,EAAE;aACnC;YACD,gBAAgB,EAAE;gBAChB,EAAE,EAAE,gBAAgB;gBACpB,IAAI,EAAE,OAAO;gBACb,IAAI,EAAE,UAAU;gBAChB,IAAI,EAAE,EAAE,IAAI,EAAE,EAAE,EAAE,KAAK,EAAE,CAAC,EAAE,IAAI,EAAE,CAAC,EAAE,IAAI,EAAE,CAAC,EAAE,MAAM,EAAE,CAAC,EAAE;gBACzD,WAAW,EAAE,CAAC;gBACd,QAAQ,EAAE,CAAC;gBACX,WAAW,EAAE,0CAA0C;gBACvD,OAAO,EAAE,EAAE,MAAM,EAAE,EAAE,EAAE;aACxB;SACF,CAAC;QAIA,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;QACvB,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;QAErB,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC,aAAa,EAAE,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;IACpE,CAAC;IAEM,KAAK;QACV,IAAI,IAAI,CAAC,OAAO;YAAE,OAAO;QAEzB,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC;QACpB,IAAI,CAAC,cAAc,GAAG,MAAM,CAAC,WAAW,CAAC,GAAG,EAAE;YAC5C,IAAI,CAAC,MAAM,EAAE,CAAC;QAChB,CAAC,EAAE,IAAI,CAAC,MAAM,CAAC,cAAc,GAAG,CAAC,CAAC,CAAC,CAAC,yCAAyC;QAE7E,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;QACrB,OAAO,CAAC,GAAG,CAAC,0BAA0B,CAAC,CAAC;IAC1C,CAAC;IAEM,IAAI;QACT,IAAI,CAAC,IAAI,CAAC,OAAO;YAAE,OAAO;QAE1B,IAAI,CAAC,OAAO,GAAG,KAAK,CAAC;QACrB,IAAI,IAAI,CAAC,cAAc,EAAE,CAAC;YACxB,aAAa,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;YACnC,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC;QAC7B,CAAC;QAED,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;QACrB,OAAO,CAAC,GAAG,CAAC,0BAA0B,CAAC,CAAC;IAC1C,CAAC;IAEM,YAAY,CAAC,MAAiB;QACnC,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;IACvB,CAAC;IAEO,iBAAiB;QACvB,IAAI,CAAC,IAAI,CAAC,OAAO,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,OAAO;YAAE,OAAO;QAE1D,+BAA+B;QAC/B,IAAI,CAAC,eAAe,EAAE,CAAC;IACzB,CAAC;IAEO,MAAM;QACZ,IAAI,CAAC,IAAI,CAAC,OAAO,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,OAAO,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,QAAQ,EAAE;YAAE,OAAO;QAEtF,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QACvB,IAAI,GAAG,GAAG,IAAI,CAAC,YAAY,GAAG,IAAI;YAAE,OAAO,CAAC,wBAAwB;QAEpE,IAAI,CAAC,YAAY,GAAG,GAAG,CAAC;QAExB,IAAI,CAAC;YACH,IAAI,CAAC,cAAc,EAAE,CAAC;QACxB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,4BAA4B,EAAE,KAAK,CAAC,CAAC;QACrD,CAAC;IACH,CAAC;IAEO,cAAc;QACpB,MAAM,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC,SAAS,EAAE,CAAC;QACxC,IAAI,CAAC,MAAM;YAAE,OAAO;QAEpB,6BAA6B;QAC7B,MAAM,cAAc,GAAG,IAAI,CAAC,iBAAiB,CAAC,MAAM,CAAC,CAAC;QAEtD,2CAA2C;QAC3C,cAAc,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,QAAQ,GAAG,CAAC,CAAC,QAAQ,CAAC,CAAC;QAEvD,qDAAqD;QACrD,KAAK,MAAM,IAAI,IAAI,cAAc,EAAE,CAAC;YAClC,IAAI,IAAI,CAAC,aAAa,CAAC,MAAM,EAAE,IAAI,CAAC,IAAI,IAAI,CAAC,aAAa,CAAC,MAAM,EAAE,IAAI,CAAC,EAAE,CAAC;gBACzE,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;gBAC5B,MAAM,CAAC,8BAA8B;YACvC,CAAC;QACH,CAAC;IACH,CAAC;IAEO,iBAAiB,CAAC,MAAc;QACtC,OAAO,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE;YAClD,yCAAyC;YACzC,IAAI,MAAM,CAAC,KAAK,GAAG,IAAI,CAAC,WAAW;gBAAE,OAAO,KAAK,CAAC;YAElD,0CAA0C;YAC1C,QAAQ,IAAI,CAAC,IAAI,EAAE,CAAC;gBAClB,KAAK,KAAK;oBACR,OAAO,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,OAAO,CAAC;gBACrC,KAAK,QAAQ;oBACX,OAAO,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,UAAU,CAAC;gBACxC,KAAK,SAAS;oBACZ,OAAO,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,WAAW,CAAC;gBACzC;oBACE,OAAO,IAAI,CAAC;YAChB,CAAC;QACH,CAAC,CAAC,CAAC;IACL,CAAC;IAEO,aAAa,CAAC,MAAc,EAAE,IAAc;QAClD,MAAM,QAAQ,GAAG,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,gBAAgB,CAAC;QAEtD,OAAO,CACL,MAAM,CAAC,SAAS,CAAC,IAAI,IAAI,IAAI,CAAC,IAAI,CAAC,IAAI,GAAG,QAAQ,CAAC,IAAI;YACvD,MAAM,CAAC,SAAS,CAAC,KAAK,IAAI,IAAI,CAAC,IAAI,CAAC,KAAK,GAAG,QAAQ,CAAC,KAAK;YAC1D,MAAM,CAAC,SAAS,CAAC,IAAI,IAAI,IAAI,CAAC,IAAI,CAAC,IAAI,GAAG,QAAQ,CAAC,IAAI;YACvD,MAAM,CAAC,SAAS,CAAC,IAAI,IAAI,IAAI,CAAC,IAAI,CAAC,IAAI,GAAG,QAAQ,CAAC,IAAI,CACxD,CAAC;IACJ,CAAC;IAEO,aAAa,CAAC,MAAc,EAAE,IAAc;QAClD,oCAAoC;QACpC,IAAI,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,aAAa,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YACjD,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,aAAa,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,CAAC;gBACzD,OAAO,KAAK,CAAC;YACf,CAAC;QACH,CAAC;QAED,wBAAwB;QACxB,MAAM,cAAc,GAAG,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,EAAE,CAAC,GAAG,GAAG,GAAG,EAAE,CAAC,CAAC,CAAC;QAC1F,MAAM,QAAQ,GAAG,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,EAAE,CAAC,GAAG,GAAG,GAAG,EAAE,CAAC,CAAC,CAAC;QAC7E,MAAM,eAAe,GAAG,CAAC,QAAQ,GAAG,cAAc,CAAC,GAAG,GAAG,CAAC;QAE1D,IAAI,eAAe,GAAG,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,kBAAkB,EAAE,CAAC;YAC7D,OAAO,KAAK,CAAC;QACf,CAAC;QAED,0DAA0D;QAC1D,IAAI,IAAI,CAAC,IAAI,KAAK,YAAY,EAAE,CAAC;YAC/B,MAAM,YAAY,GAAG,MAAM,CAAC,SAAS,CAAC,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC,OAAO,CAAC,EAAE,KAAK,IAAI,CAAC,EAAE,CAAC,CAAC;YAC9E,IAAI,YAAY;gBAAE,OAAO,KAAK,CAAC;QACjC,CAAC;QAED,2CAA2C;QAC3C,QAAQ,IAAI,CAAC,IAAI,EAAE,CAAC;YAClB,KAAK,YAAY;gBACf,OAAO,IAAI,CAAC,mBAAmB,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC;YAChD,KAAK,QAAQ;gBACX,OAAO,IAAI,CAAC,eAAe,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC;YAC5C,KAAK,SAAS;gBACZ,OAAO,IAAI,CAAC,gBAAgB,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC;YAC7C;gBACE,OAAO,IAAI,CAAC;QAChB,CAAC;IACH,CAAC;IAEO,mBAAmB,CAAC,MAAc,EAAE,IAAc;QACxD,sCAAsC;QACtC,IAAI,IAAI,CAAC,EAAE,KAAK,0BAA0B,EAAE,CAAC;YAC3C,OAAO,MAAM,CAAC,MAAM,GAAG,MAAM,CAAC,SAAS,GAAG,GAAG,CAAC;QAChD,CAAC;QAED,gCAAgC;QAChC,IAAI,IAAI,CAAC,EAAE,KAAK,wBAAwB,EAAE,CAAC;YACzC,OAAO,IAAI,CAAC,MAAM,EAAE,GAAG,GAAG,CAAC,CAAC,aAAa;QAC3C,CAAC;QAED,OAAO,IAAI,CAAC;IACd,CAAC;IAEO,eAAe,CAAC,MAAc,EAAE,IAAc;QACpD,8CAA8C;QAC9C,IAAI,CAAC,MAAM,CAAC,MAAM;YAAE,OAAO,IAAI,CAAC;QAEhC,MAAM,aAAa,GAAG,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC;QAC3C,MAAM,SAAS,GAAG,IAAI,CAAC,OAAO,CAAC,MAAM,IAAI,CAAC,CAAC;QAE3C,OAAO,SAAS,GAAG,aAAa,CAAC;IACnC,CAAC;IAEO,gBAAgB,CAAC,MAAc,EAAE,IAAc;QACrD,4CAA4C;QAC5C,OAAO,IAAI,CAAC;IACd,CAAC;IAEO,aAAa,CAAC,MAAc;QAClC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,MAAM,CAAC,EAAE,CAAC;YACpC,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;QAC7B,CAAC;IACH,CAAC;IAEO,eAAe;QACrB,IAAI,IAAI,CAAC,QAAQ,CAAC,MAAM,KAAK,CAAC;YAAE,OAAO;QAEvC,MAAM,MAAM,GAAG,IAAI,CAAC,QAAQ,CAAC,KAAK,EAAE,CAAC;QACrC,IAAI,CAAC,MAAM;YAAE,OAAO;QAEpB,MAAM,IAAI,GAAG,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC;QACrC,IAAI,CAAC,IAAI;YAAE,OAAO;QAElB,MAAM,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC,SAAS,EAAE,CAAC;QACxC,IAAI,CAAC,MAAM,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,MAAM,EAAE,IAAI,CAAC,EAAE,CAAC;YACjD,OAAO,CAAC,qBAAqB;QAC/B,CAAC;QAED,uBAAuB;QACvB,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC;YACtB,IAAI,EAAE,UAAU,CAAC,GAAG;YACpB,IAAI,EAAE,EAAE,MAAM,EAAE,IAAI,CAAC,EAAE,EAAE;YACzB,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE;SACtB,CAAC,CAAC;QAEH,sBAAsB;QACtB,MAAM,QAAQ,GAAoB;YAChC,MAAM,EAAE,IAAI,CAAC,EAAE;YACf,QAAQ,EAAE,IAAI,CAAC,IAAI;YACnB,IAAI,EAAE,EAAE,GAAG,IAAI,CAAC,IAAI,EAAE;YACtB,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE;YACrB,OAAO,EAAE,IAAI,CAAC,+BAA+B;SAC9C,CAAC;QAEF,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;QAEpC,8BAA8B;QAC9B,IAAI,IAAI,CAAC,eAAe,CAAC,MAAM,GAAG,EAAE,EAAE,CAAC;YACrC,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC,eAAe,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC;QACzD,CAAC;QAED,IAAI,CAAC,IAAI,CAAC,eAAe,EAAE,QAAQ,CAAC,CAAC;QACrC,OAAO,CAAC,GAAG,CAAC,uBAAuB,IAAI,CAAC,IAAI,MAAM,EAAE,IAAI,CAAC,IAAI,CAAC,CAAC;IACjE,CAAC;IAEM,YAAY;QACjB,OAAO,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,EAAE,GAAG,IAAI,EAAE,CAAC,CAAC,CAAC;IACnE,CAAC;IAEM,kBAAkB;QACvB,OAAO,CAAC,GAAG,IAAI,CAAC,eAAe,CAAC,CAAC;IACnC,CAAC;IAEM,oBAAoB;QACzB,IAAI,CAAC,eAAe,GAAG,EAAE,CAAC;IAC5B,CAAC;IAEM,aAAa,CAAC,IAAc;QACjC,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,EAAE,CAAC,GAAG,EAAE,GAAG,IAAI,EAAE,CAAC;IACzC,CAAC;IAEM,QAAQ;QACb,MAAM,cAAc,GAAG,IAAI,CAAC,eAAe,CAAC,MAAM,CAAC;QACnD,MAAM,UAAU,GAAG,IAAI,CAAC,eAAe,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,QAAQ,EAAE,EAAE;YAC/D,OAAO,GAAG,GAAG,MAAM,CAAC,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,MAAM,CAAC,CAAC,OAAO,EAAE,GAAG,EAAE,EAAE,CAAC,OAAO,GAAG,GAAG,EAAE,CAAC,CAAC,CAAC;QACvF,CAAC,EAAE,CAAC,CAAC,CAAC;QAEN,MAAM,eAAe,GAAG,IAAI,CAAC,eAAe,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CACtD,IAAI,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC,SAAS,GAAG,MAAM,CAAC,iBAAiB;SACpD,CAAC,MAAM,CAAC;QAET,OAAO;YACL,OAAO,EAAE,IAAI,CAAC,OAAO;YACrB,cAAc;YACd,UAAU;YACV,eAAe;YACf,WAAW,EAAE,IAAI,CAAC,QAAQ,CAAC,MAAM;YACjC,cAAc,EAAE,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,MAAM;SACpD,CAAC;IACJ,CAAC;CACF;;;ACjasG;AAC1D;AAsBtC,MAAM,eAAgB,SAAQ,aAAY;IAyB/C,YAAY,OAAsB,EAAE,MAAiB;QACnD,KAAK,EAAE,CAAC;QAvBF,YAAO,GAAG,KAAK,CAAC;QAChB,mBAAc,GAAkB,IAAI,CAAC;QACrC,kBAAa,GAAG,CAAC,CAAC;QAClB,uBAAkB,GAAG,CAAC,CAAC;QACvB,eAAU,GAA2B,IAAI,GAAG,EAAE,CAAC;QAC/C,kBAAa,GAAyB,IAAI,GAAG,EAAE,CAAC;QAChD,gBAAW,GAAc,EAAE,CAAC;QAEpC,uBAAuB;QACN,eAAU,GAAG;YAC5B,CAAC,YAAY,CAAC,IAAI,CAAC,EAAE,EAAE,IAAI,EAAE,CAAC,EAAE,KAAK,EAAE,CAAC,EAAE,IAAI,EAAE,CAAC,EAAE,IAAI,EAAE,CAAC,EAAE;YAC5D,CAAC,YAAY,CAAC,KAAK,CAAC,EAAE,EAAE,IAAI,EAAE,CAAC,EAAE,KAAK,EAAE,CAAC,EAAE,IAAI,EAAE,CAAC,EAAE,IAAI,EAAE,CAAC,EAAE;YAC7D,CAAC,YAAY,CAAC,IAAI,CAAC,EAAE,EAAE,IAAI,EAAE,EAAE,EAAE,KAAK,EAAE,CAAC,EAAE,IAAI,EAAE,CAAC,EAAE,IAAI,EAAE,CAAC,EAAE;YAC7D,CAAC,YAAY,CAAC,IAAI,CAAC,EAAE,EAAE,IAAI,EAAE,EAAE,EAAE,KAAK,EAAE,EAAE,EAAE,IAAI,EAAE,CAAC,EAAE,IAAI,EAAE,CAAC,EAAE;SAC/D,CAAC;QAEe,kBAAa,GAAgF;YAC5G,CAAC,EAAE,EAAE,IAAI,EAAE,EAAE,EAAE,KAAK,EAAE,EAAE,EAAE,IAAI,EAAE,CAAC,EAAE,IAAI,EAAE,CAAC,EAAE;YAC5C,CAAC,EAAE,EAAE,IAAI,EAAE,EAAE,EAAE,KAAK,EAAE,EAAE,EAAE,IAAI,EAAE,CAAC,EAAE,IAAI,EAAE,CAAC,EAAE;YAC5C,CAAC,EAAE,EAAE,IAAI,EAAE,EAAE,EAAE,KAAK,EAAE,EAAE,EAAE,IAAI,EAAE,CAAC,EAAE,IAAI,EAAE,CAAC,EAAE;SAC7C,CAAC;QAIA,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;QACvB,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;QAErB,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC,aAAa,EAAE,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;IACpE,CAAC;IAEM,KAAK;QACV,IAAI,IAAI,CAAC,OAAO;YAAE,OAAO;QAEzB,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC;QACpB,IAAI,CAAC,cAAc,GAAG,MAAM,CAAC,WAAW,CAAC,GAAG,EAAE;YAC5C,IAAI,CAAC,MAAM,EAAE,CAAC;QAChB,CAAC,EAAE,IAAI,CAAC,MAAM,CAAC,cAAc,CAAC,CAAC;QAE/B,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;QACrB,OAAO,CAAC,GAAG,CAAC,4BAA4B,CAAC,CAAC;IAC5C,CAAC;IAEM,IAAI;QACT,IAAI,CAAC,IAAI,CAAC,OAAO;YAAE,OAAO;QAE1B,IAAI,CAAC,OAAO,GAAG,KAAK,CAAC;QACrB,IAAI,IAAI,CAAC,cAAc,EAAE,CAAC;YACxB,aAAa,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;YACnC,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC;QAC7B,CAAC;QAED,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;QACrB,OAAO,CAAC,GAAG,CAAC,4BAA4B,CAAC,CAAC;IAC5C,CAAC;IAEM,YAAY,CAAC,MAAiB;QACnC,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;IACvB,CAAC;IAEO,iBAAiB;QACvB,IAAI,CAAC,IAAI,CAAC,OAAO,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,OAAO;YAAE,OAAO;QAE5D,IAAI,CAAC,kBAAkB,EAAE,CAAC;QAC1B,IAAI,CAAC,mBAAmB,EAAE,CAAC;QAC3B,IAAI,CAAC,iBAAiB,EAAE,CAAC;IAC3B,CAAC;IAEO,MAAM;QACZ,IAAI,CAAC,IAAI,CAAC,OAAO,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,OAAO,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,QAAQ,EAAE;YAAE,OAAO;QAExF,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QAEvB,0CAA0C;QAC1C,IAAI,GAAG,GAAG,IAAI,CAAC,aAAa,GAAG,IAAI,EAAE,CAAC;YACpC,IAAI,CAAC,aAAa,GAAG,GAAG,CAAC;YACzB,IAAI,CAAC,mBAAmB,EAAE,CAAC;QAC7B,CAAC;QAED,iDAAiD;QACjD,IAAI,GAAG,GAAG,IAAI,CAAC,kBAAkB,GAAG,KAAK,EAAE,CAAC;YAC1C,IAAI,CAAC,kBAAkB,GAAG,GAAG,CAAC;YAC9B,IAAI,CAAC,oBAAoB,EAAE,CAAC;QAC9B,CAAC;QAED,2BAA2B;QAC3B,IAAI,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,YAAY,EAAE,CAAC;YACvC,IAAI,CAAC,YAAY,EAAE,CAAC;QACtB,CAAC;IACH,CAAC;IAEO,kBAAkB;QACxB,MAAM,SAAS,GAAG,IAAI,CAAC,OAAO,CAAC,YAAY,EAAE,CAAC;QAC9C,MAAM,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC,SAAS,EAAE,CAAC;QAExC,IAAI,CAAC,MAAM;YAAE,OAAO;QAEpB,qBAAqB;QACrB,IAAI,CAAC,UAAU,CAAC,KAAK,EAAE,CAAC;QAExB,SAAS,CAAC,OAAO,CAAC,CAAC,QAAQ,EAAE,EAAE,EAAE,EAAE;YACjC,IAAI,QAAQ,CAAC,IAAI,KAAK,YAAY,CAAC,IAAI,IAAI,QAAQ,CAAC,KAAK,KAAK,MAAM,CAAC,EAAE,EAAE,CAAC;gBACxE,MAAM,IAAI,GAAG,QAAgB,CAAC;gBAC9B,MAAM,SAAS,GAAc;oBAC3B,EAAE,EAAE,IAAI,CAAC,EAAE;oBACX,QAAQ,EAAE,IAAI,CAAC,QAAQ;oBACvB,YAAY,EAAE,IAAI,CAAC,YAAY;oBAC/B,KAAK,EAAE,IAAI,CAAC,KAAK;oBACjB,UAAU,EAAE,IAAI,CAAC,cAAc;oBAC/B,OAAO,EAAE,IAAI,CAAC,OAAO;oBACrB,UAAU,EAAE,IAAI,CAAC,UAAU;oBAC3B,WAAW,EAAE,CAAC,EAAE,8BAA8B;oBAC9C,WAAW,EAAE,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,QAAQ,CAAC;iBACjD,CAAC;gBAEF,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,EAAE,EAAE,SAAS,CAAC,CAAC;YACrC,CAAC;QACH,CAAC,CAAC,CAAC;IACL,CAAC;IAEO,mBAAmB;QACzB,uEAAuE;QACvE,4CAA4C;QAC5C,IAAI,CAAC,aAAa,CAAC,KAAK,EAAE,CAAC;QAE3B,4CAA4C;QAC5C,MAAM,YAAY,GAAG;YACnB,EAAE,IAAI,EAAE,YAAY,CAAC,IAAI,EAAE,GAAG,EAAE,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE,EAAE;YACpD,EAAE,IAAI,EAAE,YAAY,CAAC,KAAK,EAAE,GAAG,EAAE,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE,EAAE;YACrD,EAAE,IAAI,EAAE,YAAY,CAAC,IAAI,EAAE,GAAG,EAAE,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE,EAAE;YACpD,EAAE,IAAI,EAAE,YAAY,CAAC,IAAI,EAAE,GAAG,EAAE,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE,EAAE;SACrD,CAAC;QAEF,YAAY,CAAC,OAAO,CAAC,CAAC,IAAI,EAAE,KAAK,EAAE,EAAE;YACnC,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,IAAI,IAAI,KAAK,EAAE,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC;QAC5D,CAAC,CAAC,CAAC;IACL,CAAC;IAEO,iBAAiB;QACvB,MAAM,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,UAAU,EAAE,CAAC;QAC1C,MAAM,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC,SAAS,EAAE,CAAC;QAExC,IAAI,CAAC,MAAM;YAAE,OAAO;QAEpB,IAAI,CAAC,WAAW,GAAG,EAAE,CAAC;QAEtB,6CAA6C;QAC7C,OAAO,CAAC,OAAO,CAAC,WAAW,CAAC,EAAE;YAC5B,IAAI,WAAW,CAAC,EAAE,KAAK,MAAM,CAAC,EAAE,EAAE,CAAC;gBACjC,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC;YAC9C,CAAC;QACH,CAAC,CAAC,CAAC;IACL,CAAC;IAEO,oBAAoB;QAC1B,MAAM,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC,SAAS,EAAE,CAAC;QACxC,IAAI,CAAC,MAAM;YAAE,OAAO;QAEpB,MAAM,gBAAgB,GAAG,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC;QAE9C,IAAI,gBAAgB,IAAI,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,QAAQ,EAAE,CAAC;YACvD,OAAO,CAAC,uBAAuB;QACjC,CAAC;QAED,uCAAuC;QACvC,MAAM,gBAAgB,GAAG,IAAI,CAAC,wBAAwB,EAAE,CAAC;QAEzD,IAAI,gBAAgB,IAAI,IAAI,CAAC,aAAa,CAAC,MAAM,EAAE,gBAAgB,CAAC,YAAY,CAAC,EAAE,CAAC;YAClF,IAAI,CAAC,SAAS,CAAC,gBAAgB,CAAC,CAAC;QACnC,CAAC;IACH,CAAC;IAEO,wBAAwB;QAC9B,MAAM,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC,SAAS,EAAE,CAAC;QACxC,IAAI,CAAC,MAAM;YAAE,OAAO,IAAI,CAAC;QAEzB,MAAM,UAAU,GAAoB,EAAE,CAAC;QAEvC,qDAAqD;QACrD,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;YAC7C,MAAM,YAAY,GAAG,IAAI,CAAC,yBAAyB,CAAC,MAAM,CAAC,CAAC;YAE5D,8CAA8C;YAC9C,KAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,GAAG,EAAE,KAAK,IAAI,EAAE,EAAE,CAAC;gBAC7C,MAAM,QAAQ,GAAG,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,WAAW,CAAC;gBACnD,MAAM,CAAC,GAAG,OAAO,CAAC,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,KAAK,GAAG,IAAI,CAAC,EAAE,GAAG,GAAG,CAAC,GAAG,QAAQ,CAAC;gBACjE,MAAM,CAAC,GAAG,OAAO,CAAC,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,KAAK,GAAG,IAAI,CAAC,EAAE,GAAG,GAAG,CAAC,GAAG,QAAQ,CAAC;gBACjE,MAAM,QAAQ,GAAG,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC;gBAE1B,IAAI,IAAI,CAAC,mBAAmB,CAAC,QAAQ,CAAC,EAAE,CAAC;oBACvC,MAAM,SAAS,GAAkB;wBAC/B,QAAQ;wBACR,YAAY;wBACZ,QAAQ,EAAE,IAAI,CAAC,0BAA0B,CAAC,YAAY,CAAC;wBACvD,MAAM,EAAE,IAAI,CAAC,eAAe,CAAC,QAAQ,CAAC;wBACtC,UAAU,EAAE,IAAI,CAAC,mBAAmB,CAAC,QAAQ,EAAE,YAAY,CAAC;qBAC7D,CAAC;oBAEF,UAAU,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;gBAC7B,CAAC;YACH,CAAC;QACH,CAAC,CAAC,CAAC;QAEH,yDAAyD;QACzD,UAAU,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE;YACvB,MAAM,MAAM,GAAG,CAAC,CAAC,QAAQ,GAAG,CAAC,CAAC,MAAM,GAAG,CAAC,CAAC,UAAU,CAAC;YACpD,MAAM,MAAM,GAAG,CAAC,CAAC,QAAQ,GAAG,CAAC,CAAC,MAAM,GAAG,CAAC,CAAC,UAAU,CAAC;YACpD,OAAO,MAAM,GAAG,MAAM,CAAC;QACzB,CAAC,CAAC,CAAC;QAEH,OAAO,UAAU,CAAC,CAAC,CAAC,IAAI,IAAI,CAAC;IAC/B,CAAC;IAEO,yBAAyB,CAAC,MAAc;QAC9C,IAAI,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC;YAAE,OAAO,YAAY,CAAC,IAAI,CAAC;QACtD,IAAI,MAAM,CAAC,QAAQ,CAAC,OAAO,CAAC;YAAE,OAAO,YAAY,CAAC,KAAK,CAAC;QACxD,IAAI,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC;YAAE,OAAO,YAAY,CAAC,IAAI,CAAC;QACtD,IAAI,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC;YAAE,OAAO,YAAY,CAAC,IAAI,CAAC;QACtD,OAAO,YAAY,CAAC,IAAI,CAAC,CAAC,UAAU;IACtC,CAAC;IAEO,mBAAmB,CAAC,QAAiB;QAC3C,mDAAmD;QACnD,KAAK,MAAM,IAAI,IAAI,IAAI,CAAC,UAAU,CAAC,MAAM,EAAE,EAAE,CAAC;YAC5C,MAAM,QAAQ,GAAG,IAAI,CAAC,OAAO,CAAC,iBAAiB,CAAC,QAAQ,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC;YACzE,IAAI,QAAQ,GAAG,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,WAAW,EAAE,CAAC;gBACjD,OAAO,KAAK,CAAC;YACf,CAAC;QACH,CAAC;QAED,wCAAwC;QACxC,KAAK,MAAM,SAAS,IAAI,IAAI,CAAC,WAAW,EAAE,CAAC;YACzC,MAAM,QAAQ,GAAG,IAAI,CAAC,OAAO,CAAC,iBAAiB,CAAC,QAAQ,EAAE,SAAS,CAAC,CAAC;YACrE,IAAI,QAAQ,GAAG,GAAG,EAAE,CAAC,CAAC,uBAAuB;gBAC3C,OAAO,KAAK,CAAC;YACf,CAAC;QACH,CAAC;QAED,OAAO,IAAI,CAAC;IACd,CAAC;IAEO,0BAA0B,CAAC,YAA0B;QAC3D,MAAM,aAAa,GAAG,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,gBAAgB,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC;QACnF,OAAO,aAAa,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,GAAG,aAAa,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACvD,CAAC;IAEO,eAAe,CAAC,QAAiB;QACvC,IAAI,MAAM,GAAG,EAAE,CAAC;QAEhB,mDAAmD;QACnD,KAAK,MAAM,SAAS,IAAI,IAAI,CAAC,WAAW,EAAE,CAAC;YACzC,MAAM,QAAQ,GAAG,IAAI,CAAC,OAAO,CAAC,iBAAiB,CAAC,QAAQ,EAAE,SAAS,CAAC,CAAC;YACrE,IAAI,QAAQ,GAAG,GAAG,EAAE,CAAC;gBACnB,MAAM,IAAI,CAAC,GAAG,GAAG,QAAQ,CAAC,GAAG,EAAE,CAAC;YAClC,CAAC;QACH,CAAC;QAED,OAAO,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC;IAC7B,CAAC;IAEO,mBAAmB,CAAC,QAAiB,EAAE,YAA0B;QACvE,IAAI,UAAU,GAAG,CAAC,CAAC;QAEnB,2DAA2D;QAC3D,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;YAC7C,IAAI,IAAI,CAAC,yBAAyB,CAAC,MAAM,CAAC,KAAK,YAAY,EAAE,CAAC;gBAC5D,MAAM,QAAQ,GAAG,IAAI,CAAC,OAAO,CAAC,iBAAiB,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAC;gBACnE,IAAI,QAAQ,GAAG,GAAG,EAAE,CAAC;oBACnB,UAAU,IAAI,CAAC,GAAG,GAAG,QAAQ,CAAC,GAAG,EAAE,CAAC;gBACtC,CAAC;YACH,CAAC;QACH,CAAC,CAAC,CAAC;QAEH,OAAO,UAAU,CAAC;IACpB,CAAC;IAEO,aAAa,CAAC,MAAc,EAAE,YAA0B;QAC9D,MAAM,IAAI,GAAG,IAAI,CAAC,UAAU,CAAC,YAAY,CAAC,CAAC;QAE3C,OAAO,CACL,MAAM,CAAC,SAAS,CAAC,IAAI,IAAI,IAAI,CAAC,IAAI;YAClC,MAAM,CAAC,SAAS,CAAC,KAAK,IAAI,IAAI,CAAC,KAAK;YACpC,MAAM,CAAC,SAAS,CAAC,IAAI,IAAI,IAAI,CAAC,IAAI;YAClC,MAAM,CAAC,SAAS,CAAC,IAAI,IAAI,IAAI,CAAC,IAAI,CACnC,CAAC;IACJ,CAAC;IAEO,SAAS,CAAC,SAAwB;QACxC,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC;YACtB,IAAI,EAAE,UAAU,CAAC,KAAK;YACtB,IAAI,EAAE;gBACJ,IAAI,EAAE,MAAM;gBACZ,CAAC,EAAE,SAAS,CAAC,QAAQ,CAAC,CAAC;gBACvB,CAAC,EAAE,SAAS,CAAC,QAAQ,CAAC,CAAC;gBACvB,YAAY,EAAE,SAAS,CAAC,YAAY;aACrC;YACD,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE;SACtB,CAAC,CAAC;QAEH,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE,SAAS,CAAC,CAAC;QACnC,OAAO,CAAC,GAAG,CAAC,sBAAsB,SAAS,CAAC,YAAY,aAAa,SAAS,CAAC,QAAQ,CAAC,CAAC,KAAK,SAAS,CAAC,QAAQ,CAAC,CAAC,GAAG,CAAC,CAAC;IACzH,CAAC;IAEO,mBAAmB;QACzB,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,WAAW;YAAE,OAAO;QAE/C,MAAM,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC,SAAS,EAAE,CAAC;QACxC,IAAI,CAAC,MAAM;YAAE,OAAO;QAEpB,kCAAkC;QAClC,KAAK,MAAM,IAAI,IAAI,IAAI,CAAC,UAAU,CAAC,MAAM,EAAE,EAAE,CAAC;YAC5C,IAAI,IAAI,CAAC,KAAK,GAAG,CAAC,IAAI,IAAI,CAAC,gBAAgB,CAAC,MAAM,EAAE,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC;gBAChE,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;gBACvB,MAAM,CAAC,kCAAkC;YAC3C,CAAC;QACH,CAAC;IACH,CAAC;IAEO,gBAAgB,CAAC,MAAc,EAAE,YAAoB;QAC3D,MAAM,IAAI,GAAG,IAAI,CAAC,aAAa,CAAC,YAAY,CAAC,CAAC;QAC9C,IAAI,CAAC,IAAI;YAAE,OAAO,KAAK,CAAC;QAExB,OAAO,CACL,MAAM,CAAC,SAAS,CAAC,IAAI,IAAI,IAAI,CAAC,IAAI;YAClC,MAAM,CAAC,SAAS,CAAC,KAAK,IAAI,IAAI,CAAC,KAAK;YACpC,MAAM,CAAC,SAAS,CAAC,IAAI,IAAI,IAAI,CAAC,IAAI;YAClC,MAAM,CAAC,SAAS,CAAC,IAAI,IAAI,IAAI,CAAC,IAAI,CACnC,CAAC;IACJ,CAAC;IAEO,WAAW,CAAC,IAAe;QACjC,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC;YACtB,IAAI,EAAE,UAAU,CAAC,OAAO;YACxB,IAAI,EAAE,EAAE,UAAU,EAAE,IAAI,CAAC,EAAE,EAAE;YAC7B,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE;SACtB,CAAC,CAAC;QAEH,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE,IAAI,CAAC,CAAC;QAChC,OAAO,CAAC,GAAG,CAAC,6BAA6B,IAAI,CAAC,EAAE,aAAa,IAAI,CAAC,KAAK,GAAG,CAAC,EAAE,CAAC,CAAC;IACjF,CAAC;IAEO,eAAe,CAAC,QAAiB;QACvC,iDAAiD;QACjD,MAAM,SAAS,GAAG,IAAI,CAAC,OAAO,CAAC,YAAY,EAAE,CAAC;QAE9C,KAAK,MAAM,QAAQ,IAAI,SAAS,CAAC,MAAM,EAAE,EAAE,CAAC;YAC1C,IAAI,QAAQ,CAAC,IAAI,KAAK,YAAY,CAAC,KAAK,IAAI,QAAQ,CAAC,IAAI,KAAK,YAAY,CAAC,IAAI,EAAE,CAAC;gBAChF,MAAM,QAAQ,GAAG,IAAI,CAAC,OAAO,CAAC,iBAAiB,CAAC,QAAQ,EAAE,QAAQ,CAAC,QAAQ,CAAC,CAAC;gBAC7E,IAAI,QAAQ,GAAG,GAAG,EAAE,CAAC;oBACnB,OAAO,IAAI,CAAC;gBACd,CAAC;YACH,CAAC;QACH,CAAC;QAED,OAAO,KAAK,CAAC;IACf,CAAC;IAEO,YAAY;QAClB,MAAM,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC,SAAS,EAAE,CAAC;QACxC,IAAI,CAAC,MAAM;YAAE,OAAO;QAEpB,yBAAyB;QACzB,KAAK,MAAM,IAAI,IAAI,IAAI,CAAC,UAAU,CAAC,MAAM,EAAE,EAAE,CAAC;YAC5C,IAAI,CAAC,IAAI,CAAC,WAAW,IAAI,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,EAAE,CAAC;gBACrD,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;gBAC3B,MAAM,CAAC,kCAAkC;YAC3C,CAAC;QACH,CAAC;IACH,CAAC;IAEO,cAAc,CAAC,MAAc;QACnC,OAAO,MAAM,CAAC,SAAS,CAAC,IAAI,IAAI,EAAE,CAAC;IACrC,CAAC;IAEO,eAAe,CAAC,IAAe;QACrC,+BAA+B;QAC/B,MAAM,MAAM,GAAG,CAAC,CAAC,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;QACjC,MAAM,QAAQ,GAAG,EAAE,CAAC;QAEpB,KAAK,MAAM,KAAK,IAAI,MAAM,EAAE,CAAC;YAC3B,MAAM,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,KAAK,GAAG,IAAI,CAAC,EAAE,GAAG,GAAG,CAAC,GAAG,QAAQ,CAAC;YACvE,MAAM,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,KAAK,GAAG,IAAI,CAAC,EAAE,GAAG,GAAG,CAAC,GAAG,QAAQ,CAAC;YAEvE,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC;gBACtB,IAAI,EAAE,UAAU,CAAC,KAAK;gBACtB,IAAI,EAAE;oBACJ,IAAI,EAAE,OAAO;oBACb,CAAC;oBACD,CAAC;iBACF;gBACD,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE;aACtB,CAAC,CAAC;QACL,CAAC;QAED,IAAI,CAAC,IAAI,CAAC,eAAe,EAAE,IAAI,CAAC,CAAC;QACjC,OAAO,CAAC,GAAG,CAAC,8BAA8B,IAAI,CAAC,EAAE,cAAc,CAAC,CAAC;IACnE,CAAC;IAEM,aAAa;QAClB,OAAO,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,MAAM,EAAE,CAAC,CAAC;IAC9C,CAAC;IAEM,QAAQ;QACb,MAAM,UAAU,GAAG,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC;QACxC,MAAM,cAAc,GAAG,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,MAAM,EAAE,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,MAAM,CAAC;QAC9F,MAAM,eAAe,GAAG,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,MAAM,EAAE,CAAC,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,IAAI,EAAE,EAAE,CAAC,GAAG,GAAG,IAAI,CAAC,UAAU,EAAE,CAAC,CAAC,CAAC;QAE7G,OAAO;YACL,OAAO,EAAE,IAAI,CAAC,OAAO;YACrB,UAAU;YACV,QAAQ,EAAE,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,QAAQ;YACxC,cAAc;YACd,eAAe;YACf,aAAa,EAAE,IAAI,CAAC,aAAa,CAAC,IAAI;YACtC,WAAW,EAAE,IAAI,CAAC,WAAW,CAAC,MAAM;SACrC,CAAC;IACJ,CAAC;CACF;;;AC1bwE;AAC5B;AAwB7C,IAAY,WAOX;AAPD,WAAY,WAAW;IACrB,kCAAmB;IACnB,gCAAiB;IACjB,oCAAqB;IACrB,0CAA2B;IAC3B,wCAAyB;IACzB,4BAAa;AACf,CAAC,EAPW,WAAW,KAAX,WAAW,QAOtB;AAED,IAAY,QAQX;AARD,WAAY,QAAQ;IAClB,iCAAqB;IACrB,6BAAiB;IACjB,+BAAmB;IACnB,iCAAqB;IACrB,+BAAmB;IACnB,yBAAa;IACb,yBAAa;AACf,CAAC,EARW,QAAQ,KAAR,QAAQ,QAQnB;AAEM,MAAM,eAAgB,SAAQ,aAAY;IAc/C,YAAY,OAAsB,EAAE,MAAiB;QACnD,KAAK,EAAE,CAAC;QAZF,YAAO,GAAG,KAAK,CAAC;QAChB,mBAAc,GAAkB,IAAI,CAAC;QACrC,SAAI,GAA6B,IAAI,GAAG,EAAE,CAAC;QAC3C,cAAS,GAAc,EAAE,CAAC;QAC1B,iBAAY,GAAG,CAAC,CAAC;QACjB,qBAAgB,GAAG;YACzB,eAAe,EAAE,EAAE,IAAI,EAAE,CAAC,EAAE,KAAK,EAAE,CAAC,EAAE,IAAI,EAAE,CAAC,EAAE,IAAI,EAAE,CAAC,EAAE;YACxD,WAAW,EAAE,IAAsB;YACnC,WAAW,EAAE,CAAC;SACf,CAAC;QAIA,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;QACvB,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;QAErB,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC,aAAa,EAAE,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;IACpE,CAAC;IAEM,KAAK;QACV,IAAI,IAAI,CAAC,OAAO;YAAE,OAAO;QAEzB,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC;QACpB,IAAI,CAAC,cAAc,GAAG,MAAM,CAAC,WAAW,CAAC,GAAG,EAAE;YAC5C,IAAI,CAAC,MAAM,EAAE,CAAC;QAChB,CAAC,EAAE,IAAI,CAAC,MAAM,CAAC,cAAc,CAAC,CAAC;QAE/B,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;QACrB,OAAO,CAAC,GAAG,CAAC,4BAA4B,CAAC,CAAC;IAC5C,CAAC;IAEM,IAAI;QACT,IAAI,CAAC,IAAI,CAAC,OAAO;YAAE,OAAO;QAE1B,IAAI,CAAC,OAAO,GAAG,KAAK,CAAC;QACrB,IAAI,IAAI,CAAC,cAAc,EAAE,CAAC;YACxB,aAAa,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;YACnC,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC;QAC7B,CAAC;QAED,mBAAmB;QACnB,IAAI,CAAC,IAAI,CAAC,KAAK,EAAE,CAAC;QAElB,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;QACrB,OAAO,CAAC,GAAG,CAAC,4BAA4B,CAAC,CAAC;IAC5C,CAAC;IAEM,YAAY,CAAC,MAAiB;QACnC,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;IACvB,CAAC;IAEO,iBAAiB;QACvB,IAAI,CAAC,IAAI,CAAC,OAAO,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,OAAO;YAAE,OAAO;QAE5D,IAAI,CAAC,eAAe,EAAE,CAAC;QACvB,IAAI,CAAC,kBAAkB,EAAE,CAAC;IAC5B,CAAC;IAEO,MAAM;QACZ,IAAI,CAAC,IAAI,CAAC,OAAO,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,OAAO,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,QAAQ,EAAE;YAAE,OAAO;QAExF,IAAI,CAAC;YACH,IAAI,CAAC,iBAAiB,EAAE,CAAC;YACzB,IAAI,CAAC,iBAAiB,EAAE,CAAC;YACzB,IAAI,CAAC,WAAW,EAAE,CAAC;YACnB,IAAI,CAAC,kBAAkB,EAAE,CAAC;QAC5B,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,8BAA8B,EAAE,KAAK,CAAC,CAAC;QACvD,CAAC;IACH,CAAC;IAEO,iBAAiB;QACvB,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QACvB,MAAM,eAAe,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC;QAEvC,2BAA2B;QAC3B,IAAI,eAAe,GAAG,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,OAAO;YAC/C,GAAG,GAAG,IAAI,CAAC,YAAY,GAAG,KAAK,EAAE,CAAC,CAAC,qBAAqB;YAE1D,IAAI,CAAC,QAAQ,EAAE,CAAC;YAChB,IAAI,CAAC,YAAY,GAAG,GAAG,CAAC;QAC1B,CAAC;QAED,mBAAmB;QACnB,KAAK,MAAM,CAAC,KAAK,EAAE,GAAG,CAAC,IAAI,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,EAAE,CAAC;YAC/C,IAAI,GAAG,CAAC,KAAK,KAAK,QAAQ,CAAC,IAAI,EAAE,CAAC;gBAChC,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;gBACxB,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE,EAAE,KAAK,EAAE,MAAM,EAAE,OAAO,EAAE,CAAC,CAAC;YACxD,CAAC;QACH,CAAC;IACH,CAAC;IAEO,QAAQ;QACd,MAAM,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC,SAAS,EAAE,CAAC;QACxC,IAAI,CAAC,MAAM;YAAE,OAAO;QAEpB,MAAM,KAAK,GAAG,OAAO,IAAI,CAAC,GAAG,EAAE,IAAI,IAAI,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC;QAC7E,MAAM,aAAa,GAAG,IAAI,CAAC,qBAAqB,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;QAElE,MAAM,GAAG,GAAgB;YACvB,EAAE,EAAE,KAAK;YACT,IAAI,EAAE,OAAO,IAAI,CAAC,IAAI,CAAC,IAAI,GAAG,CAAC,EAAE;YACjC,QAAQ,EAAE,aAAa;YACvB,MAAM,EAAE,IAAI;YACZ,QAAQ,EAAE,IAAI,CAAC,iBAAiB,EAAE;YAClC,KAAK,EAAE,QAAQ,CAAC,QAAQ;YACxB,UAAU,EAAE,IAAI,CAAC,GAAG,EAAE;YACtB,SAAS,EAAE,EAAE,IAAI,EAAE,CAAC,EAAE,KAAK,EAAE,CAAC,EAAE,IAAI,EAAE,CAAC,EAAE,IAAI,EAAE,CAAC,EAAE;YAClD,MAAM,EAAE,GAAG;YACX,KAAK,EAAE,CAAC;YACR,IAAI,EAAE,IAAI;SACX,CAAC;QAEF,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,KAAK,EAAE,GAAG,CAAC,CAAC;QAC1B,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE,GAAG,CAAC,CAAC;QAC7B,OAAO,CAAC,GAAG,CAAC,2BAA2B,GAAG,CAAC,IAAI,kBAAkB,GAAG,CAAC,QAAQ,EAAE,CAAC,CAAC;IACnF,CAAC;IAEO,qBAAqB,CAAC,SAAkB;QAC9C,+CAA+C;QAC/C,MAAM,KAAK,GAAG,IAAI,CAAC,MAAM,EAAE,GAAG,CAAC,GAAG,IAAI,CAAC,EAAE,CAAC;QAC1C,MAAM,QAAQ,GAAG,GAAG,GAAG,IAAI,CAAC,MAAM,EAAE,GAAG,GAAG,CAAC,CAAC,qBAAqB;QAEjE,OAAO;YACL,CAAC,EAAE,SAAS,CAAC,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,QAAQ;YAC3C,CAAC,EAAE,SAAS,CAAC,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,QAAQ;SAC5C,CAAC;IACJ,CAAC;IAEO,iBAAiB;QACvB,MAAM,SAAS,GAAG,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,YAAY,CAAC;QACrD,MAAM,kBAAkB,GAAkB,EAAE,CAAC;QAE7C,IAAI,SAAS,CAAC,OAAO;YAAE,kBAAkB,CAAC,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC;QACpE,IAAI,SAAS,CAAC,MAAM;YAAE,kBAAkB,CAAC,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC;QAClE,IAAI,SAAS,CAAC,QAAQ;YAAE,kBAAkB,CAAC,IAAI,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC;QACtE,IAAI,SAAS,CAAC,WAAW;YAAE,kBAAkB,CAAC,IAAI,CAAC,WAAW,CAAC,WAAW,CAAC,CAAC;QAC5E,IAAI,SAAS,CAAC,UAAU;YAAE,kBAAkB,CAAC,IAAI,CAAC,WAAW,CAAC,UAAU,CAAC,CAAC;QAE1E,IAAI,kBAAkB,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YACpC,OAAO,WAAW,CAAC,IAAI,CAAC;QAC1B,CAAC;QAED,OAAO,kBAAkB,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,kBAAkB,CAAC,MAAM,CAAC,CAAC,IAAI,WAAW,CAAC,IAAI,CAAC;IACvG,CAAC;IAEO,eAAe;QACrB,MAAM,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,UAAU,EAAE,CAAC;QAE1C,KAAK,MAAM,GAAG,IAAI,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,EAAE,CAAC;YACrC,2BAA2B;YAC3B,MAAM,aAAa,GAAG,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAC5D,CAAC,CAAC,CAAC,KAAK;gBACR,IAAI,CAAC,OAAO,CAAC,iBAAiB,CAAC,GAAG,CAAC,QAAQ,EAAE,CAAC,CAAC,QAAQ,CAAC,GAAG,GAAG,CAC/D,CAAC;YAEF,sCAAsC;YACtC,IAAI,aAAa,CAAC,MAAM,GAAG,CAAC,IAAI,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,YAAY,CAAC,YAAY,EAAE,CAAC;gBAChF,IAAI,GAAG,CAAC,QAAQ,KAAK,WAAW,CAAC,MAAM,EAAE,CAAC;oBACxC,GAAG,CAAC,KAAK,GAAG,QAAQ,CAAC,OAAO,CAAC;gBAC/B,CAAC;qBAAM,CAAC;oBACN,GAAG,CAAC,KAAK,GAAG,QAAQ,CAAC,QAAQ,CAAC;gBAChC,CAAC;YACH,CAAC;iBAAM,IAAI,GAAG,CAAC,IAAI,EAAE,CAAC;gBACpB,GAAG,CAAC,KAAK,GAAG,QAAQ,CAAC,OAAO,CAAC;YAC/B,CAAC;iBAAM,CAAC;gBACN,GAAG,CAAC,KAAK,GAAG,QAAQ,CAAC,IAAI,CAAC;YAC5B,CAAC;QACH,CAAC;IACH,CAAC;IAEO,kBAAkB;QACxB,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,YAAY,CAAC,cAAc;YAAE,OAAO;QAE/D,6BAA6B;QAC7B,IAAI,CAAC,gBAAgB,CAAC,eAAe,GAAG,EAAE,IAAI,EAAE,CAAC,EAAE,KAAK,EAAE,CAAC,EAAE,IAAI,EAAE,CAAC,EAAE,IAAI,EAAE,CAAC,EAAE,CAAC;QAEhF,KAAK,MAAM,GAAG,IAAI,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,EAAE,CAAC;YACrC,IAAI,CAAC,gBAAgB,CAAC,eAAe,CAAC,IAAI,IAAI,GAAG,CAAC,SAAS,CAAC,IAAI,CAAC;YACjE,IAAI,CAAC,gBAAgB,CAAC,eAAe,CAAC,KAAK,IAAI,GAAG,CAAC,SAAS,CAAC,KAAK,CAAC;YACnE,IAAI,CAAC,gBAAgB,CAAC,eAAe,CAAC,IAAI,IAAI,GAAG,CAAC,SAAS,CAAC,IAAI,CAAC;YACjE,IAAI,CAAC,gBAAgB,CAAC,eAAe,CAAC,IAAI,IAAI,GAAG,CAAC,SAAS,CAAC,IAAI,CAAC;QACnE,CAAC;QAED,sBAAsB;QACtB,MAAM,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,UAAU,EAAE,CAAC;QAC1C,MAAM,aAAa,GAAG,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE;YAC5D,IAAI,CAAC,CAAC,KAAK;gBAAE,OAAO,KAAK,CAAC;YAE1B,KAAK,MAAM,GAAG,IAAI,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,EAAE,CAAC;gBACrC,IAAI,IAAI,CAAC,OAAO,CAAC,iBAAiB,CAAC,GAAG,CAAC,QAAQ,EAAE,CAAC,CAAC,QAAQ,CAAC,GAAG,GAAG,EAAE,CAAC;oBACnE,OAAO,IAAI,CAAC;gBACd,CAAC;YACH,CAAC;YACD,OAAO,KAAK,CAAC;QACf,CAAC,CAAC,CAAC;QAEH,IAAI,CAAC,gBAAgB,CAAC,WAAW,GAAG,aAAa,CAAC,MAAM,CAAC;IAC3D,CAAC;IAEO,WAAW;QACjB,4CAA4C;QAC5C,KAAK,MAAM,GAAG,IAAI,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,EAAE,CAAC;YACrC,IAAI,GAAG,CAAC,IAAI,IAAI,IAAI,CAAC,GAAG,EAAE,GAAG,GAAG,CAAC,IAAI,CAAC,SAAS,GAAG,GAAG,CAAC,IAAI,CAAC,iBAAiB,EAAE,CAAC;gBAC7E,SAAS,CAAC,uCAAuC;YACnD,CAAC;YAED,GAAG,CAAC,IAAI,GAAG,IAAI,CAAC,kBAAkB,CAAC,GAAG,CAAC,CAAC;QAC1C,CAAC;IACH,CAAC;IAEO,kBAAkB,CAAC,GAAgB;QACzC,QAAQ,GAAG,CAAC,QAAQ,EAAE,CAAC;YACrB,KAAK,WAAW,CAAC,OAAO;gBACtB,OAAO,IAAI,CAAC,mBAAmB,CAAC,GAAG,CAAC,CAAC;YACvC,KAAK,WAAW,CAAC,QAAQ;gBACvB,OAAO,IAAI,CAAC,oBAAoB,CAAC,GAAG,CAAC,CAAC;YACxC,KAAK,WAAW,CAAC,WAAW;gBAC1B,OAAO,IAAI,CAAC,uBAAuB,CAAC,GAAG,CAAC,CAAC;YAC3C,KAAK,WAAW,CAAC,UAAU;gBACzB,OAAO,IAAI,CAAC,sBAAsB,CAAC,GAAG,CAAC,CAAC;YAC1C;gBACE,OAAO,IAAI,CAAC;QAChB,CAAC;IACH,CAAC;IAEO,mBAAmB,CAAC,GAAgB;QAC1C,6BAA6B;QAC7B,MAAM,aAAa,GAAG,CAAC,YAAY,CAAC,IAAI,EAAE,YAAY,CAAC,KAAK,EAAE,YAAY,CAAC,IAAI,CAAC,CAAC;QACjF,MAAM,cAAc,GAAG,aAAa,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,aAAa,CAAC,MAAM,CAAC,CAAC,CAAC;QAEvF,mDAAmD;QACnD,MAAM,KAAK,GAAG,IAAI,CAAC,MAAM,EAAE,GAAG,CAAC,GAAG,IAAI,CAAC,EAAE,CAAC;QAC1C,MAAM,QAAQ,GAAG,EAAE,GAAG,IAAI,CAAC,MAAM,EAAE,GAAG,GAAG,CAAC;QAC1C,MAAM,MAAM,GAAG;YACb,CAAC,EAAE,GAAG,CAAC,QAAQ,CAAC,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,QAAQ;YAC9C,CAAC,EAAE,GAAG,CAAC,QAAQ,CAAC,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,QAAQ;SAC/C,CAAC;QAEF,OAAO;YACL,IAAI,EAAE,MAAM;YACZ,MAAM;YACN,QAAQ,EAAE,CAAC;YACX,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE;YACrB,iBAAiB,EAAE,KAAK,CAAC,aAAa;SACvC,CAAC;IACJ,CAAC;IAEO,oBAAoB,CAAC,GAAgB;QAC3C,gCAAgC;QAChC,MAAM,KAAK,GAAG,IAAI,CAAC,MAAM,EAAE,GAAG,CAAC,GAAG,IAAI,CAAC,EAAE,CAAC;QAC1C,MAAM,QAAQ,GAAG,GAAG,GAAG,IAAI,CAAC,MAAM,EAAE,GAAG,GAAG,CAAC;QAC3C,MAAM,MAAM,GAAG;YACb,CAAC,EAAE,GAAG,CAAC,QAAQ,CAAC,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,QAAQ;YAC9C,CAAC,EAAE,GAAG,CAAC,QAAQ,CAAC,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,QAAQ;SAC/C,CAAC;QAEF,OAAO;YACL,IAAI,EAAE,OAAO;YACb,MAAM;YACN,QAAQ,EAAE,CAAC;YACX,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE;YACrB,iBAAiB,EAAE,KAAK,CAAC,aAAa;SACvC,CAAC;IACJ,CAAC;IAEO,uBAAuB,CAAC,GAAgB;QAC9C,qCAAqC;QACrC,MAAM,KAAK,GAAG,IAAI,CAAC,MAAM,EAAE,GAAG,CAAC,GAAG,IAAI,CAAC,EAAE,CAAC;QAC1C,MAAM,QAAQ,GAAG,GAAG,GAAG,IAAI,CAAC,MAAM,EAAE,GAAG,GAAG,CAAC;QAC3C,MAAM,MAAM,GAAG;YACb,CAAC,EAAE,GAAG,CAAC,QAAQ,CAAC,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,QAAQ;YAC9C,CAAC,EAAE,GAAG,CAAC,QAAQ,CAAC,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,QAAQ;SAC/C,CAAC;QAEF,OAAO;YACL,IAAI,EAAE,SAAS;YACf,MAAM;YACN,QAAQ,EAAE,CAAC;YACX,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE;YACrB,iBAAiB,EAAE,KAAK,CAAC,aAAa;SACvC,CAAC;IACJ,CAAC;IAEO,sBAAsB,CAAC,GAAgB;QAC7C,MAAM,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC,SAAS,EAAE,CAAC;QACxC,IAAI,CAAC,MAAM;YAAE,OAAO,IAAI,CAAC,uBAAuB,CAAC,GAAG,CAAC,CAAC;QAEtD,0BAA0B;QAC1B,OAAO;YACL,IAAI,EAAE,QAAQ;YACd,MAAM,EAAE,MAAM,CAAC,QAAQ;YACvB,QAAQ,EAAE,CAAC;YACX,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE;YACrB,iBAAiB,EAAE,KAAK,CAAC,aAAa;SACvC,CAAC;IACJ,CAAC;IAEO,iBAAiB;QACvB,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QAEvB,KAAK,MAAM,GAAG,IAAI,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,EAAE,CAAC;YACrC,IAAI,GAAG,GAAG,GAAG,CAAC,UAAU,GAAG,GAAG;gBAAE,SAAS,CAAC,gBAAgB;YAE1D,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,CAAC;YAC3B,GAAG,CAAC,UAAU,GAAG,GAAG,CAAC;QACvB,CAAC;IACH,CAAC;IAEO,gBAAgB,CAAC,GAAgB;QACvC,IAAI,CAAC,GAAG,CAAC,IAAI;YAAE,OAAO;QAEtB,QAAQ,GAAG,CAAC,KAAK,EAAE,CAAC;YAClB,KAAK,QAAQ,CAAC,MAAM;gBAClB,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,CAAC;gBAC1B,MAAM;YACR,KAAK,QAAQ,CAAC,OAAO;gBACnB,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,CAAC;gBACzB,MAAM;YACR,KAAK,QAAQ,CAAC,OAAO;gBACnB,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,CAAC;gBACzB,MAAM;YACR,KAAK,QAAQ,CAAC,QAAQ;gBACpB,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,CAAC;gBACzB,MAAM;QACV,CAAC;IACH,CAAC;IAEO,eAAe,CAAC,GAAgB;QACtC,IAAI,CAAC,GAAG,CAAC,IAAI;YAAE,OAAO;QAEtB,MAAM,QAAQ,GAAG,IAAI,CAAC,OAAO,CAAC,iBAAiB,CAAC,GAAG,CAAC,QAAQ,EAAE,GAAG,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;QAE/E,IAAI,QAAQ,GAAG,EAAE,EAAE,CAAC;YAClB,GAAG,CAAC,KAAK,GAAG,QAAQ,CAAC,OAAO,CAAC;YAC7B,OAAO;QACT,CAAC;QAED,sBAAsB;QACtB,MAAM,KAAK,GAAG,IAAI,CAAC,OAAO,CAAC,cAAc,CAAC,GAAG,CAAC,QAAQ,EAAE,GAAG,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;QACzE,MAAM,KAAK,GAAG,CAAC,CAAC,CAAC,qBAAqB;QAEtC,GAAG,CAAC,QAAQ,CAAC,CAAC,IAAI,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,KAAK,CAAC;QAC1C,GAAG,CAAC,QAAQ,CAAC,CAAC,IAAI,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,KAAK,CAAC;QAE1C,wDAAwD;QACxD,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC;YACtB,IAAI,EAAE,UAAU,CAAC,IAAI;YACrB,IAAI,EAAE,EAAE,CAAC,EAAE,GAAG,CAAC,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAE,GAAG,CAAC,QAAQ,CAAC,CAAC,EAAE;YAC9C,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE;SACtB,CAAC,CAAC;IACL,CAAC;IAEO,cAAc,CAAC,GAAgB;QACrC,IAAI,CAAC,GAAG,CAAC,IAAI;YAAE,OAAO;QAEtB,QAAQ,GAAG,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC;YACtB,KAAK,MAAM;gBACT,IAAI,CAAC,oBAAoB,CAAC,GAAG,CAAC,CAAC;gBAC/B,MAAM;YACR,KAAK,OAAO;gBACV,IAAI,CAAC,qBAAqB,CAAC,GAAG,CAAC,CAAC;gBAChC,MAAM;YACR,KAAK,SAAS;gBACZ,IAAI,CAAC,wBAAwB,CAAC,GAAG,CAAC,CAAC;gBACnC,MAAM;YACR,KAAK,QAAQ;gBACX,IAAI,CAAC,oBAAoB,CAAC,GAAG,CAAC,CAAC;gBAC/B,MAAM;QACV,CAAC;IACH,CAAC;IAEO,oBAAoB,CAAC,GAAgB;QAC3C,8BAA8B;QAC9B,MAAM,YAAY,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC;QACvD,MAAM,YAAY,GAAG,CAAC,MAAM,EAAE,OAAO,EAAE,MAAM,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC,CAA+B,CAAC;QAE5G,GAAG,CAAC,SAAS,CAAC,YAAY,CAAC,IAAI,YAAY,CAAC;QAE5C,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE;YACrB,KAAK,EAAE,GAAG,CAAC,EAAE;YACb,MAAM,EAAE,MAAM;YACd,MAAM,EAAE,EAAE,CAAC,YAAY,CAAC,EAAE,YAAY,EAAE;SACzC,CAAC,CAAC;IACL,CAAC;IAEO,qBAAqB,CAAC,GAAgB;QAC5C,8BAA8B;QAC9B,IAAI,GAAG,CAAC,SAAS,CAAC,IAAI,IAAI,CAAC,IAAI,GAAG,CAAC,SAAS,CAAC,KAAK,IAAI,CAAC,EAAE,CAAC;YACxD,GAAG,CAAC,SAAS,CAAC,IAAI,IAAI,CAAC,CAAC;YACxB,GAAG,CAAC,SAAS,CAAC,KAAK,IAAI,CAAC,CAAC;YAEzB,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC;gBACtB,IAAI,EAAE,UAAU,CAAC,KAAK;gBACtB,IAAI,EAAE;oBACJ,IAAI,EAAE,MAAM;oBACZ,CAAC,EAAE,GAAG,CAAC,IAAK,CAAC,MAAM,CAAC,CAAC;oBACrB,CAAC,EAAE,GAAG,CAAC,IAAK,CAAC,MAAM,CAAC,CAAC;iBACtB;gBACD,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE;aACtB,CAAC,CAAC;YAEH,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE;gBACrB,KAAK,EAAE,GAAG,CAAC,EAAE;gBACb,MAAM,EAAE,OAAO;gBACf,MAAM,EAAE,EAAE,QAAQ,EAAE,MAAM,EAAE;aAC7B,CAAC,CAAC;QACL,CAAC;IACH,CAAC;IAEO,wBAAwB,CAAC,GAAgB;QAC/C,6CAA6C;QAC7C,GAAG,CAAC,KAAK,IAAI,GAAG,CAAC;QAEjB,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE;YACrB,KAAK,EAAE,GAAG,CAAC,EAAE;YACb,MAAM,EAAE,SAAS;YACjB,MAAM,EAAE,EAAE,UAAU,EAAE,GAAG,EAAE;SAC5B,CAAC,CAAC;IACL,CAAC;IAEO,oBAAoB,CAAC,GAAgB;QAC3C,8CAA8C;QAC9C,MAAM,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC,SAAS,EAAE,CAAC;QACxC,IAAI,MAAM,EAAE,CAAC;YACX,GAAG,CAAC,IAAK,CAAC,MAAM,GAAG,MAAM,CAAC,QAAQ,CAAC;QACrC,CAAC;IACH,CAAC;IAEO,cAAc,CAAC,GAAgB;QACrC,MAAM,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,UAAU,EAAE,CAAC;QAE1C,qBAAqB;QACrB,IAAI,YAAY,GAAkB,IAAI,CAAC;QACvC,IAAI,WAAW,GAAG,QAAQ,CAAC;QAE3B,KAAK,MAAM,MAAM,IAAI,OAAO,CAAC,MAAM,EAAE,EAAE,CAAC;YACtC,IAAI,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC;gBAClB,MAAM,QAAQ,GAAG,IAAI,CAAC,OAAO,CAAC,iBAAiB,CAAC,GAAG,CAAC,QAAQ,EAAE,MAAM,CAAC,QAAQ,CAAC,CAAC;gBAC/E,IAAI,QAAQ,GAAG,WAAW,EAAE,CAAC;oBAC3B,WAAW,GAAG,QAAQ,CAAC;oBACvB,YAAY,GAAG,MAAM,CAAC;gBACxB,CAAC;YACH,CAAC;QACH,CAAC;QAED,IAAI,YAAY,EAAE,CAAC;YACjB,uBAAuB;YACvB,MAAM,KAAK,GAAG,IAAI,CAAC,OAAO,CAAC,cAAc,CAAC,YAAY,CAAC,QAAQ,EAAE,GAAG,CAAC,QAAQ,CAAC,CAAC;YAC/E,MAAM,KAAK,GAAG,CAAC,CAAC,CAAC,sBAAsB;YAEvC,GAAG,CAAC,QAAQ,CAAC,CAAC,IAAI,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,KAAK,CAAC;YAC1C,GAAG,CAAC,QAAQ,CAAC,CAAC,IAAI,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,KAAK,CAAC;QAC5C,CAAC;IACH,CAAC;IAEO,cAAc,CAAC,GAAgB;QACrC,0CAA0C;QAC1C,MAAM,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,UAAU,EAAE,CAAC;QAE1C,KAAK,MAAM,MAAM,IAAI,OAAO,CAAC,MAAM,EAAE,EAAE,CAAC;YACtC,IAAI,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC;gBAClB,MAAM,QAAQ,GAAG,IAAI,CAAC,OAAO,CAAC,iBAAiB,CAAC,GAAG,CAAC,QAAQ,EAAE,MAAM,CAAC,QAAQ,CAAC,CAAC;gBAC/E,IAAI,QAAQ,GAAG,GAAG,EAAE,CAAC;oBACnB,MAAM,KAAK,GAAG,IAAI,CAAC,OAAO,CAAC,cAAc,CAAC,GAAG,CAAC,QAAQ,EAAE,MAAM,CAAC,QAAQ,CAAC,CAAC;oBAEzE,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC;wBACtB,IAAI,EAAE,UAAU,CAAC,MAAM;wBACvB,IAAI,EAAE,EAAE,KAAK,EAAE;wBACf,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE;qBACtB,CAAC,CAAC;oBAEH,MAAM;gBACR,CAAC;YACH,CAAC;QACH,CAAC;IACH,CAAC;IAEO,kBAAkB;QACxB,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,YAAY,CAAC,gBAAgB;YAAE,OAAO;QAEjE,iDAAiD;QACjD,KAAK,MAAM,GAAG,IAAI,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,EAAE,CAAC;YACrC,IAAI,IAAI,CAAC,gBAAgB,CAAC,WAAW,GAAG,CAAC,EAAE,CAAC;gBAC1C,8CAA8C;gBAC9C,IAAI,GAAG,CAAC,QAAQ,KAAK,WAAW,CAAC,OAAO,IAAI,GAAG,CAAC,QAAQ,KAAK,WAAW,CAAC,WAAW,EAAE,CAAC;oBACrF,GAAG,CAAC,QAAQ,GAAG,WAAW,CAAC,UAAU,CAAC;gBACxC,CAAC;YACH,CAAC;iBAAM,IAAI,IAAI,CAAC,gBAAgB,CAAC,WAAW,KAAK,CAAC,EAAE,CAAC;gBACnD,2CAA2C;gBAC3C,IAAI,GAAG,CAAC,QAAQ,KAAK,WAAW,CAAC,UAAU,EAAE,CAAC;oBAC5C,GAAG,CAAC,QAAQ,GAAG,WAAW,CAAC,OAAO,CAAC;gBACrC,CAAC;YACH,CAAC;QACH,CAAC;IACH,CAAC;IAEM,OAAO;QACZ,OAAO,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC,CAAC;IACxC,CAAC;IAEM,UAAU,CAAC,KAAa;QAC7B,OAAO,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,IAAI,IAAI,CAAC;IACtC,CAAC;IAEM,mBAAmB;QACxB,OAAO,EAAE,GAAG,IAAI,CAAC,gBAAgB,EAAE,CAAC;IACtC,CAAC;IAEM,QAAQ;QACb,MAAM,SAAS,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC;QACjC,MAAM,UAAU,GAAG,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,KAAK,KAAK,QAAQ,CAAC,IAAI,CAAC,CAAC,MAAM,CAAC;QAChG,MAAM,cAAc,GAA2B,EAAE,CAAC;QAElD,KAAK,MAAM,GAAG,IAAI,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,EAAE,CAAC;YACrC,cAAc,CAAC,GAAG,CAAC,QAAQ,CAAC,GAAG,CAAC,cAAc,CAAC,GAAG,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC;QACzE,CAAC;QAED,OAAO;YACL,OAAO,EAAE,IAAI,CAAC,OAAO;YACrB,SAAS;YACT,UAAU;YACV,OAAO,EAAE,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,OAAO;YACtC,cAAc;YACd,WAAW,EAAE,IAAI,CAAC,gBAAgB,CAAC,WAAW;YAC9C,eAAe,EAAE,IAAI,CAAC,gBAAgB,CAAC,eAAe;SACvD,CAAC;IACJ,CAAC;CACF;;;ACjkBD,iBAAiB;AACjB,uCAAuC;AACvC,yCAAyC;AACzC,sBAAsB;AACtB,4FAA4F;AAC5F,uCAAuC;AACvC,gCAAgC;AAChC,kCAAkC;AAClC,qBAAqB;AACrB,+BAA+B;AAC/B,kBAAkB;AAE8B;AACI;AACA;AACF;AACI;AACA;AACT;AAS7C,MAAM,iBAAkB,SAAQ,aAAY;IAY1C;QACE,KAAK,EAAE,CAAC;QALF,OAAE,GAAuB,IAAI,CAAC;QAC9B,kBAAa,GAAG,KAAK,CAAC;QACtB,wBAAmB,GAAG,KAAK,CAAC;QAKlC,OAAO,CAAC,GAAG,CAAC,qCAAqC,CAAC,CAAC;QAEnD,6BAA6B;QAC7B,IAAI,CAAC,aAAa,GAAG,IAAI,aAAa,EAAE,CAAC;QACzC,IAAI,CAAC,OAAO,GAAG,IAAI,aAAa,EAAE,CAAC;QAEnC,qBAAqB;QACrB,MAAM,MAAM,GAAG,IAAI,CAAC,aAAa,CAAC,SAAS,EAAE,CAAC;QAC9C,IAAI,CAAC,QAAQ,GAAG,IAAI,cAAc,CAAC,IAAI,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC;QACzD,IAAI,CAAC,OAAO,GAAG,IAAI,aAAa,CAAC,IAAI,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC;QACvD,IAAI,CAAC,SAAS,GAAG,IAAI,eAAe,CAAC,IAAI,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC;QAC3D,IAAI,CAAC,SAAS,GAAG,IAAI,eAAe,CAAC,IAAI,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC;QAE3D,IAAI,CAAC,mBAAmB,EAAE,CAAC;QAC3B,IAAI,CAAC,kBAAkB,EAAE,CAAC;QAC1B,IAAI,CAAC,UAAU,EAAE,CAAC;IACpB,CAAC;IAEO,KAAK,CAAC,UAAU;QACtB,IAAI,CAAC;YACH,wBAAwB;YACxB,MAAM,IAAI,CAAC,eAAe,EAAE,CAAC;YAE7B,YAAY;YACZ,IAAI,CAAC,QAAQ,EAAE,CAAC;YAEhB,gCAAgC;YAChC,MAAM,MAAM,GAAG,IAAI,CAAC,aAAa,CAAC,SAAS,EAAE,CAAC;YAC9C,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,CAAC;YAE1B,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC;YAC1B,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;YAEzB,OAAO,CAAC,GAAG,CAAC,+CAA+C,CAAC,CAAC;QAE/D,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,4CAA4C,EAAE,KAAK,CAAC,CAAC;QACrE,CAAC;IACH,CAAC;IAEO,eAAe;QACrB,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,EAAE;YAC7B,MAAM,SAAS,GAAG,GAAG,EAAE;gBACrB,IAAI,IAAI,CAAC,OAAO,CAAC,QAAQ,EAAE,EAAE,CAAC;oBAC5B,OAAO,EAAE,CAAC;gBACZ,CAAC;qBAAM,CAAC;oBACN,UAAU,CAAC,SAAS,EAAE,IAAI,CAAC,CAAC;gBAC9B,CAAC;YACH,CAAC,CAAC;YACF,SAAS,EAAE,CAAC;QACd,CAAC,CAAC,CAAC;IACL,CAAC;IAEO,mBAAmB;QACzB,kBAAkB;QAClB,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC,YAAY,EAAE,GAAG,EAAE;YACjC,OAAO,CAAC,GAAG,CAAC,iCAAiC,CAAC,CAAC;QACjD,CAAC,CAAC,CAAC;QAEH,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC,aAAa,EAAE,CAAC,SAAS,EAAE,EAAE;YAC3C,IAAI,CAAC,QAAQ,EAAE,CAAC;QAClB,CAAC,CAAC,CAAC;QAEH,gBAAgB;QAChB,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC,aAAa,EAAE,CAAC,IAAI,EAAE,EAAE;YACvC,IAAI,CAAC,SAAS,CAAC,WAAW,EAAE,YAAY,IAAI,CAAC,OAAO,EAAE,CAAC,CAAC;QAC1D,CAAC,CAAC,CAAC;QAEH,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC,cAAc,EAAE,CAAC,IAAI,EAAE,EAAE;YACxC,IAAI,CAAC,SAAS,CAAC,WAAW,EAAE,aAAa,IAAI,CAAC,OAAO,QAAQ,IAAI,CAAC,IAAI,OAAO,CAAC,CAAC;QACjF,CAAC,CAAC,CAAC;QAEH,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC,eAAe,EAAE,CAAC,IAAI,EAAE,EAAE;YACxC,IAAI,CAAC,SAAS,CAAC,UAAU,EAAE,aAAa,IAAI,CAAC,QAAQ,EAAE,CAAC,CAAC;QAC3D,CAAC,CAAC,CAAC;QAEH,IAAI,CAAC,SAAS,CAAC,EAAE,CAAC,YAAY,EAAE,CAAC,IAAI,EAAE,EAAE;YACvC,IAAI,CAAC,SAAS,CAAC,YAAY,EAAE,UAAU,IAAI,CAAC,YAAY,OAAO,CAAC,CAAC;QACnE,CAAC,CAAC,CAAC;QAEH,IAAI,CAAC,SAAS,CAAC,EAAE,CAAC,cAAc,EAAE,CAAC,IAAI,EAAE,EAAE;YACzC,IAAI,CAAC,SAAS,CAAC,YAAY,EAAE,0BAA0B,IAAI,CAAC,KAAK,GAAG,CAAC,EAAE,CAAC,CAAC;QAC3E,CAAC,CAAC,CAAC;QAEH,IAAI,CAAC,SAAS,CAAC,EAAE,CAAC,YAAY,EAAE,CAAC,IAAI,EAAE,EAAE;YACvC,IAAI,CAAC,SAAS,CAAC,YAAY,EAAE,eAAe,IAAI,CAAC,IAAI,EAAE,CAAC,CAAC;QAC3D,CAAC,CAAC,CAAC;QAEH,IAAI,CAAC,SAAS,CAAC,EAAE,CAAC,WAAW,EAAE,CAAC,IAAI,EAAE,EAAE;YACtC,IAAI,CAAC,SAAS,CAAC,YAAY,EAAE,OAAO,IAAI,CAAC,KAAK,cAAc,IAAI,CAAC,MAAM,EAAE,CAAC,CAAC;QAC7E,CAAC,CAAC,CAAC;IACL,CAAC;IAEO,kBAAkB;QACxB,QAAQ,CAAC,gBAAgB,CAAC,SAAS,EAAE,CAAC,KAAK,EAAE,EAAE;YAC7C,MAAM,MAAM,GAAG,IAAI,CAAC,aAAa,CAAC,SAAS,EAAE,CAAC;YAC9C,MAAM,OAAO,GAAG,MAAM,CAAC,MAAM,CAAC,aAAa,CAAC;YAE5C,8DAA8D;YAC9D,MAAM,IAAI,GAAG,OAAO,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;YAChC,IAAI,OAAO,GAAG,IAAI,CAAC;YAEnB,KAAK,MAAM,GAAG,IAAI,IAAI,EAAE,CAAC;gBACvB,QAAQ,GAAG,CAAC,IAAI,EAAE,EAAE,CAAC;oBACnB,KAAK,MAAM;wBACT,IAAI,CAAC,KAAK,CAAC,OAAO;4BAAE,OAAO,GAAG,KAAK,CAAC;wBACpC,MAAM;oBACR,KAAK,OAAO;wBACV,IAAI,CAAC,KAAK,CAAC,QAAQ;4BAAE,OAAO,GAAG,KAAK,CAAC;wBACrC,MAAM;oBACR,KAAK,KAAK;wBACR,IAAI,CAAC,KAAK,CAAC,MAAM;4BAAE,OAAO,GAAG,KAAK,CAAC;wBACnC,MAAM;oBACR;wBACE,IAAI,KAAK,CAAC,GAAG,KAAK,GAAG;4BAAE,OAAO,GAAG,KAAK,CAAC;gBAC3C,CAAC;YACH,CAAC;YAED,IAAI,OAAO,EAAE,CAAC;gBACZ,IAAI,CAAC,aAAa,EAAE,CAAC;YACvB,CAAC;QACH,CAAC,CAAC,CAAC;IACL,CAAC;IAEO,aAAa;QACnB,IAAI,IAAI,CAAC,mBAAmB;YAAE,OAAO;QAErC,IAAI,CAAC,mBAAmB,GAAG,IAAI,CAAC;QAEhC,OAAO,CAAC,GAAG,CAAC,+CAA+C,CAAC,CAAC;QAE7D,mBAAmB;QACnB,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE,CAAC;QACrB,IAAI,CAAC,OAAO,CAAC,IAAI,EAAE,CAAC;QACpB,IAAI,CAAC,SAAS,CAAC,IAAI,EAAE,CAAC;QACtB,IAAI,CAAC,SAAS,CAAC,IAAI,EAAE,CAAC;QAEtB,mCAAmC;QACnC,IAAI,CAAC,gBAAgB,CAAC,0BAA0B,EAAE,OAAO,CAAC,CAAC;QAE3D,YAAY;QACZ,IAAI,CAAC,QAAQ,EAAE,CAAC;QAEhB,IAAI,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;IAC7B,CAAC;IAEO,YAAY,CAAC,MAAiB;QACpC,IAAI,MAAM,CAAC,QAAQ,CAAC,OAAO,IAAI,CAAC,IAAI,CAAC,mBAAmB,EAAE,CAAC;YACzD,IAAI,CAAC,QAAQ,CAAC,KAAK,EAAE,CAAC;QACxB,CAAC;QAED,IAAI,MAAM,CAAC,OAAO,CAAC,OAAO,IAAI,CAAC,IAAI,CAAC,mBAAmB,EAAE,CAAC;YACxD,IAAI,CAAC,OAAO,CAAC,KAAK,EAAE,CAAC;QACvB,CAAC;QAED,IAAI,MAAM,CAAC,SAAS,CAAC,OAAO,IAAI,CAAC,IAAI,CAAC,mBAAmB,EAAE,CAAC;YAC1D,IAAI,CAAC,SAAS,CAAC,KAAK,EAAE,CAAC;QACzB,CAAC;QAED,IAAI,MAAM,CAAC,SAAS,CAAC,OAAO,IAAI,CAAC,IAAI,CAAC,mBAAmB,EAAE,CAAC;YAC1D,IAAI,CAAC,SAAS,CAAC,KAAK,EAAE,CAAC;QACzB,CAAC;IACH,CAAC;IAEO,WAAW;QACjB,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE,CAAC;QACrB,IAAI,CAAC,OAAO,CAAC,IAAI,EAAE,CAAC;QACpB,IAAI,CAAC,SAAS,CAAC,IAAI,EAAE,CAAC;QACtB,IAAI,CAAC,SAAS,CAAC,IAAI,EAAE,CAAC;IACxB,CAAC;IAEO,QAAQ;QACd,IAAI,IAAI,CAAC,EAAE;YAAE,OAAO;QAEpB,MAAM,MAAM,GAAG,IAAI,CAAC,aAAa,CAAC,SAAS,EAAE,CAAC;QAC9C,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,WAAW;YAAE,OAAO;QAEnC,2BAA2B;QAC3B,IAAI,CAAC,EAAE,GAAG,QAAQ,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;QACxC,IAAI,CAAC,EAAE,CAAC,EAAE,GAAG,wBAAwB,CAAC;QACtC,IAAI,CAAC,EAAE,CAAC,KAAK,CAAC,OAAO,GAAG;;QAEpB,MAAM,CAAC,EAAE,CAAC,eAAe,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,eAAe;QAC1E,MAAM,CAAC,EAAE,CAAC,eAAe,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,cAAc,CAAC,CAAC,CAAC,aAAa;;kCAElD,MAAM,CAAC,EAAE,CAAC,YAAY;;;;;;;;;KASnD,CAAC;QAEF,QAAQ,CAAC,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;QACnC,IAAI,CAAC,QAAQ,EAAE,CAAC;IAClB,CAAC;IAEO,QAAQ;QACd,IAAI,CAAC,IAAI,CAAC,EAAE;YAAE,OAAO;QAErB,MAAM,MAAM,GAAG,IAAI,CAAC,aAAa,CAAC,SAAS,EAAE,CAAC;QAC9C,MAAM,KAAK,GAAG,IAAI,CAAC,cAAc,EAAE,CAAC;QAEpC,IAAI,CAAC,EAAE,CAAC,SAAS,GAAG;;;UAGd,IAAI,CAAC,mBAAmB,CAAC,CAAC,CAAC,2DAA2D,CAAC,CAAC,CAAC,2DAA2D;;;QAGtJ,MAAM,CAAC,EAAE,CAAC,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,EAAE;;;0BAGhC,MAAM,CAAC,MAAM,CAAC,aAAa;;KAEhD,CAAC;IACJ,CAAC;IAEO,WAAW,CAAC,KAAkB;QACpC,OAAO;;;;oBAIS,KAAK,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG;mBACnC,KAAK,CAAC,QAAQ,CAAC,SAAS,IAAI,KAAK,CAAC,QAAQ,CAAC,SAAS;qBAClD,KAAK,CAAC,QAAQ,CAAC,WAAW;;;;;oBAK3B,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG;uBAC9B,KAAK,CAAC,OAAO,CAAC,cAAc;mBAChC,KAAK,CAAC,OAAO,CAAC,WAAW;;;;;oBAKxB,KAAK,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG;mBACpC,KAAK,CAAC,SAAS,CAAC,UAAU,IAAI,KAAK,CAAC,SAAS,CAAC,QAAQ;uBAClD,KAAK,CAAC,SAAS,CAAC,cAAc;;;;;oBAKjC,KAAK,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG;kBACrC,KAAK,CAAC,SAAS,CAAC,UAAU,IAAI,KAAK,CAAC,SAAS,CAAC,OAAO;oBACnD,KAAK,CAAC,SAAS,CAAC,WAAW;;;KAG1C,CAAC;IACJ,CAAC;IAEO,cAAc;QACpB,OAAO;YACL,QAAQ,EAAE,IAAI,CAAC,QAAQ,CAAC,QAAQ,EAAE;YAClC,OAAO,EAAE,IAAI,CAAC,OAAO,CAAC,QAAQ,EAAE;YAChC,SAAS,EAAE,IAAI,CAAC,SAAS,CAAC,QAAQ,EAAE;YACpC,SAAS,EAAE,IAAI,CAAC,SAAS,CAAC,QAAQ,EAAE;SACrC,CAAC;IACJ,CAAC;IAEO,SAAS,CAAC,MAAc,EAAE,MAAc;QAC9C,MAAM,MAAM,GAAG,IAAI,CAAC,aAAa,CAAC,SAAS,EAAE,CAAC;QAE9C,IAAI,MAAM,CAAC,EAAE,CAAC,QAAQ,EAAE,CAAC;YACvB,OAAO,CAAC,GAAG,CAAC,IAAI,MAAM,KAAK,MAAM,EAAE,CAAC,CAAC;QACvC,CAAC;QAED,IAAI,MAAM,CAAC,KAAK,EAAE,CAAC;YACjB,OAAO,CAAC,KAAK,CAAC,IAAI,MAAM,KAAK,MAAM,EAAE,CAAC,CAAC;QACzC,CAAC;IACH,CAAC;IAEO,gBAAgB,CAAC,OAAe,EAAE,OAAiD,MAAM;QAC/F,MAAM,YAAY,GAAG,QAAQ,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;QACnD,YAAY,CAAC,KAAK,CAAC,OAAO,GAAG;;;;;oBAKb,IAAI,KAAK,OAAO,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,IAAI,KAAK,SAAS,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,IAAI,KAAK,SAAS,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,SAAS;;;;;;;;;KASzH,CAAC;QACF,YAAY,CAAC,WAAW,GAAG,OAAO,CAAC;QAEnC,QAAQ,CAAC,IAAI,CAAC,WAAW,CAAC,YAAY,CAAC,CAAC;QAExC,UAAU,CAAC,GAAG,EAAE;YACd,IAAI,YAAY,CAAC,UAAU,EAAE,CAAC;gBAC5B,YAAY,CAAC,UAAU,CAAC,WAAW,CAAC,YAAY,CAAC,CAAC;YACpD,CAAC;QACH,CAAC,EAAE,IAAI,CAAC,CAAC;IACX,CAAC;IAED,qBAAqB;IACd,SAAS;QACd,OAAO,IAAI,CAAC,aAAa,CAAC,SAAS,EAAE,CAAC;IACxC,CAAC;IAEM,YAAY,CAAC,OAA2B;QAC7C,IAAI,CAAC,aAAa,CAAC,YAAY,CAAC,OAAO,CAAC,CAAC;QACzC,MAAM,SAAS,GAAG,IAAI,CAAC,aAAa,CAAC,SAAS,EAAE,CAAC;QAEjD,iCAAiC;QACjC,IAAI,CAAC,QAAQ,CAAC,YAAY,CAAC,SAAS,CAAC,CAAC;QACtC,IAAI,CAAC,OAAO,CAAC,YAAY,CAAC,SAAS,CAAC,CAAC;QACrC,IAAI,CAAC,SAAS,CAAC,YAAY,CAAC,SAAS,CAAC,CAAC;QACvC,IAAI,CAAC,SAAS,CAAC,YAAY,CAAC,SAAS,CAAC,CAAC;QAEvC,4BAA4B;QAC5B,IAAI,CAAC,WAAW,EAAE,CAAC;QACnB,IAAI,CAAC,YAAY,CAAC,SAAS,CAAC,CAAC;QAE7B,IAAI,CAAC,QAAQ,EAAE,CAAC;IAClB,CAAC;IAEM,WAAW;QAChB,IAAI,CAAC,aAAa,CAAC,WAAW,EAAE,CAAC;QACjC,IAAI,CAAC,YAAY,CAAC,EAAE,CAAC,CAAC;IACxB,CAAC;IAEM,QAAQ;QACb,OAAO,IAAI,CAAC,cAAc,EAAE,CAAC;IAC/B,CAAC;IAEM,YAAY,CAAC,UAA8D;QAChF,MAAM,MAAM,GAAG,IAAI,CAAC,SAAS,EAAE,CAAC;QAEhC,QAAQ,UAAU,EAAE,CAAC;YACnB,KAAK,UAAU;gBACb,IAAI,CAAC,YAAY,CAAC,EAAE,QAAQ,EAAE,EAAE,GAAG,MAAM,CAAC,QAAQ,EAAE,OAAO,EAAE,CAAC,MAAM,CAAC,QAAQ,CAAC,OAAO,EAAE,EAAE,CAAC,CAAC;gBAC3F,MAAM;YACR,KAAK,SAAS;gBACZ,IAAI,CAAC,YAAY,CAAC,EAAE,OAAO,EAAE,EAAE,GAAG,MAAM,CAAC,OAAO,EAAE,OAAO,EAAE,CAAC,MAAM,CAAC,OAAO,CAAC,OAAO,EAAE,EAAE,CAAC,CAAC;gBACxF,MAAM;YACR,KAAK,WAAW;gBACd,IAAI,CAAC,YAAY,CAAC,EAAE,SAAS,EAAE,EAAE,GAAG,MAAM,CAAC,SAAS,EAAE,OAAO,EAAE,CAAC,MAAM,CAAC,SAAS,CAAC,OAAO,EAAE,EAAE,CAAC,CAAC;gBAC9F,MAAM;YACR,KAAK,WAAW;gBACd,IAAI,CAAC,YAAY,CAAC,EAAE,SAAS,EAAE,EAAE,GAAG,MAAM,CAAC,SAAS,EAAE,OAAO,EAAE,CAAC,MAAM,CAAC,SAAS,CAAC,OAAO,EAAE,EAAE,CAAC,CAAC;gBAC9F,MAAM;QACV,CAAC;IACH,CAAC;IAEM,OAAO;QACZ,IAAI,CAAC,WAAW,EAAE,CAAC;QAEnB,IAAI,IAAI,CAAC,EAAE,IAAI,IAAI,CAAC,EAAE,CAAC,UAAU,EAAE,CAAC;YAClC,IAAI,CAAC,EAAE,CAAC,UAAU,CAAC,WAAW,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;QAC1C,CAAC;QAED,IAAI,CAAC,OAAO,CAAC,OAAO,EAAE,CAAC;QACvB,IAAI,CAAC,kBAAkB,EAAE,CAAC;QAE1B,OAAO,CAAC,GAAG,CAAC,+BAA+B,CAAC,CAAC;IAC/C,CAAC;CACF;AAED,2CAA2C;AAC3C,IAAI,WAAW,GAA6B,IAAI,CAAC;AAEjD,SAAS,aAAa;IACpB,IAAI,WAAW,EAAE,CAAC;QAChB,WAAW,CAAC,OAAO,EAAE,CAAC;IACxB,CAAC;IAED,WAAW,GAAG,IAAI,iBAAiB,EAAE,CAAC;IAEtC,uCAAuC;IACtC,MAAc,CAAC,iBAAiB,GAAG,WAAW,CAAC;AAClD,CAAC;AAED,kCAAkC;AAClC,IAAI,QAAQ,CAAC,UAAU,KAAK,SAAS,EAAE,CAAC;IACtC,QAAQ,CAAC,gBAAgB,CAAC,kBAAkB,EAAE,aAAa,CAAC,CAAC;AAC/D,CAAC;KAAM,CAAC;IACN,aAAa,EAAE,CAAC;AAClB,CAAC;AAED,iCAAiC;AACjC,IAAI,OAAO,GAAG,QAAQ,CAAC,IAAI,CAAC;AAC5B,IAAI,gBAAgB,CAAC,GAAG,EAAE;IACxB,MAAM,GAAG,GAAG,QAAQ,CAAC,IAAI,CAAC;IAC1B,IAAI,GAAG,KAAK,OAAO,EAAE,CAAC;QACpB,OAAO,GAAG,GAAG,CAAC;QACd,UAAU,CAAC,aAAa,EAAE,IAAI,CAAC,CAAC,CAAC,gCAAgC;IACnE,CAAC;AACH,CAAC,CAAC,CAAC,OAAO,CAAC,QAAQ,EAAE,EAAE,OAAO,EAAE,IAAI,EAAE,SAAS,EAAE,IAAI,EAAE,CAAC,CAAC;AAEzD,2CAAe,iBAAiB,EAAC", "sources": ["webpack://SploopAdvancedMod/webpack/universalModuleDefinition", "webpack://SploopAdvancedMod/./node_modules/eventemitter3/index.js", "webpack://SploopAdvancedMod/webpack/bootstrap", "webpack://SploopAdvancedMod/webpack/runtime/define property getters", "webpack://SploopAdvancedMod/webpack/runtime/hasOwnProperty shorthand", "webpack://SploopAdvancedMod/webpack/runtime/make namespace object", "webpack://SploopAdvancedMod/./src/types/game.ts", "webpack://SploopAdvancedMod/./node_modules/eventemitter3/index.mjs", "webpack://SploopAdvancedMod/./src/utils/gameApi.ts", "webpack://SploopAdvancedMod/./src/config.ts", "webpack://SploopAdvancedMod/./src/modules/autoHats.ts", "webpack://SploopAdvancedMod/./src/modules/autoBuy.ts", "webpack://SploopAdvancedMod/./src/modules/autoMills.ts", "webpack://SploopAdvancedMod/./src/modules/botSystem.ts", "webpack://SploopAdvancedMod/./src/main.ts"], "sourcesContent": ["(function webpackUniversalModuleDefinition(root, factory) {\n\tif(typeof exports === 'object' && typeof module === 'object')\n\t\tmodule.exports = factory();\n\telse if(typeof define === 'function' && define.amd)\n\t\tdefine([], factory);\n\telse if(typeof exports === 'object')\n\t\texports[\"SploopAdvancedMod\"] = factory();\n\telse\n\t\troot[\"SploopAdvancedMod\"] = factory();\n})(this, () => {\nreturn ", "'use strict';\n\nvar has = Object.prototype.hasOwnProperty\n  , prefix = '~';\n\n/**\n * Constructor to create a storage for our `EE` objects.\n * An `Events` instance is a plain object whose properties are event names.\n *\n * @constructor\n * @private\n */\nfunction Events() {}\n\n//\n// We try to not inherit from `Object.prototype`. In some engines creating an\n// instance in this way is faster than calling `Object.create(null)` directly.\n// If `Object.create(null)` is not supported we prefix the event names with a\n// character to make sure that the built-in object properties are not\n// overridden or used as an attack vector.\n//\nif (Object.create) {\n  Events.prototype = Object.create(null);\n\n  //\n  // This hack is needed because the `__proto__` property is still inherited in\n  // some old browsers like Android 4, iPhone 5.1, Opera 11 and Safari 5.\n  //\n  if (!new Events().__proto__) prefix = false;\n}\n\n/**\n * Representation of a single event listener.\n *\n * @param {Function} fn The listener function.\n * @param {*} context The context to invoke the listener with.\n * @param {Boolean} [once=false] Specify if the listener is a one-time listener.\n * @constructor\n * @private\n */\nfunction EE(fn, context, once) {\n  this.fn = fn;\n  this.context = context;\n  this.once = once || false;\n}\n\n/**\n * Add a listener for a given event.\n *\n * @param {EventEmitter} emitter Reference to the `EventEmitter` instance.\n * @param {(String|Symbol)} event The event name.\n * @param {Function} fn The listener function.\n * @param {*} context The context to invoke the listener with.\n * @param {Boolean} once Specify if the listener is a one-time listener.\n * @returns {EventEmitter}\n * @private\n */\nfunction addListener(emitter, event, fn, context, once) {\n  if (typeof fn !== 'function') {\n    throw new TypeError('The listener must be a function');\n  }\n\n  var listener = new EE(fn, context || emitter, once)\n    , evt = prefix ? prefix + event : event;\n\n  if (!emitter._events[evt]) emitter._events[evt] = listener, emitter._eventsCount++;\n  else if (!emitter._events[evt].fn) emitter._events[evt].push(listener);\n  else emitter._events[evt] = [emitter._events[evt], listener];\n\n  return emitter;\n}\n\n/**\n * Clear event by name.\n *\n * @param {EventEmitter} emitter Reference to the `EventEmitter` instance.\n * @param {(String|Symbol)} evt The Event name.\n * @private\n */\nfunction clearEvent(emitter, evt) {\n  if (--emitter._eventsCount === 0) emitter._events = new Events();\n  else delete emitter._events[evt];\n}\n\n/**\n * Minimal `EventEmitter` interface that is molded against the Node.js\n * `EventEmitter` interface.\n *\n * @constructor\n * @public\n */\nfunction EventEmitter() {\n  this._events = new Events();\n  this._eventsCount = 0;\n}\n\n/**\n * Return an array listing the events for which the emitter has registered\n * listeners.\n *\n * @returns {Array}\n * @public\n */\nEventEmitter.prototype.eventNames = function eventNames() {\n  var names = []\n    , events\n    , name;\n\n  if (this._eventsCount === 0) return names;\n\n  for (name in (events = this._events)) {\n    if (has.call(events, name)) names.push(prefix ? name.slice(1) : name);\n  }\n\n  if (Object.getOwnPropertySymbols) {\n    return names.concat(Object.getOwnPropertySymbols(events));\n  }\n\n  return names;\n};\n\n/**\n * Return the listeners registered for a given event.\n *\n * @param {(String|Symbol)} event The event name.\n * @returns {Array} The registered listeners.\n * @public\n */\nEventEmitter.prototype.listeners = function listeners(event) {\n  var evt = prefix ? prefix + event : event\n    , handlers = this._events[evt];\n\n  if (!handlers) return [];\n  if (handlers.fn) return [handlers.fn];\n\n  for (var i = 0, l = handlers.length, ee = new Array(l); i < l; i++) {\n    ee[i] = handlers[i].fn;\n  }\n\n  return ee;\n};\n\n/**\n * Return the number of listeners listening to a given event.\n *\n * @param {(String|Symbol)} event The event name.\n * @returns {Number} The number of listeners.\n * @public\n */\nEventEmitter.prototype.listenerCount = function listenerCount(event) {\n  var evt = prefix ? prefix + event : event\n    , listeners = this._events[evt];\n\n  if (!listeners) return 0;\n  if (listeners.fn) return 1;\n  return listeners.length;\n};\n\n/**\n * Calls each of the listeners registered for a given event.\n *\n * @param {(String|Symbol)} event The event name.\n * @returns {Boolean} `true` if the event had listeners, else `false`.\n * @public\n */\nEventEmitter.prototype.emit = function emit(event, a1, a2, a3, a4, a5) {\n  var evt = prefix ? prefix + event : event;\n\n  if (!this._events[evt]) return false;\n\n  var listeners = this._events[evt]\n    , len = arguments.length\n    , args\n    , i;\n\n  if (listeners.fn) {\n    if (listeners.once) this.removeListener(event, listeners.fn, undefined, true);\n\n    switch (len) {\n      case 1: return listeners.fn.call(listeners.context), true;\n      case 2: return listeners.fn.call(listeners.context, a1), true;\n      case 3: return listeners.fn.call(listeners.context, a1, a2), true;\n      case 4: return listeners.fn.call(listeners.context, a1, a2, a3), true;\n      case 5: return listeners.fn.call(listeners.context, a1, a2, a3, a4), true;\n      case 6: return listeners.fn.call(listeners.context, a1, a2, a3, a4, a5), true;\n    }\n\n    for (i = 1, args = new Array(len -1); i < len; i++) {\n      args[i - 1] = arguments[i];\n    }\n\n    listeners.fn.apply(listeners.context, args);\n  } else {\n    var length = listeners.length\n      , j;\n\n    for (i = 0; i < length; i++) {\n      if (listeners[i].once) this.removeListener(event, listeners[i].fn, undefined, true);\n\n      switch (len) {\n        case 1: listeners[i].fn.call(listeners[i].context); break;\n        case 2: listeners[i].fn.call(listeners[i].context, a1); break;\n        case 3: listeners[i].fn.call(listeners[i].context, a1, a2); break;\n        case 4: listeners[i].fn.call(listeners[i].context, a1, a2, a3); break;\n        default:\n          if (!args) for (j = 1, args = new Array(len -1); j < len; j++) {\n            args[j - 1] = arguments[j];\n          }\n\n          listeners[i].fn.apply(listeners[i].context, args);\n      }\n    }\n  }\n\n  return true;\n};\n\n/**\n * Add a listener for a given event.\n *\n * @param {(String|Symbol)} event The event name.\n * @param {Function} fn The listener function.\n * @param {*} [context=this] The context to invoke the listener with.\n * @returns {EventEmitter} `this`.\n * @public\n */\nEventEmitter.prototype.on = function on(event, fn, context) {\n  return addListener(this, event, fn, context, false);\n};\n\n/**\n * Add a one-time listener for a given event.\n *\n * @param {(String|Symbol)} event The event name.\n * @param {Function} fn The listener function.\n * @param {*} [context=this] The context to invoke the listener with.\n * @returns {EventEmitter} `this`.\n * @public\n */\nEventEmitter.prototype.once = function once(event, fn, context) {\n  return addListener(this, event, fn, context, true);\n};\n\n/**\n * Remove the listeners of a given event.\n *\n * @param {(String|Symbol)} event The event name.\n * @param {Function} fn Only remove the listeners that match this function.\n * @param {*} context Only remove the listeners that have this context.\n * @param {Boolean} once Only remove one-time listeners.\n * @returns {EventEmitter} `this`.\n * @public\n */\nEventEmitter.prototype.removeListener = function removeListener(event, fn, context, once) {\n  var evt = prefix ? prefix + event : event;\n\n  if (!this._events[evt]) return this;\n  if (!fn) {\n    clearEvent(this, evt);\n    return this;\n  }\n\n  var listeners = this._events[evt];\n\n  if (listeners.fn) {\n    if (\n      listeners.fn === fn &&\n      (!once || listeners.once) &&\n      (!context || listeners.context === context)\n    ) {\n      clearEvent(this, evt);\n    }\n  } else {\n    for (var i = 0, events = [], length = listeners.length; i < length; i++) {\n      if (\n        listeners[i].fn !== fn ||\n        (once && !listeners[i].once) ||\n        (context && listeners[i].context !== context)\n      ) {\n        events.push(listeners[i]);\n      }\n    }\n\n    //\n    // Reset the array, or remove it completely if we have no more listeners.\n    //\n    if (events.length) this._events[evt] = events.length === 1 ? events[0] : events;\n    else clearEvent(this, evt);\n  }\n\n  return this;\n};\n\n/**\n * Remove all listeners, or those of the specified event.\n *\n * @param {(String|Symbol)} [event] The event name.\n * @returns {EventEmitter} `this`.\n * @public\n */\nEventEmitter.prototype.removeAllListeners = function removeAllListeners(event) {\n  var evt;\n\n  if (event) {\n    evt = prefix ? prefix + event : event;\n    if (this._events[evt]) clearEvent(this, evt);\n  } else {\n    this._events = new Events();\n    this._eventsCount = 0;\n  }\n\n  return this;\n};\n\n//\n// Alias methods names because people roll like that.\n//\nEventEmitter.prototype.off = EventEmitter.prototype.removeListener;\nEventEmitter.prototype.addListener = EventEmitter.prototype.on;\n\n//\n// Expose the prefix.\n//\nEventEmitter.prefixed = prefix;\n\n//\n// Allow `EventEmitter` to be imported as module namespace.\n//\nEventEmitter.EventEmitter = EventEmitter;\n\n//\n// Expose the module.\n//\nif ('undefined' !== typeof module) {\n  module.exports = EventEmitter;\n}\n", "// The module cache\nvar __webpack_module_cache__ = {};\n\n// The require function\nfunction __webpack_require__(moduleId) {\n\t// Check if module is in cache\n\tvar cachedModule = __webpack_module_cache__[moduleId];\n\tif (cachedModule !== undefined) {\n\t\treturn cachedModule.exports;\n\t}\n\t// Create a new module (and put it into the cache)\n\tvar module = __webpack_module_cache__[moduleId] = {\n\t\t// no module.id needed\n\t\t// no module.loaded needed\n\t\texports: {}\n\t};\n\n\t// Execute the module function\n\t__webpack_modules__[moduleId](module, module.exports, __webpack_require__);\n\n\t// Return the exports of the module\n\treturn module.exports;\n}\n\n", "// define getter functions for harmony exports\n__webpack_require__.d = (exports, definition) => {\n\tfor(var key in definition) {\n\t\tif(__webpack_require__.o(definition, key) && !__webpack_require__.o(exports, key)) {\n\t\t\tObject.defineProperty(exports, key, { enumerable: true, get: definition[key] });\n\t\t}\n\t}\n};", "__webpack_require__.o = (obj, prop) => (Object.prototype.hasOwnProperty.call(obj, prop))", "// define __esModule on exports\n__webpack_require__.r = (exports) => {\n\tif(typeof Symbol !== 'undefined' && Symbol.toStringTag) {\n\t\tObject.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });\n\t}\n\tObject.defineProperty(exports, '__esModule', { value: true });\n};", "// Sploop.io Game Type Definitions\n\nexport interface Vector2 {\n  x: number;\n  y: number;\n}\n\nexport interface Player {\n  id: string;\n  name: string;\n  position: Vector2;\n  health: number;\n  maxHealth: number;\n  resources: Resources;\n  inventory: InventoryItem[];\n  hat: Hat | null;\n  weapon: Weapon | null;\n  level: number;\n  experience: number;\n  clan: string | null;\n  isBot: boolean;\n}\n\nexport interface Resources {\n  wood: number;\n  stone: number;\n  food: number;\n  gold: number;\n  points: number;\n}\n\nexport interface InventoryItem {\n  id: string;\n  type: ItemType;\n  quantity: number;\n  durability?: number;\n}\n\nexport interface Hat {\n  id: string;\n  name: string;\n  type: HatType;\n  stats: HatStats;\n  cost: number;\n  unlockLevel: number;\n}\n\nexport interface HatStats {\n  healthBonus?: number;\n  speedBonus?: number;\n  damageBonus?: number;\n  resourceBonus?: number;\n  experienceBonus?: number;\n}\n\nexport interface Weapon {\n  id: string;\n  name: string;\n  damage: number;\n  range: number;\n  speed: number;\n  cost: number;\n}\n\nexport interface Building {\n  id: string;\n  type: BuildingType;\n  position: Vector2;\n  health: number;\n  maxHealth: number;\n  owner: string;\n  level: number;\n  isActive: boolean;\n}\n\nexport interface Mill extends Building {\n  resourceType: ResourceType;\n  productionRate: number;\n  storage: number;\n  maxStorage: number;\n}\n\nexport enum ItemType {\n  WEAPON = 'weapon',\n  HAT = 'hat',\n  FOOD = 'food',\n  MATERIAL = 'material',\n  TOOL = 'tool'\n}\n\nexport enum HatType {\n  NONE = 'none',\n  MARKSMAN = 'marksman',\n  BUSH = 'bush',\n  BERSERKER = 'berserker',\n  JUNGLE = 'jungle',\n  CRYSTAL = 'crystal',\n  SPACE = 'space',\n  CYBORG = 'cyborg',\n  MONKEY = 'monkey',\n  ELF = 'elf',\n  KNIGHT = 'knight',\n  SAMURAI = 'samurai',\n  ANGEL = 'angel',\n  DEVIL = 'devil'\n}\n\nexport enum BuildingType {\n  MILL = 'mill',\n  SPIKE = 'spike',\n  WALL = 'wall',\n  WINDMILL = 'windmill',\n  MINE = 'mine',\n  PIT_TRAP = 'pit_trap',\n  BOOST_PAD = 'boost_pad',\n  TURRET = 'turret',\n  PLATFORM = 'platform',\n  HEALING_PAD = 'healing_pad',\n  SPAWN_PAD = 'spawn_pad',\n  BLOCKER = 'blocker',\n  TELEPORTER = 'teleporter'\n}\n\nexport enum ResourceType {\n  WOOD = 'wood',\n  STONE = 'stone',\n  FOOD = 'food',\n  GOLD = 'gold'\n}\n\nexport interface GameState {\n  player: Player;\n  players: Map<string, Player>;\n  buildings: Map<string, Building>;\n  items: Map<string, InventoryItem>;\n  gameObjects: Map<string, GameObject>;\n  isInGame: boolean;\n  gameMode: string;\n  serverInfo: ServerInfo;\n}\n\nexport interface GameObject {\n  id: string;\n  type: string;\n  position: Vector2;\n  angle: number;\n  scale: number;\n  visible: boolean;\n}\n\nexport interface ServerInfo {\n  region: string;\n  playerCount: number;\n  maxPlayers: number;\n  gameMode: string;\n  mapSize: number;\n}\n\n// Game API interfaces\nexport interface GameAPI {\n  getPlayer(): Player | null;\n  getPlayers(): Map<string, Player>;\n  getBuildings(): Map<string, Building>;\n  getGameState(): GameState;\n  sendAction(action: GameAction): void;\n  sendChat(message: string): void;\n  isInGame(): boolean;\n}\n\nexport interface GameAction {\n  type: ActionType;\n  data: any;\n  timestamp: number;\n}\n\nexport enum ActionType {\n  MOVE = 'move',\n  ATTACK = 'attack',\n  BUILD = 'build',\n  UPGRADE = 'upgrade',\n  BUY = 'buy',\n  EQUIP = 'equip',\n  USE_ITEM = 'use_item',\n  CHAT = 'chat',\n  JOIN_CLAN = 'join_clan',\n  LEAVE_CLAN = 'leave_clan'\n}\n", "import EventEmitter from './index.js'\n\nexport { EventEmitter }\nexport default EventEmitter\n", "import { Game<PERSON><PERSON>, GameState, Player, Building, GameAction, ActionType, Vector2 } from '@/types/game';\nimport { EventEmitter } from 'eventemitter3';\n\ndeclare global {\n  interface Window {\n    game: any;\n    io: any;\n    player: any;\n    players: any;\n    buildings: any;\n    items: any;\n    socket: any;\n  }\n}\n\nexport class SploopGameAPI extends EventEmitter<string | symbol, any> implements GameAPI {\n  private gameState: GameState | null = null;\n  private updateInterval: number | null = null;\n  private lastActionTime = 0;\n  private actionQueue: GameAction[] = [];\n  \n  constructor() {\n    super();\n    this.initialize();\n  }\n  \n  private initialize(): void {\n    // Wait for game to load\n    const checkGame = () => {\n      if (this.isGameLoaded()) {\n        this.startMonitoring();\n        super.emit('gameLoaded');\n      } else {\n        setTimeout(checkGame, 1000);\n      }\n    };\n    checkGame();\n  }\n  \n  private isGameLoaded(): boolean {\n    return typeof window !== 'undefined' && \n           window.game && \n           window.player && \n           window.socket;\n  }\n  \n  private startMonitoring(): void {\n    if (this.updateInterval) {\n      clearInterval(this.updateInterval);\n    }\n    \n    this.updateInterval = window.setInterval(() => {\n      this.updateGameState();\n      this.processActionQueue();\n    }, 50);\n  }\n  \n  private updateGameState(): void {\n    if (!this.isGameLoaded()) return;\n    \n    try {\n      const newState: GameState = {\n        player: this.parsePlayer(window.player),\n        players: this.parsePlayers(window.players),\n        buildings: this.parseBuildings(window.buildings),\n        items: new Map(),\n        gameObjects: new Map(),\n        isInGame: this.isInGame(),\n        gameMode: window.game?.gameMode || 'unknown',\n        serverInfo: {\n          region: window.game?.region || 'unknown',\n          playerCount: window.players?.length || 0,\n          maxPlayers: window.game?.maxPlayers || 0,\n          gameMode: window.game?.gameMode || 'unknown',\n          mapSize: window.game?.mapSize || 0\n        }\n      };\n      \n      this.gameState = newState;\n      super.emit('stateUpdate', newState);\n    } catch (error) {\n      console.error('Error updating game state:', error);\n    }\n  }\n  \n  private parsePlayer(playerData: any): Player {\n    return {\n      id: playerData?.id || '',\n      name: playerData?.name || '',\n      position: { x: playerData?.x || 0, y: playerData?.y || 0 },\n      health: playerData?.health || 0,\n      maxHealth: playerData?.maxHealth || 100,\n      resources: {\n        wood: playerData?.wood || 0,\n        stone: playerData?.stone || 0,\n        food: playerData?.food || 0,\n        gold: playerData?.gold || 0,\n        points: playerData?.points || 0\n      },\n      inventory: playerData?.inventory || [],\n      hat: playerData?.hat || null,\n      weapon: playerData?.weapon || null,\n      level: playerData?.level || 1,\n      experience: playerData?.experience || 0,\n      clan: playerData?.clan || null,\n      isBot: false\n    };\n  }\n  \n  private parsePlayers(playersData: any): Map<string, Player> {\n    const players = new Map<string, Player>();\n    \n    if (Array.isArray(playersData)) {\n      playersData.forEach((playerData: any) => {\n        const player = this.parsePlayer(playerData);\n        players.set(player.id, player);\n      });\n    }\n    \n    return players;\n  }\n  \n  private parseBuildings(buildingsData: any): Map<string, Building> {\n    const buildings = new Map<string, Building>();\n    \n    if (Array.isArray(buildingsData)) {\n      buildingsData.forEach((buildingData: any) => {\n        const building: Building = {\n          id: buildingData?.id || '',\n          type: buildingData?.type || 'unknown',\n          position: { x: buildingData?.x || 0, y: buildingData?.y || 0 },\n          health: buildingData?.health || 0,\n          maxHealth: buildingData?.maxHealth || 100,\n          owner: buildingData?.owner || '',\n          level: buildingData?.level || 1,\n          isActive: buildingData?.isActive || false\n        };\n        buildings.set(building.id, building);\n      });\n    }\n    \n    return buildings;\n  }\n  \n  public getPlayer(): Player | null {\n    return this.gameState?.player || null;\n  }\n  \n  public getPlayers(): Map<string, Player> {\n    return this.gameState?.players || new Map();\n  }\n  \n  public getBuildings(): Map<string, Building> {\n    return this.gameState?.buildings || new Map();\n  }\n  \n  public getGameState(): GameState {\n    return this.gameState || {\n      player: {} as Player,\n      players: new Map(),\n      buildings: new Map(),\n      items: new Map(),\n      gameObjects: new Map(),\n      isInGame: false,\n      gameMode: 'unknown',\n      serverInfo: {\n        region: 'unknown',\n        playerCount: 0,\n        maxPlayers: 0,\n        gameMode: 'unknown',\n        mapSize: 0\n      }\n    };\n  }\n  \n  public sendAction(action: GameAction): void {\n    this.actionQueue.push(action);\n  }\n  \n  private processActionQueue(): void {\n    if (this.actionQueue.length === 0) return;\n    \n    const now = Date.now();\n    if (now - this.lastActionTime < 200) return; // Rate limiting\n    \n    const action = this.actionQueue.shift();\n    if (!action) return;\n    \n    try {\n      this.executeAction(action);\n      this.lastActionTime = now;\n    } catch (error) {\n      console.error('Error executing action:', error);\n    }\n  }\n  \n  private executeAction(action: GameAction): void {\n    if (!this.isGameLoaded() || !window.socket) return;\n    \n    switch (action.type) {\n      case ActionType.MOVE:\n        this.move(action.data.x, action.data.y);\n        break;\n      case ActionType.ATTACK:\n        this.attack(action.data.angle);\n        break;\n      case ActionType.BUILD:\n        this.build(action.data.type, action.data.x, action.data.y);\n        break;\n      case ActionType.BUY:\n        this.buy(action.data.itemId);\n        break;\n      case ActionType.EQUIP:\n        this.equip(action.data.itemId);\n        break;\n      case ActionType.CHAT:\n        this.sendChat(action.data.message);\n        break;\n      default:\n        console.warn('Unknown action type:', action.type);\n    }\n  }\n  \n  private move(x: number, y: number): void {\n    if (window.socket && window.socket.emit) {\n      window.socket.emit('move', { x, y });\n    }\n  }\n  \n  private attack(angle: number): void {\n    if (window.socket && window.socket.emit) {\n      window.socket.emit('attack', { angle });\n    }\n  }\n  \n  private build(type: string, x: number, y: number): void {\n    if (window.socket && window.socket.emit) {\n      window.socket.emit('build', { type, x, y });\n    }\n  }\n  \n  private buy(itemId: string): void {\n    if (window.socket && window.socket.emit) {\n      window.socket.emit('buy', { itemId });\n    }\n  }\n  \n  private equip(itemId: string): void {\n    if (window.socket && window.socket.emit) {\n      window.socket.emit('equip', { itemId });\n    }\n  }\n  \n  public sendChat(message: string): void {\n    if (window.socket && window.socket.emit) {\n      window.socket.emit('chat', { message });\n    }\n  }\n  \n  public isInGame(): boolean {\n    return this.isGameLoaded() && \n           window.game?.inGame === true && \n           this.gameState?.player?.id !== '';\n  }\n  \n  public calculateDistance(pos1: Vector2, pos2: Vector2): number {\n    const dx = pos1.x - pos2.x;\n    const dy = pos1.y - pos2.y;\n    return Math.sqrt(dx * dx + dy * dy);\n  }\n  \n  public calculateAngle(from: Vector2, to: Vector2): number {\n    return Math.atan2(to.y - from.y, to.x - from.x);\n  }\n  \n  public destroy(): void {\n    if (this.updateInterval) {\n      clearInterval(this.updateInterval);\n      this.updateInterval = null;\n    } \n    super.removeAllListeners();\n  }\n}\n", "import { HatType, ResourceType } from '@/types/game';\n\nexport interface ModConfig {\n  // General settings\n  enabled: boolean;\n  debug: boolean;\n  safeMode: boolean;\n  updateInterval: number;\n  \n  // Auto Hat settings\n  autoHats: {\n    enabled: boolean;\n    priority: HatType[];\n    autoUpgrade: boolean;\n    switchBasedOnSituation: boolean;\n    combatHat: HatType;\n    farmingHat: HatType;\n    explorationHat: HatType;\n  };\n  \n  // Auto Buy settings\n  autoBuy: {\n    enabled: boolean;\n    priorityItems: string[];\n    maxSpendPercentage: number;\n    reserveResources: {\n      wood: number;\n      stone: number;\n      food: number;\n      gold: number;\n    };\n    buyHats: boolean;\n    buyWeapons: boolean;\n    buyUpgrades: boolean;\n  };\n  \n  // Auto Mills settings\n  autoMills: {\n    enabled: boolean;\n    maxMills: number;\n    resourcePriority: ResourceType[];\n    autoUpgrade: boolean;\n    optimalPlacement: boolean;\n    protectMills: boolean;\n    millSpacing: number;\n    preferredLocations: {\n      nearResources: boolean;\n      nearBase: boolean;\n      hiddenAreas: boolean;\n    };\n  };\n  \n  // Bot System settings\n  botSystem: {\n    enabled: boolean;\n    maxBots: number;\n    botBehaviors: {\n      farming: boolean;\n      combat: boolean;\n      building: boolean;\n      exploration: boolean;\n      protection: boolean;\n    };\n    coordination: {\n      shareResources: boolean;\n      groupMovement: boolean;\n      defendTogether: boolean;\n    };\n    intelligence: {\n      avoidPlayers: boolean;\n      learnFromActions: boolean;\n      adaptToSituation: boolean;\n    };\n  };\n  \n  // Safety settings\n  safety: {\n    antiDetection: boolean;\n    randomizeActions: boolean;\n    humanLikeMovement: boolean;\n    pauseOnDanger: boolean;\n    emergencyStop: string; // Key combination\n    maxActionsPerSecond: number;\n  };\n  \n  // UI settings\n  ui: {\n    showOverlay: boolean;\n    showStats: boolean;\n    showLogs: boolean;\n    overlayPosition: 'top-left' | 'top-right' | 'bottom-left' | 'bottom-right';\n    transparency: number;\n  };\n}\n\nexport const DEFAULT_CONFIG: ModConfig = {\n  enabled: true,\n  debug: false,\n  safeMode: true,\n  updateInterval: 100,\n  \n  autoHats: {\n    enabled: true,\n    priority: [\n      HatType.ANGEL,\n      HatType.DEVIL,\n      HatType.SAMURAI,\n      HatType.KNIGHT,\n      HatType.CYBORG,\n      HatType.CRYSTAL,\n      HatType.BERSERKER,\n      HatType.MARKSMAN\n    ],\n    autoUpgrade: true,\n    switchBasedOnSituation: true,\n    combatHat: HatType.BERSERKER,\n    farmingHat: HatType.MONKEY,\n    explorationHat: HatType.BUSH\n  },\n  \n  autoBuy: {\n    enabled: true,\n    priorityItems: [\n      'weapon_upgrade',\n      'hat_upgrade',\n      'health_potion',\n      'speed_boost'\n    ],\n    maxSpendPercentage: 80,\n    reserveResources: {\n      wood: 100,\n      stone: 100,\n      food: 50,\n      gold: 50\n    },\n    buyHats: true,\n    buyWeapons: true,\n    buyUpgrades: true\n  },\n  \n  autoMills: {\n    enabled: true,\n    maxMills: 8,\n    resourcePriority: [\n      ResourceType.GOLD,\n      ResourceType.STONE,\n      ResourceType.WOOD,\n      ResourceType.FOOD\n    ],\n    autoUpgrade: true,\n    optimalPlacement: true,\n    protectMills: true,\n    millSpacing: 200,\n    preferredLocations: {\n      nearResources: true,\n      nearBase: false,\n      hiddenAreas: true\n    }\n  },\n  \n  botSystem: {\n    enabled: false, // Disabled by default for safety\n    maxBots: 3,\n    botBehaviors: {\n      farming: true,\n      combat: false,\n      building: true,\n      exploration: true,\n      protection: true\n    },\n    coordination: {\n      shareResources: true,\n      groupMovement: false,\n      defendTogether: true\n    },\n    intelligence: {\n      avoidPlayers: true,\n      learnFromActions: true,\n      adaptToSituation: true\n    }\n  },\n  \n  safety: {\n    antiDetection: true,\n    randomizeActions: true,\n    humanLikeMovement: true,\n    pauseOnDanger: true,\n    emergencyStop: 'Ctrl+Shift+X',\n    maxActionsPerSecond: 5\n  },\n  \n  ui: {\n    showOverlay: true,\n    showStats: true,\n    showLogs: false,\n    overlayPosition: 'top-right',\n    transparency: 0.8\n  }\n};\n\nexport class ConfigManager {\n  private config: ModConfig;\n  private readonly STORAGE_KEY = 'sploop_advanced_mod_config';\n  \n  constructor() {\n    this.config = this.loadConfig();\n  }\n  \n  public getConfig(): ModConfig {\n    return { ...this.config };\n  }\n  \n  public updateConfig(updates: Partial<ModConfig>): void {\n    this.config = { ...this.config, ...updates };\n    this.saveConfig();\n  }\n  \n  public resetConfig(): void {\n    this.config = { ...DEFAULT_CONFIG };\n    this.saveConfig();\n  }\n  \n  private loadConfig(): ModConfig {\n    try {\n      const stored = localStorage.getItem(this.STORAGE_KEY);\n      if (stored) {\n        const parsed = JSON.parse(stored);\n        return { ...DEFAULT_CONFIG, ...parsed };\n      }\n    } catch (error) {\n      console.warn('Failed to load config, using defaults:', error);\n    }\n    return { ...DEFAULT_CONFIG };\n  }\n  \n  private saveConfig(): void {\n    try {\n      localStorage.setItem(this.STORAGE_KEY, JSON.stringify(this.config));\n    } catch (error) {\n      console.error('Failed to save config:', error);\n    }\n  }\n}\n", "import { SploopGameAPI } from '@/utils/gameApi';\nimport { ModConfig } from '@/config';\nimport { Hat, HatType, Player, ActionType } from '@/types/game';\nimport { EventEmitter } from 'eventemitter3';\n\nexport interface HatInfo {\n  id: string;\n  type: HatType;\n  name: string;\n  cost: number;\n  unlockLevel: number;\n  owned: boolean;\n  equipped: boolean;\n  stats: {\n    healthBonus: number;\n    speedBonus: number;\n    damageBonus: number;\n    resourceBonus: number;\n    experienceBonus: number;\n  };\n}\n\nexport class AutoHatsModule extends EventEmitter {\n  private gameApi: SploopGameAPI;\n  private config: ModConfig;\n  private enabled = false;\n  private updateInterval: number | null = null;\n  private lastHatCheck = 0;\n  private currentSituation: 'combat' | 'farming' | 'exploration' | 'idle' = 'idle';\n  \n  // Hat database with stats and costs\n  private readonly HAT_DATABASE: Record<HatType, HatInfo> = {\n    [HatType.NONE]: {\n      id: 'hat_none',\n      type: HatType.NONE,\n      name: 'No Hat',\n      cost: 0,\n      unlockLevel: 0,\n      owned: true,\n      equipped: false,\n      stats: { healthBonus: 0, speedBonus: 0, damageBonus: 0, resourceBonus: 0, experienceBonus: 0 }\n    },\n    [HatType.MARKSMAN]: {\n      id: 'hat_marksman',\n      type: HatType.MARKSMAN,\n      name: 'Marksman Hat',\n      cost: 7000,\n      unlockLevel: 1,\n      owned: false,\n      equipped: false,\n      stats: { healthBonus: 0, speedBonus: 0, damageBonus: 25, resourceBonus: 0, experienceBonus: 0 }\n    },\n    [HatType.BUSH]: {\n      id: 'hat_bush',\n      type: HatType.BUSH,\n      name: 'Bush Hat',\n      cost: 3000,\n      unlockLevel: 1,\n      owned: false,\n      equipped: false,\n      stats: { healthBonus: 20, speedBonus: 0, damageBonus: 0, resourceBonus: 0, experienceBonus: 0 }\n    },\n    [HatType.BERSERKER]: {\n      id: 'hat_berserker',\n      type: HatType.BERSERKER,\n      name: 'Berserker Hat',\n      cost: 12000,\n      unlockLevel: 7,\n      owned: false,\n      equipped: false,\n      stats: { healthBonus: 0, speedBonus: 15, damageBonus: 35, resourceBonus: 0, experienceBonus: 0 }\n    },\n    [HatType.JUNGLE]: {\n      id: 'hat_jungle',\n      type: HatType.JUNGLE,\n      name: 'Jungle Hat',\n      cost: 15000,\n      unlockLevel: 6,\n      owned: false,\n      equipped: false,\n      stats: { healthBonus: 30, speedBonus: 0, damageBonus: 0, resourceBonus: 15, experienceBonus: 0 }\n    },\n    [HatType.CRYSTAL]: {\n      id: 'hat_crystal',\n      type: HatType.CRYSTAL,\n      name: 'Crystal Hat',\n      cost: 25000,\n      unlockLevel: 12,\n      owned: false,\n      equipped: false,\n      stats: { healthBonus: 0, speedBonus: 0, damageBonus: 0, resourceBonus: 25, experienceBonus: 25 }\n    },\n    [HatType.SPACE]: {\n      id: 'hat_space',\n      type: HatType.SPACE,\n      name: 'Space Hat',\n      cost: 30000,\n      unlockLevel: 15,\n      owned: false,\n      equipped: false,\n      stats: { healthBonus: 40, speedBonus: 20, damageBonus: 0, resourceBonus: 0, experienceBonus: 0 }\n    },\n    [HatType.CYBORG]: {\n      id: 'hat_cyborg',\n      type: HatType.CYBORG,\n      name: 'Cyborg Hat',\n      cost: 50000,\n      unlockLevel: 18,\n      owned: false,\n      equipped: false,\n      stats: { healthBonus: 25, speedBonus: 0, damageBonus: 40, resourceBonus: 0, experienceBonus: 15 }\n    },\n    [HatType.MONKEY]: {\n      id: 'hat_monkey',\n      type: HatType.MONKEY,\n      name: 'Monkey Hat',\n      cost: 8000,\n      unlockLevel: 4,\n      owned: false,\n      equipped: false,\n      stats: { healthBonus: 0, speedBonus: 25, damageBonus: 0, resourceBonus: 20, experienceBonus: 0 }\n    },\n    [HatType.ELF]: {\n      id: 'hat_elf',\n      type: HatType.ELF,\n      name: 'Elf Hat',\n      cost: 20000,\n      unlockLevel: 9,\n      owned: false,\n      equipped: false,\n      stats: { healthBonus: 0, speedBonus: 0, damageBonus: 20, resourceBonus: 30, experienceBonus: 0 }\n    },\n    [HatType.KNIGHT]: {\n      id: 'hat_knight',\n      type: HatType.KNIGHT,\n      name: 'Knight Hat',\n      cost: 35000,\n      unlockLevel: 14,\n      owned: false,\n      equipped: false,\n      stats: { healthBonus: 50, speedBonus: 0, damageBonus: 15, resourceBonus: 0, experienceBonus: 0 }\n    },\n    [HatType.SAMURAI]: {\n      id: 'hat_samurai',\n      type: HatType.SAMURAI,\n      name: 'Samurai Hat',\n      cost: 70000,\n      unlockLevel: 20,\n      owned: false,\n      equipped: false,\n      stats: { healthBonus: 35, speedBonus: 10, damageBonus: 50, resourceBonus: 0, experienceBonus: 0 }\n    },\n    [HatType.ANGEL]: {\n      id: 'hat_angel',\n      type: HatType.ANGEL,\n      name: 'Angel Hat',\n      cost: 100000,\n      unlockLevel: 25,\n      owned: false,\n      equipped: false,\n      stats: { healthBonus: 60, speedBonus: 15, damageBonus: 30, resourceBonus: 20, experienceBonus: 30 }\n    },\n    [HatType.DEVIL]: {\n      id: 'hat_devil',\n      type: HatType.DEVIL,\n      name: 'Devil Hat',\n      cost: 150000,\n      unlockLevel: 30,\n      owned: false,\n      equipped: false,\n      stats: { healthBonus: 40, speedBonus: 20, damageBonus: 60, resourceBonus: 15, experienceBonus: 25 }\n    }\n  };\n  \n  constructor(gameApi: SploopGameAPI, config: ModConfig) {\n    super();\n    this.gameApi = gameApi;\n    this.config = config;\n    \n    this.gameApi.on('stateUpdate', this.onGameStateUpdate.bind(this));\n  }\n  \n  public start(): void {\n    if (this.enabled) return;\n    \n    this.enabled = true;\n    this.updateInterval = window.setInterval(() => {\n      this.update();\n    }, this.config.updateInterval);\n    \n    this.emit('started');\n    console.log('[AutoHats] Module started');\n  }\n  \n  public stop(): void {\n    if (!this.enabled) return;\n    \n    this.enabled = false;\n    if (this.updateInterval) {\n      clearInterval(this.updateInterval);\n      this.updateInterval = null;\n    }\n    \n    this.emit('stopped');\n    console.log('[AutoHats] Module stopped');\n  }\n  \n  public updateConfig(config: ModConfig): void {\n    this.config = config;\n  }\n  \n  private onGameStateUpdate(): void {\n    if (!this.enabled || !this.config.autoHats.enabled) return;\n    \n    this.updateHatOwnership();\n    this.analyzeSituation();\n  }\n  \n  private update(): void {\n    if (!this.enabled || !this.config.autoHats.enabled || !this.gameApi.isInGame()) return;\n    \n    const now = Date.now();\n    if (now - this.lastHatCheck < 2000) return; // Check every 2 seconds\n    \n    this.lastHatCheck = now;\n    \n    try {\n      this.processAutoHats();\n    } catch (error) {\n      console.error('[AutoHats] Error in update:', error);\n    }\n  }\n  \n  private updateHatOwnership(): void {\n    const player = this.gameApi.getPlayer();\n    if (!player) return;\n    \n    // Update owned hats based on player inventory\n    Object.values(this.HAT_DATABASE).forEach(hat => {\n      hat.owned = this.isHatOwned(hat.type, player);\n      hat.equipped = this.isHatEquipped(hat.type, player);\n    });\n  }\n  \n  private isHatOwned(hatType: HatType, player: Player): boolean {\n    if (hatType === HatType.NONE) return true;\n    \n    // Check if hat is in inventory\n    return player.inventory.some(item => \n      item.type === 'hat' && item.id.includes(hatType)\n    );\n  }\n  \n  private isHatEquipped(hatType: HatType, player: Player): boolean {\n    if (!player.hat) return hatType === HatType.NONE;\n    return player.hat.type === hatType;\n  }\n  \n  private analyzeSituation(): void {\n    const player = this.gameApi.getPlayer();\n    const players = this.gameApi.getPlayers();\n    \n    if (!player) return;\n    \n    // Determine current situation based on game state\n    const nearbyEnemies = Array.from(players.values()).filter(p => \n      p.id !== player.id && \n      this.gameApi.calculateDistance(player.position, p.position) < 300\n    );\n    \n    const isLowHealth = player.health < player.maxHealth * 0.5;\n    const hasResources = Object.values(player.resources).some(r => r > 100);\n    \n    if (nearbyEnemies.length > 0 || isLowHealth) {\n      this.currentSituation = 'combat';\n    } else if (hasResources) {\n      this.currentSituation = 'farming';\n    } else {\n      this.currentSituation = 'exploration';\n    }\n  }\n  \n  private processAutoHats(): void {\n    const player = this.gameApi.getPlayer();\n    if (!player) return;\n    \n    let targetHat: HatType;\n    \n    if (this.config.autoHats.switchBasedOnSituation) {\n      targetHat = this.getOptimalHatForSituation();\n    } else {\n      targetHat = this.getBestAvailableHat();\n    }\n    \n    // Check if we need to buy the target hat\n    if (this.config.autoHats.autoUpgrade && !this.HAT_DATABASE[targetHat].owned) {\n      this.tryBuyHat(targetHat);\n    }\n    \n    // Equip the target hat if we own it and it's not already equipped\n    if (this.HAT_DATABASE[targetHat].owned && !this.HAT_DATABASE[targetHat].equipped) {\n      this.equipHat(targetHat);\n    }\n  }\n  \n  private getOptimalHatForSituation(): HatType {\n    switch (this.currentSituation) {\n      case 'combat':\n        return this.getBestOwnedHat([this.config.autoHats.combatHat]) || \n               this.getBestAvailableHat();\n      case 'farming':\n        return this.getBestOwnedHat([this.config.autoHats.farmingHat]) || \n               this.getBestAvailableHat();\n      case 'exploration':\n        return this.getBestOwnedHat([this.config.autoHats.explorationHat]) || \n               this.getBestAvailableHat();\n      default:\n        return this.getBestAvailableHat();\n    }\n  }\n  \n  private getBestOwnedHat(preferred: HatType[] = []): HatType | null {\n    // First check preferred hats\n    for (const hatType of preferred) {\n      if (this.HAT_DATABASE[hatType].owned) {\n        return hatType;\n      }\n    }\n    \n    // Then check priority list\n    for (const hatType of this.config.autoHats.priority) {\n      if (this.HAT_DATABASE[hatType].owned) {\n        return hatType;\n      }\n    }\n    \n    return null;\n  }\n  \n  private getBestAvailableHat(): HatType {\n    const player = this.gameApi.getPlayer();\n    if (!player) return HatType.NONE;\n    \n    // Find the best hat we can afford and have unlocked\n    for (const hatType of this.config.autoHats.priority) {\n      const hat = this.HAT_DATABASE[hatType];\n      if (hat.owned || \n          (player.level >= hat.unlockLevel && player.resources.gold >= hat.cost)) {\n        return hatType;\n      }\n    }\n    \n    return HatType.NONE;\n  }\n  \n  private tryBuyHat(hatType: HatType): void {\n    const player = this.gameApi.getPlayer();\n    const hat = this.HAT_DATABASE[hatType];\n    \n    if (!player || hat.owned) return;\n    \n    if (player.level >= hat.unlockLevel && player.resources.gold >= hat.cost) {\n      this.gameApi.sendAction({\n        type: ActionType.BUY,\n        data: { itemId: hat.id },\n        timestamp: Date.now()\n      });\n      \n      this.emit('hatPurchased', { hatType, cost: hat.cost });\n      console.log(`[AutoHats] Purchased ${hat.name} for ${hat.cost} gold`);\n    }\n  }\n  \n  private equipHat(hatType: HatType): void {\n    const hat = this.HAT_DATABASE[hatType];\n    \n    this.gameApi.sendAction({\n      type: ActionType.EQUIP,\n      data: { itemId: hat.id },\n      timestamp: Date.now()\n    });\n    \n    this.emit('hatEquipped', { hatType });\n    console.log(`[AutoHats] Equipped ${hat.name}`);\n  }\n  \n  public getHatInfo(hatType: HatType): HatInfo {\n    return { ...this.HAT_DATABASE[hatType] };\n  }\n  \n  public getAllHats(): HatInfo[] {\n    return Object.values(this.HAT_DATABASE).map(hat => ({ ...hat }));\n  }\n  \n  public getCurrentSituation(): string {\n    return this.currentSituation;\n  }\n  \n  public getStats(): any {\n    const ownedHats = Object.values(this.HAT_DATABASE).filter(h => h.owned).length;\n    const totalHats = Object.values(this.HAT_DATABASE).length;\n    \n    return {\n      enabled: this.enabled,\n      ownedHats,\n      totalHats,\n      currentSituation: this.currentSituation,\n      equippedHat: Object.values(this.HAT_DATABASE).find(h => h.equipped)?.name || 'None'\n    };\n  }\n}\n", "import { SploopGameAPI } from '@/utils/gameApi';\nimport { ModConfig } from '@/config';\nimport { Player, ActionType, Resources } from '@/types/game';\nimport { EventEmitter } from 'eventemitter3';\n\nexport interface ShopItem {\n  id: string;\n  name: string;\n  type: 'weapon' | 'hat' | 'upgrade' | 'consumable' | 'building';\n  cost: Resources;\n  unlockLevel: number;\n  priority: number;\n  description: string;\n  effects: Record<string, number>;\n}\n\nexport interface PurchaseHistory {\n  itemId: string;\n  itemName: string;\n  cost: Resources;\n  timestamp: number;\n  success: boolean;\n}\n\nexport class AutoBuyModule extends EventEmitter {\n  private gameApi: SploopGameAPI;\n  private config: ModConfig;\n  private enabled = false;\n  private updateInterval: number | null = null;\n  private lastBuyCheck = 0;\n  private purchaseHistory: PurchaseHistory[] = [];\n  private buyQueue: string[] = [];\n  \n  // Shop items database\n  private readonly SHOP_ITEMS: Record<string, ShopItem> = {\n    // Weapons\n    'weapon_wood_sword': {\n      id: 'weapon_wood_sword',\n      name: 'Wood Sword',\n      type: 'weapon',\n      cost: { wood: 0, stone: 0, food: 0, gold: 100, points: 0 },\n      unlockLevel: 1,\n      priority: 5,\n      description: 'Basic wooden sword',\n      effects: { damage: 25 }\n    },\n    'weapon_stone_sword': {\n      id: 'weapon_stone_sword',\n      name: 'Stone Sword',\n      type: 'weapon',\n      cost: { wood: 0, stone: 0, food: 0, gold: 400, points: 0 },\n      unlockLevel: 2,\n      priority: 6,\n      description: 'Stronger stone sword',\n      effects: { damage: 35 }\n    },\n    'weapon_gold_sword': {\n      id: 'weapon_gold_sword',\n      name: 'Gold Sword',\n      type: 'weapon',\n      cost: { wood: 0, stone: 0, food: 0, gold: 3000, points: 0 },\n      unlockLevel: 5,\n      priority: 7,\n      description: 'Powerful gold sword',\n      effects: { damage: 50 }\n    },\n    'weapon_diamond_sword': {\n      id: 'weapon_diamond_sword',\n      name: 'Diamond Sword',\n      type: 'weapon',\n      cost: { wood: 0, stone: 0, food: 0, gold: 15000, points: 0 },\n      unlockLevel: 10,\n      priority: 8,\n      description: 'Elite diamond sword',\n      effects: { damage: 65 }\n    },\n    \n    // Upgrades\n    'upgrade_damage_1': {\n      id: 'upgrade_damage_1',\n      name: 'Damage Upgrade I',\n      type: 'upgrade',\n      cost: { wood: 0, stone: 0, food: 0, gold: 1000, points: 0 },\n      unlockLevel: 2,\n      priority: 9,\n      description: 'Increases weapon damage',\n      effects: { damageMultiplier: 1.1 }\n    },\n    'upgrade_damage_2': {\n      id: 'upgrade_damage_2',\n      name: 'Damage Upgrade II',\n      type: 'upgrade',\n      cost: { wood: 0, stone: 0, food: 0, gold: 3000, points: 0 },\n      unlockLevel: 5,\n      priority: 9,\n      description: 'Further increases weapon damage',\n      effects: { damageMultiplier: 1.2 }\n    },\n    'upgrade_speed_1': {\n      id: 'upgrade_speed_1',\n      name: 'Speed Upgrade I',\n      type: 'upgrade',\n      cost: { wood: 0, stone: 0, food: 0, gold: 1500, points: 0 },\n      unlockLevel: 3,\n      priority: 8,\n      description: 'Increases movement speed',\n      effects: { speedMultiplier: 1.15 }\n    },\n    'upgrade_health_1': {\n      id: 'upgrade_health_1',\n      name: 'Health Upgrade I',\n      type: 'upgrade',\n      cost: { wood: 0, stone: 0, food: 0, gold: 2000, points: 0 },\n      unlockLevel: 4,\n      priority: 8,\n      description: 'Increases maximum health',\n      effects: { healthBonus: 20 }\n    },\n    \n    // Consumables\n    'consumable_health_potion': {\n      id: 'consumable_health_potion',\n      name: 'Health Potion',\n      type: 'consumable',\n      cost: { wood: 0, stone: 0, food: 0, gold: 200, points: 0 },\n      unlockLevel: 1,\n      priority: 6,\n      description: 'Restores health instantly',\n      effects: { healthRestore: 50 }\n    },\n    'consumable_speed_boost': {\n      id: 'consumable_speed_boost',\n      name: 'Speed Boost',\n      type: 'consumable',\n      cost: { wood: 0, stone: 0, food: 0, gold: 300, points: 0 },\n      unlockLevel: 2,\n      priority: 5,\n      description: 'Temporary speed increase',\n      effects: { speedBoost: 30, duration: 10000 }\n    },\n    \n    // Buildings\n    'building_mill': {\n      id: 'building_mill',\n      name: 'Windmill',\n      type: 'building',\n      cost: { wood: 5, stone: 5, food: 0, gold: 0, points: 0 },\n      unlockLevel: 1,\n      priority: 10,\n      description: 'Generates resources automatically',\n      effects: { resourceGeneration: 1 }\n    },\n    'building_spike': {\n      id: 'building_spike',\n      name: 'Spike',\n      type: 'building',\n      cost: { wood: 10, stone: 0, food: 0, gold: 0, points: 0 },\n      unlockLevel: 1,\n      priority: 7,\n      description: 'Defensive structure that damages enemies',\n      effects: { damage: 25 }\n    }\n  };\n  \n  constructor(gameApi: SploopGameAPI, config: ModConfig) {\n    super();\n    this.gameApi = gameApi;\n    this.config = config;\n    \n    this.gameApi.on('stateUpdate', this.onGameStateUpdate.bind(this));\n  }\n  \n  public start(): void {\n    if (this.enabled) return;\n    \n    this.enabled = true;\n    this.updateInterval = window.setInterval(() => {\n      this.update();\n    }, this.config.updateInterval * 2); // Run less frequently than other modules\n    \n    this.emit('started');\n    console.log('[AutoBuy] Module started');\n  }\n  \n  public stop(): void {\n    if (!this.enabled) return;\n    \n    this.enabled = false;\n    if (this.updateInterval) {\n      clearInterval(this.updateInterval);\n      this.updateInterval = null;\n    }\n    \n    this.emit('stopped');\n    console.log('[AutoBuy] Module stopped');\n  }\n  \n  public updateConfig(config: ModConfig): void {\n    this.config = config;\n  }\n  \n  private onGameStateUpdate(): void {\n    if (!this.enabled || !this.config.autoBuy.enabled) return;\n    \n    // Process any queued purchases\n    this.processBuyQueue();\n  }\n  \n  private update(): void {\n    if (!this.enabled || !this.config.autoBuy.enabled || !this.gameApi.isInGame()) return;\n    \n    const now = Date.now();\n    if (now - this.lastBuyCheck < 5000) return; // Check every 5 seconds\n    \n    this.lastBuyCheck = now;\n    \n    try {\n      this.processAutoBuy();\n    } catch (error) {\n      console.error('[AutoBuy] Error in update:', error);\n    }\n  }\n  \n  private processAutoBuy(): void {\n    const player = this.gameApi.getPlayer();\n    if (!player) return;\n    \n    // Get available items to buy\n    const availableItems = this.getAvailableItems(player);\n    \n    // Sort by priority (higher priority first)\n    availableItems.sort((a, b) => b.priority - a.priority);\n    \n    // Try to buy the highest priority item we can afford\n    for (const item of availableItems) {\n      if (this.canAffordItem(player, item) && this.shouldBuyItem(player, item)) {\n        this.queuePurchase(item.id);\n        break; // Only buy one item per cycle\n      }\n    }\n  }\n  \n  private getAvailableItems(player: Player): ShopItem[] {\n    return Object.values(this.SHOP_ITEMS).filter(item => {\n      // Check if player has unlocked this item\n      if (player.level < item.unlockLevel) return false;\n      \n      // Check if item type is enabled in config\n      switch (item.type) {\n        case 'hat':\n          return this.config.autoBuy.buyHats;\n        case 'weapon':\n          return this.config.autoBuy.buyWeapons;\n        case 'upgrade':\n          return this.config.autoBuy.buyUpgrades;\n        default:\n          return true;\n      }\n    });\n  }\n  \n  private canAffordItem(player: Player, item: ShopItem): boolean {\n    const reserves = this.config.autoBuy.reserveResources;\n    \n    return (\n      player.resources.wood >= item.cost.wood + reserves.wood &&\n      player.resources.stone >= item.cost.stone + reserves.stone &&\n      player.resources.food >= item.cost.food + reserves.food &&\n      player.resources.gold >= item.cost.gold + reserves.gold\n    );\n  }\n  \n  private shouldBuyItem(player: Player, item: ShopItem): boolean {\n    // Check if item is in priority list\n    if (this.config.autoBuy.priorityItems.length > 0) {\n      if (!this.config.autoBuy.priorityItems.includes(item.id)) {\n        return false;\n      }\n    }\n    \n    // Check spending limits\n    const totalResources = Object.values(player.resources).reduce((sum, val) => sum + val, 0);\n    const itemCost = Object.values(item.cost).reduce((sum, val) => sum + val, 0);\n    const spendPercentage = (itemCost / totalResources) * 100;\n    \n    if (spendPercentage > this.config.autoBuy.maxSpendPercentage) {\n      return false;\n    }\n    \n    // Check if we already own this item (for non-consumables)\n    if (item.type !== 'consumable') {\n      const alreadyOwned = player.inventory.some(invItem => invItem.id === item.id);\n      if (alreadyOwned) return false;\n    }\n    \n    // Additional logic for specific item types\n    switch (item.type) {\n      case 'consumable':\n        return this.shouldBuyConsumable(player, item);\n      case 'weapon':\n        return this.shouldBuyWeapon(player, item);\n      case 'upgrade':\n        return this.shouldBuyUpgrade(player, item);\n      default:\n        return true;\n    }\n  }\n  \n  private shouldBuyConsumable(player: Player, item: ShopItem): boolean {\n    // Buy health potions if health is low\n    if (item.id === 'consumable_health_potion') {\n      return player.health < player.maxHealth * 0.7;\n    }\n    \n    // Buy speed boosts occasionally\n    if (item.id === 'consumable_speed_boost') {\n      return Math.random() < 0.1; // 10% chance\n    }\n    \n    return true;\n  }\n  \n  private shouldBuyWeapon(player: Player, item: ShopItem): boolean {\n    // Only buy if it's better than current weapon\n    if (!player.weapon) return true;\n    \n    const currentDamage = player.weapon.damage;\n    const newDamage = item.effects.damage || 0;\n    \n    return newDamage > currentDamage;\n  }\n  \n  private shouldBuyUpgrade(player: Player, item: ShopItem): boolean {\n    // Always buy upgrades if we can afford them\n    return true;\n  }\n  \n  private queuePurchase(itemId: string): void {\n    if (!this.buyQueue.includes(itemId)) {\n      this.buyQueue.push(itemId);\n    }\n  }\n  \n  private processBuyQueue(): void {\n    if (this.buyQueue.length === 0) return;\n    \n    const itemId = this.buyQueue.shift();\n    if (!itemId) return;\n    \n    const item = this.SHOP_ITEMS[itemId];\n    if (!item) return;\n    \n    const player = this.gameApi.getPlayer();\n    if (!player || !this.canAffordItem(player, item)) {\n      return; // Skip this purchase\n    }\n    \n    // Execute the purchase\n    this.gameApi.sendAction({\n      type: ActionType.BUY,\n      data: { itemId: item.id },\n      timestamp: Date.now()\n    });\n    \n    // Record the purchase\n    const purchase: PurchaseHistory = {\n      itemId: item.id,\n      itemName: item.name,\n      cost: { ...item.cost },\n      timestamp: Date.now(),\n      success: true // We'll assume success for now\n    };\n    \n    this.purchaseHistory.push(purchase);\n    \n    // Keep only last 50 purchases\n    if (this.purchaseHistory.length > 50) {\n      this.purchaseHistory = this.purchaseHistory.slice(-50);\n    }\n    \n    this.emit('itemPurchased', purchase);\n    console.log(`[AutoBuy] Purchased ${item.name} for`, item.cost);\n  }\n  \n  public getShopItems(): ShopItem[] {\n    return Object.values(this.SHOP_ITEMS).map(item => ({ ...item }));\n  }\n  \n  public getPurchaseHistory(): PurchaseHistory[] {\n    return [...this.purchaseHistory];\n  }\n  \n  public clearPurchaseHistory(): void {\n    this.purchaseHistory = [];\n  }\n  \n  public addCustomItem(item: ShopItem): void {\n    this.SHOP_ITEMS[item.id] = { ...item };\n  }\n  \n  public getStats(): any {\n    const totalPurchases = this.purchaseHistory.length;\n    const totalSpent = this.purchaseHistory.reduce((sum, purchase) => {\n      return sum + Object.values(purchase.cost).reduce((costSum, val) => costSum + val, 0);\n    }, 0);\n    \n    const recentPurchases = this.purchaseHistory.filter(p => \n      Date.now() - p.timestamp < 300000 // Last 5 minutes\n    ).length;\n    \n    return {\n      enabled: this.enabled,\n      totalPurchases,\n      totalSpent,\n      recentPurchases,\n      queueLength: this.buyQueue.length,\n      availableItems: Object.keys(this.SHOP_ITEMS).length\n    };\n  }\n}\n", "import { SploopGameAPI } from '@/utils/gameApi';\nimport { ModConfig } from '@/config';\nimport { Player, Building, Mill, ActionType, Vector2, ResourceType, BuildingType } from '@/types/game';\nimport { EventEmitter } from 'eventemitter3';\n\nexport interface MillPlacement {\n  position: Vector2;\n  resourceType: ResourceType;\n  priority: number;\n  safety: number;\n  efficiency: number;\n}\n\nexport interface MillStats {\n  id: string;\n  position: Vector2;\n  resourceType: ResourceType;\n  level: number;\n  production: number;\n  storage: number;\n  maxStorage: number;\n  lastUpgrade: number;\n  isProtected: boolean;\n}\n\nexport class AutoMillsModule extends EventEmitter {\n  private gameApi: SploopGameAPI;\n  private config: ModConfig;\n  private enabled = false;\n  private updateInterval: number | null = null;\n  private lastMillCheck = 0;\n  private lastPlacementCheck = 0;\n  private ownedMills: Map<string, MillStats> = new Map();\n  private resourceNodes: Map<string, Vector2> = new Map();\n  private dangerZones: Vector2[] = [];\n  \n  // Mill costs and stats\n  private readonly MILL_COSTS = {\n    [ResourceType.WOOD]: { wood: 5, stone: 5, food: 0, gold: 0 },\n    [ResourceType.STONE]: { wood: 5, stone: 5, food: 0, gold: 0 },\n    [ResourceType.FOOD]: { wood: 10, stone: 0, food: 0, gold: 0 },\n    [ResourceType.GOLD]: { wood: 20, stone: 10, food: 0, gold: 0 }\n  };\n  \n  private readonly UPGRADE_COSTS: Record<number, { wood: number; stone: number; food: number; gold: number }> = {\n    1: { wood: 15, stone: 15, food: 0, gold: 0 },\n    2: { wood: 25, stone: 25, food: 0, gold: 0 },\n    3: { wood: 35, stone: 35, food: 0, gold: 0 }\n  };\n  \n  constructor(gameApi: SploopGameAPI, config: ModConfig) {\n    super();\n    this.gameApi = gameApi;\n    this.config = config;\n    \n    this.gameApi.on('stateUpdate', this.onGameStateUpdate.bind(this));\n  }\n  \n  public start(): void {\n    if (this.enabled) return;\n    \n    this.enabled = true;\n    this.updateInterval = window.setInterval(() => {\n      this.update();\n    }, this.config.updateInterval);\n    \n    this.emit('started');\n    console.log('[AutoMills] Module started');\n  }\n  \n  public stop(): void {\n    if (!this.enabled) return;\n    \n    this.enabled = false;\n    if (this.updateInterval) {\n      clearInterval(this.updateInterval);\n      this.updateInterval = null;\n    }\n    \n    this.emit('stopped');\n    console.log('[AutoMills] Module stopped');\n  }\n  \n  public updateConfig(config: ModConfig): void {\n    this.config = config;\n  }\n  \n  private onGameStateUpdate(): void {\n    if (!this.enabled || !this.config.autoMills.enabled) return;\n    \n    this.updateMillTracking();\n    this.updateResourceNodes();\n    this.updateDangerZones();\n  }\n  \n  private update(): void {\n    if (!this.enabled || !this.config.autoMills.enabled || !this.gameApi.isInGame()) return;\n    \n    const now = Date.now();\n    \n    // Check for mill upgrades every 3 seconds\n    if (now - this.lastMillCheck > 3000) {\n      this.lastMillCheck = now;\n      this.processMillUpgrades();\n    }\n    \n    // Check for new mill placements every 10 seconds\n    if (now - this.lastPlacementCheck > 10000) {\n      this.lastPlacementCheck = now;\n      this.processMillPlacement();\n    }\n    \n    // Protect mills if enabled\n    if (this.config.autoMills.protectMills) {\n      this.protectMills();\n    }\n  }\n  \n  private updateMillTracking(): void {\n    const buildings = this.gameApi.getBuildings();\n    const player = this.gameApi.getPlayer();\n    \n    if (!player) return;\n    \n    // Update owned mills\n    this.ownedMills.clear();\n    \n    buildings.forEach((building, id) => {\n      if (building.type === BuildingType.MILL && building.owner === player.id) {\n        const mill = building as Mill;\n        const millStats: MillStats = {\n          id: mill.id,\n          position: mill.position,\n          resourceType: mill.resourceType,\n          level: mill.level,\n          production: mill.productionRate,\n          storage: mill.storage,\n          maxStorage: mill.maxStorage,\n          lastUpgrade: 0, // We'll track this separately\n          isProtected: this.isMillProtected(mill.position)\n        };\n        \n        this.ownedMills.set(id, millStats);\n      }\n    });\n  }\n  \n  private updateResourceNodes(): void {\n    // In a real implementation, this would scan the map for resource nodes\n    // For now, we'll use some example positions\n    this.resourceNodes.clear();\n    \n    // These would be detected from the game map\n    const exampleNodes = [\n      { type: ResourceType.WOOD, pos: { x: 100, y: 100 } },\n      { type: ResourceType.STONE, pos: { x: 200, y: 150 } },\n      { type: ResourceType.FOOD, pos: { x: 150, y: 200 } },\n      { type: ResourceType.GOLD, pos: { x: 300, y: 250 } }\n    ];\n    \n    exampleNodes.forEach((node, index) => {\n      this.resourceNodes.set(`${node.type}_${index}`, node.pos);\n    });\n  }\n  \n  private updateDangerZones(): void {\n    const players = this.gameApi.getPlayers();\n    const player = this.gameApi.getPlayer();\n    \n    if (!player) return;\n    \n    this.dangerZones = [];\n    \n    // Mark areas near enemy players as dangerous\n    players.forEach(otherPlayer => {\n      if (otherPlayer.id !== player.id) {\n        this.dangerZones.push(otherPlayer.position);\n      }\n    });\n  }\n  \n  private processMillPlacement(): void {\n    const player = this.gameApi.getPlayer();\n    if (!player) return;\n    \n    const currentMillCount = this.ownedMills.size;\n    \n    if (currentMillCount >= this.config.autoMills.maxMills) {\n      return; // Already at max mills\n    }\n    \n    // Find optimal placement for next mill\n    const optimalPlacement = this.findOptimalMillPlacement();\n    \n    if (optimalPlacement && this.canAffordMill(player, optimalPlacement.resourceType)) {\n      this.placeMill(optimalPlacement);\n    }\n  }\n  \n  private findOptimalMillPlacement(): MillPlacement | null {\n    const player = this.gameApi.getPlayer();\n    if (!player) return null;\n    \n    const placements: MillPlacement[] = [];\n    \n    // Evaluate potential positions around resource nodes\n    this.resourceNodes.forEach((nodePos, nodeId) => {\n      const resourceType = this.getResourceTypeFromNodeId(nodeId);\n      \n      // Generate positions around the resource node\n      for (let angle = 0; angle < 360; angle += 45) {\n        const distance = this.config.autoMills.millSpacing;\n        const x = nodePos.x + Math.cos(angle * Math.PI / 180) * distance;\n        const y = nodePos.y + Math.sin(angle * Math.PI / 180) * distance;\n        const position = { x, y };\n        \n        if (this.isValidMillPosition(position)) {\n          const placement: MillPlacement = {\n            position,\n            resourceType,\n            priority: this.calculatePlacementPriority(resourceType),\n            safety: this.calculateSafety(position),\n            efficiency: this.calculateEfficiency(position, resourceType)\n          };\n          \n          placements.push(placement);\n        }\n      }\n    });\n    \n    // Sort by overall score (priority + safety + efficiency)\n    placements.sort((a, b) => {\n      const scoreA = a.priority + a.safety + a.efficiency;\n      const scoreB = b.priority + b.safety + b.efficiency;\n      return scoreB - scoreA;\n    });\n    \n    return placements[0] || null;\n  }\n  \n  private getResourceTypeFromNodeId(nodeId: string): ResourceType {\n    if (nodeId.includes('wood')) return ResourceType.WOOD;\n    if (nodeId.includes('stone')) return ResourceType.STONE;\n    if (nodeId.includes('food')) return ResourceType.FOOD;\n    if (nodeId.includes('gold')) return ResourceType.GOLD;\n    return ResourceType.WOOD; // Default\n  }\n  \n  private isValidMillPosition(position: Vector2): boolean {\n    // Check if position is too close to existing mills\n    for (const mill of this.ownedMills.values()) {\n      const distance = this.gameApi.calculateDistance(position, mill.position);\n      if (distance < this.config.autoMills.millSpacing) {\n        return false;\n      }\n    }\n    \n    // Check if position is in a danger zone\n    for (const dangerPos of this.dangerZones) {\n      const distance = this.gameApi.calculateDistance(position, dangerPos);\n      if (distance < 150) { // Too close to enemies\n        return false;\n      }\n    }\n    \n    return true;\n  }\n  \n  private calculatePlacementPriority(resourceType: ResourceType): number {\n    const priorityIndex = this.config.autoMills.resourcePriority.indexOf(resourceType);\n    return priorityIndex >= 0 ? (10 - priorityIndex) : 1;\n  }\n  \n  private calculateSafety(position: Vector2): number {\n    let safety = 10;\n    \n    // Reduce safety based on proximity to danger zones\n    for (const dangerPos of this.dangerZones) {\n      const distance = this.gameApi.calculateDistance(position, dangerPos);\n      if (distance < 300) {\n        safety -= (300 - distance) / 30;\n      }\n    }\n    \n    return Math.max(0, safety);\n  }\n  \n  private calculateEfficiency(position: Vector2, resourceType: ResourceType): number {\n    let efficiency = 5;\n    \n    // Increase efficiency based on proximity to resource nodes\n    this.resourceNodes.forEach((nodePos, nodeId) => {\n      if (this.getResourceTypeFromNodeId(nodeId) === resourceType) {\n        const distance = this.gameApi.calculateDistance(position, nodePos);\n        if (distance < 200) {\n          efficiency += (200 - distance) / 20;\n        }\n      }\n    });\n    \n    return efficiency;\n  }\n  \n  private canAffordMill(player: Player, resourceType: ResourceType): boolean {\n    const cost = this.MILL_COSTS[resourceType];\n    \n    return (\n      player.resources.wood >= cost.wood &&\n      player.resources.stone >= cost.stone &&\n      player.resources.food >= cost.food &&\n      player.resources.gold >= cost.gold\n    );\n  }\n  \n  private placeMill(placement: MillPlacement): void {\n    this.gameApi.sendAction({\n      type: ActionType.BUILD,\n      data: {\n        type: 'mill',\n        x: placement.position.x,\n        y: placement.position.y,\n        resourceType: placement.resourceType\n      },\n      timestamp: Date.now()\n    });\n    \n    this.emit('millPlaced', placement);\n    console.log(`[AutoMills] Placed ${placement.resourceType} mill at (${placement.position.x}, ${placement.position.y})`);\n  }\n  \n  private processMillUpgrades(): void {\n    if (!this.config.autoMills.autoUpgrade) return;\n    \n    const player = this.gameApi.getPlayer();\n    if (!player) return;\n    \n    // Find mills that can be upgraded\n    for (const mill of this.ownedMills.values()) {\n      if (mill.level < 4 && this.canAffordUpgrade(player, mill.level)) {\n        this.upgradeMill(mill);\n        break; // Only upgrade one mill per cycle\n      }\n    }\n  }\n  \n  private canAffordUpgrade(player: Player, currentLevel: number): boolean {\n    const cost = this.UPGRADE_COSTS[currentLevel];\n    if (!cost) return false;\n    \n    return (\n      player.resources.wood >= cost.wood &&\n      player.resources.stone >= cost.stone &&\n      player.resources.food >= cost.food &&\n      player.resources.gold >= cost.gold\n    );\n  }\n  \n  private upgradeMill(mill: MillStats): void {\n    this.gameApi.sendAction({\n      type: ActionType.UPGRADE,\n      data: { buildingId: mill.id },\n      timestamp: Date.now()\n    });\n    \n    this.emit('millUpgraded', mill);\n    console.log(`[AutoMills] Upgraded mill ${mill.id} to level ${mill.level + 1}`);\n  }\n  \n  private isMillProtected(position: Vector2): boolean {\n    // Check if there are defensive structures nearby\n    const buildings = this.gameApi.getBuildings();\n    \n    for (const building of buildings.values()) {\n      if (building.type === BuildingType.SPIKE || building.type === BuildingType.WALL) {\n        const distance = this.gameApi.calculateDistance(position, building.position);\n        if (distance < 100) {\n          return true;\n        }\n      }\n    }\n    \n    return false;\n  }\n  \n  private protectMills(): void {\n    const player = this.gameApi.getPlayer();\n    if (!player) return;\n    \n    // Find unprotected mills\n    for (const mill of this.ownedMills.values()) {\n      if (!mill.isProtected && this.canAffordSpike(player)) {\n        this.placeProtection(mill);\n        break; // Only protect one mill per cycle\n      }\n    }\n  }\n  \n  private canAffordSpike(player: Player): boolean {\n    return player.resources.wood >= 10;\n  }\n  \n  private placeProtection(mill: MillStats): void {\n    // Place spikes around the mill\n    const angles = [0, 90, 180, 270];\n    const distance = 80;\n    \n    for (const angle of angles) {\n      const x = mill.position.x + Math.cos(angle * Math.PI / 180) * distance;\n      const y = mill.position.y + Math.sin(angle * Math.PI / 180) * distance;\n      \n      this.gameApi.sendAction({\n        type: ActionType.BUILD,\n        data: {\n          type: 'spike',\n          x,\n          y\n        },\n        timestamp: Date.now()\n      });\n    }\n    \n    this.emit('millProtected', mill);\n    console.log(`[AutoMills] Protected mill ${mill.id} with spikes`);\n  }\n  \n  public getOwnedMills(): MillStats[] {\n    return Array.from(this.ownedMills.values());\n  }\n  \n  public getStats(): any {\n    const totalMills = this.ownedMills.size;\n    const protectedMills = Array.from(this.ownedMills.values()).filter(m => m.isProtected).length;\n    const totalProduction = Array.from(this.ownedMills.values()).reduce((sum, mill) => sum + mill.production, 0);\n    \n    return {\n      enabled: this.enabled,\n      totalMills,\n      maxMills: this.config.autoMills.maxMills,\n      protectedMills,\n      totalProduction,\n      resourceNodes: this.resourceNodes.size,\n      dangerZones: this.dangerZones.length\n    };\n  }\n}\n", "import { SploopGameAPI } from '@/utils/gameApi';\nimport { ModConfig } from '@/config';\nimport { Player, Vector2, ActionType, ResourceType } from '@/types/game';\nimport { EventEmitter } from 'eventemitter3';\n\nexport interface BotInstance {\n  id: string;\n  name: string;\n  position: Vector2;\n  target: Vector2 | null;\n  behavior: BotBehavior;\n  state: BotState;\n  lastAction: number;\n  resources: { wood: number; stone: number; food: number; gold: number };\n  health: number;\n  level: number;\n  task: BotTask | null;\n}\n\nexport interface BotTask {\n  type: 'farm' | 'build' | 'explore' | 'defend' | 'collect';\n  target: Vector2;\n  priority: number;\n  startTime: number;\n  estimatedDuration: number;\n}\n\nexport enum BotBehavior {\n  FARMING = 'farming',\n  COMBAT = 'combat',\n  BUILDING = 'building',\n  EXPLORATION = 'exploration',\n  PROTECTION = 'protection',\n  IDLE = 'idle'\n}\n\nexport enum BotState {\n  SPAWNING = 'spawning',\n  MOVING = 'moving',\n  WORKING = 'working',\n  FIGHTING = 'fighting',\n  FLEEING = 'fleeing',\n  DEAD = 'dead',\n  IDLE = 'idle'\n}\n\nexport class BotSystemModule extends EventEmitter {\n  private gameApi: SploopGameAPI;\n  private config: ModConfig;\n  private enabled = false;\n  private updateInterval: number | null = null;\n  private bots: Map<string, BotInstance> = new Map();\n  private taskQueue: BotTask[] = [];\n  private lastBotSpawn = 0;\n  private coordinationData = {\n    sharedResources: { wood: 0, stone: 0, food: 0, gold: 0 },\n    groupTarget: null as Vector2 | null,\n    threatLevel: 0\n  };\n  \n  constructor(gameApi: SploopGameAPI, config: ModConfig) {\n    super();\n    this.gameApi = gameApi;\n    this.config = config;\n    \n    this.gameApi.on('stateUpdate', this.onGameStateUpdate.bind(this));\n  }\n  \n  public start(): void {\n    if (this.enabled) return;\n    \n    this.enabled = true;\n    this.updateInterval = window.setInterval(() => {\n      this.update();\n    }, this.config.updateInterval);\n    \n    this.emit('started');\n    console.log('[BotSystem] Module started');\n  }\n  \n  public stop(): void {\n    if (!this.enabled) return;\n    \n    this.enabled = false;\n    if (this.updateInterval) {\n      clearInterval(this.updateInterval);\n      this.updateInterval = null;\n    }\n    \n    // Despawn all bots\n    this.bots.clear();\n    \n    this.emit('stopped');\n    console.log('[BotSystem] Module stopped');\n  }\n  \n  public updateConfig(config: ModConfig): void {\n    this.config = config;\n  }\n  \n  private onGameStateUpdate(): void {\n    if (!this.enabled || !this.config.botSystem.enabled) return;\n    \n    this.updateBotStates();\n    this.updateCoordination();\n  }\n  \n  private update(): void {\n    if (!this.enabled || !this.config.botSystem.enabled || !this.gameApi.isInGame()) return;\n    \n    try {\n      this.manageBotSpawning();\n      this.processBotActions();\n      this.assignTasks();\n      this.updateBotBehaviors();\n    } catch (error) {\n      console.error('[BotSystem] Error in update:', error);\n    }\n  }\n  \n  private manageBotSpawning(): void {\n    const now = Date.now();\n    const currentBotCount = this.bots.size;\n    \n    // Spawn new bots if needed\n    if (currentBotCount < this.config.botSystem.maxBots && \n        now - this.lastBotSpawn > 10000) { // 10 second cooldown\n      \n      this.spawnBot();\n      this.lastBotSpawn = now;\n    }\n    \n    // Remove dead bots\n    for (const [botId, bot] of this.bots.entries()) {\n      if (bot.state === BotState.DEAD) {\n        this.bots.delete(botId);\n        this.emit('botDespawned', { botId, reason: 'death' });\n      }\n    }\n  }\n  \n  private spawnBot(): void {\n    const player = this.gameApi.getPlayer();\n    if (!player) return;\n    \n    const botId = `bot_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;\n    const spawnPosition = this.findSafeSpawnPosition(player.position);\n    \n    const bot: BotInstance = {\n      id: botId,\n      name: `Bot_${this.bots.size + 1}`,\n      position: spawnPosition,\n      target: null,\n      behavior: this.selectBotBehavior(),\n      state: BotState.SPAWNING,\n      lastAction: Date.now(),\n      resources: { wood: 0, stone: 0, food: 0, gold: 0 },\n      health: 100,\n      level: 1,\n      task: null\n    };\n    \n    this.bots.set(botId, bot);\n    this.emit('botSpawned', bot);\n    console.log(`[BotSystem] Spawned bot ${bot.name} with behavior ${bot.behavior}`);\n  }\n  \n  private findSafeSpawnPosition(playerPos: Vector2): Vector2 {\n    // Spawn bots near the player but not too close\n    const angle = Math.random() * 2 * Math.PI;\n    const distance = 100 + Math.random() * 100; // 100-200 units away\n    \n    return {\n      x: playerPos.x + Math.cos(angle) * distance,\n      y: playerPos.y + Math.sin(angle) * distance\n    };\n  }\n  \n  private selectBotBehavior(): BotBehavior {\n    const behaviors = this.config.botSystem.botBehaviors;\n    const availableBehaviors: BotBehavior[] = [];\n    \n    if (behaviors.farming) availableBehaviors.push(BotBehavior.FARMING);\n    if (behaviors.combat) availableBehaviors.push(BotBehavior.COMBAT);\n    if (behaviors.building) availableBehaviors.push(BotBehavior.BUILDING);\n    if (behaviors.exploration) availableBehaviors.push(BotBehavior.EXPLORATION);\n    if (behaviors.protection) availableBehaviors.push(BotBehavior.PROTECTION);\n    \n    if (availableBehaviors.length === 0) {\n      return BotBehavior.IDLE;\n    }\n    \n    return availableBehaviors[Math.floor(Math.random() * availableBehaviors.length)] || BotBehavior.IDLE;\n  }\n  \n  private updateBotStates(): void {\n    const players = this.gameApi.getPlayers();\n    \n    for (const bot of this.bots.values()) {\n      // Check for nearby threats\n      const nearbyEnemies = Array.from(players.values()).filter(p => \n        !p.isBot && \n        this.gameApi.calculateDistance(bot.position, p.position) < 200\n      );\n      \n      // Update bot state based on situation\n      if (nearbyEnemies.length > 0 && this.config.botSystem.intelligence.avoidPlayers) {\n        if (bot.behavior !== BotBehavior.COMBAT) {\n          bot.state = BotState.FLEEING;\n        } else {\n          bot.state = BotState.FIGHTING;\n        }\n      } else if (bot.task) {\n        bot.state = BotState.WORKING;\n      } else {\n        bot.state = BotState.IDLE;\n      }\n    }\n  }\n  \n  private updateCoordination(): void {\n    if (!this.config.botSystem.coordination.shareResources) return;\n    \n    // Calculate shared resources\n    this.coordinationData.sharedResources = { wood: 0, stone: 0, food: 0, gold: 0 };\n    \n    for (const bot of this.bots.values()) {\n      this.coordinationData.sharedResources.wood += bot.resources.wood;\n      this.coordinationData.sharedResources.stone += bot.resources.stone;\n      this.coordinationData.sharedResources.food += bot.resources.food;\n      this.coordinationData.sharedResources.gold += bot.resources.gold;\n    }\n    \n    // Update threat level\n    const players = this.gameApi.getPlayers();\n    const nearbyEnemies = Array.from(players.values()).filter(p => {\n      if (p.isBot) return false;\n      \n      for (const bot of this.bots.values()) {\n        if (this.gameApi.calculateDistance(bot.position, p.position) < 300) {\n          return true;\n        }\n      }\n      return false;\n    });\n    \n    this.coordinationData.threatLevel = nearbyEnemies.length;\n  }\n  \n  private assignTasks(): void {\n    // Generate new tasks based on bot behaviors\n    for (const bot of this.bots.values()) {\n      if (bot.task && Date.now() - bot.task.startTime < bot.task.estimatedDuration) {\n        continue; // Bot is still working on current task\n      }\n      \n      bot.task = this.generateTaskForBot(bot);\n    }\n  }\n  \n  private generateTaskForBot(bot: BotInstance): BotTask | null {\n    switch (bot.behavior) {\n      case BotBehavior.FARMING:\n        return this.generateFarmingTask(bot);\n      case BotBehavior.BUILDING:\n        return this.generateBuildingTask(bot);\n      case BotBehavior.EXPLORATION:\n        return this.generateExplorationTask(bot);\n      case BotBehavior.PROTECTION:\n        return this.generateProtectionTask(bot);\n      default:\n        return null;\n    }\n  }\n  \n  private generateFarmingTask(bot: BotInstance): BotTask {\n    // Find nearest resource node\n    const resourceTypes = [ResourceType.WOOD, ResourceType.STONE, ResourceType.FOOD];\n    const targetResource = resourceTypes[Math.floor(Math.random() * resourceTypes.length)];\n    \n    // Generate a position near a resource (simplified)\n    const angle = Math.random() * 2 * Math.PI;\n    const distance = 50 + Math.random() * 100;\n    const target = {\n      x: bot.position.x + Math.cos(angle) * distance,\n      y: bot.position.y + Math.sin(angle) * distance\n    };\n    \n    return {\n      type: 'farm',\n      target,\n      priority: 5,\n      startTime: Date.now(),\n      estimatedDuration: 15000 // 15 seconds\n    };\n  }\n  \n  private generateBuildingTask(bot: BotInstance): BotTask {\n    // Find a good position to build\n    const angle = Math.random() * 2 * Math.PI;\n    const distance = 100 + Math.random() * 150;\n    const target = {\n      x: bot.position.x + Math.cos(angle) * distance,\n      y: bot.position.y + Math.sin(angle) * distance\n    };\n    \n    return {\n      type: 'build',\n      target,\n      priority: 6,\n      startTime: Date.now(),\n      estimatedDuration: 20000 // 20 seconds\n    };\n  }\n  \n  private generateExplorationTask(bot: BotInstance): BotTask {\n    // Generate random exploration target\n    const angle = Math.random() * 2 * Math.PI;\n    const distance = 200 + Math.random() * 300;\n    const target = {\n      x: bot.position.x + Math.cos(angle) * distance,\n      y: bot.position.y + Math.sin(angle) * distance\n    };\n    \n    return {\n      type: 'explore',\n      target,\n      priority: 3,\n      startTime: Date.now(),\n      estimatedDuration: 25000 // 25 seconds\n    };\n  }\n  \n  private generateProtectionTask(bot: BotInstance): BotTask {\n    const player = this.gameApi.getPlayer();\n    if (!player) return this.generateExplorationTask(bot);\n    \n    // Protect the main player\n    return {\n      type: 'defend',\n      target: player.position,\n      priority: 8,\n      startTime: Date.now(),\n      estimatedDuration: 30000 // 30 seconds\n    };\n  }\n  \n  private processBotActions(): void {\n    const now = Date.now();\n    \n    for (const bot of this.bots.values()) {\n      if (now - bot.lastAction < 500) continue; // Rate limiting\n      \n      this.executeBotAction(bot);\n      bot.lastAction = now;\n    }\n  }\n  \n  private executeBotAction(bot: BotInstance): void {\n    if (!bot.task) return;\n    \n    switch (bot.state) {\n      case BotState.MOVING:\n        this.moveBotToTarget(bot);\n        break;\n      case BotState.WORKING:\n        this.executeBotTask(bot);\n        break;\n      case BotState.FLEEING:\n        this.fleeFromDanger(bot);\n        break;\n      case BotState.FIGHTING:\n        this.engageInCombat(bot);\n        break;\n    }\n  }\n  \n  private moveBotToTarget(bot: BotInstance): void {\n    if (!bot.task) return;\n    \n    const distance = this.gameApi.calculateDistance(bot.position, bot.task.target);\n    \n    if (distance < 50) {\n      bot.state = BotState.WORKING;\n      return;\n    }\n    \n    // Move towards target\n    const angle = this.gameApi.calculateAngle(bot.position, bot.task.target);\n    const speed = 2; // Bot movement speed\n    \n    bot.position.x += Math.cos(angle) * speed;\n    bot.position.y += Math.sin(angle) * speed;\n    \n    // Send movement action (this would be sent to the game)\n    this.gameApi.sendAction({\n      type: ActionType.MOVE,\n      data: { x: bot.position.x, y: bot.position.y },\n      timestamp: Date.now()\n    });\n  }\n  \n  private executeBotTask(bot: BotInstance): void {\n    if (!bot.task) return;\n    \n    switch (bot.task.type) {\n      case 'farm':\n        this.executeFarmingAction(bot);\n        break;\n      case 'build':\n        this.executeBuildingAction(bot);\n        break;\n      case 'explore':\n        this.executeExplorationAction(bot);\n        break;\n      case 'defend':\n        this.executeDefenseAction(bot);\n        break;\n    }\n  }\n  \n  private executeFarmingAction(bot: BotInstance): void {\n    // Simulate resource gathering\n    const resourceGain = Math.floor(Math.random() * 5) + 1;\n    const resourceType = ['wood', 'stone', 'food'][Math.floor(Math.random() * 3)] as keyof typeof bot.resources;\n    \n    bot.resources[resourceType] += resourceGain;\n    \n    this.emit('botAction', { \n      botId: bot.id, \n      action: 'farm', \n      result: { [resourceType]: resourceGain } \n    });\n  }\n  \n  private executeBuildingAction(bot: BotInstance): void {\n    // Simulate building placement\n    if (bot.resources.wood >= 5 && bot.resources.stone >= 5) {\n      bot.resources.wood -= 5;\n      bot.resources.stone -= 5;\n      \n      this.gameApi.sendAction({\n        type: ActionType.BUILD,\n        data: {\n          type: 'mill',\n          x: bot.task!.target.x,\n          y: bot.task!.target.y\n        },\n        timestamp: Date.now()\n      });\n      \n      this.emit('botAction', { \n        botId: bot.id, \n        action: 'build', \n        result: { building: 'mill' } \n      });\n    }\n  }\n  \n  private executeExplorationAction(bot: BotInstance): void {\n    // Exploration increases bot level/experience\n    bot.level += 0.1;\n    \n    this.emit('botAction', { \n      botId: bot.id, \n      action: 'explore', \n      result: { experience: 0.1 } \n    });\n  }\n  \n  private executeDefenseAction(bot: BotInstance): void {\n    // Stay near the target and be ready to defend\n    const player = this.gameApi.getPlayer();\n    if (player) {\n      bot.task!.target = player.position;\n    }\n  }\n  \n  private fleeFromDanger(bot: BotInstance): void {\n    const players = this.gameApi.getPlayers();\n    \n    // Find nearest enemy\n    let nearestEnemy: Player | null = null;\n    let minDistance = Infinity;\n    \n    for (const player of players.values()) {\n      if (!player.isBot) {\n        const distance = this.gameApi.calculateDistance(bot.position, player.position);\n        if (distance < minDistance) {\n          minDistance = distance;\n          nearestEnemy = player;\n        }\n      }\n    }\n    \n    if (nearestEnemy) {\n      // Move away from enemy\n      const angle = this.gameApi.calculateAngle(nearestEnemy.position, bot.position);\n      const speed = 3; // Faster when fleeing\n      \n      bot.position.x += Math.cos(angle) * speed;\n      bot.position.y += Math.sin(angle) * speed;\n    }\n  }\n  \n  private engageInCombat(bot: BotInstance): void {\n    // Simple combat AI - attack nearest enemy\n    const players = this.gameApi.getPlayers();\n    \n    for (const player of players.values()) {\n      if (!player.isBot) {\n        const distance = this.gameApi.calculateDistance(bot.position, player.position);\n        if (distance < 100) {\n          const angle = this.gameApi.calculateAngle(bot.position, player.position);\n          \n          this.gameApi.sendAction({\n            type: ActionType.ATTACK,\n            data: { angle },\n            timestamp: Date.now()\n          });\n          \n          break;\n        }\n      }\n    }\n  }\n  \n  private updateBotBehaviors(): void {\n    if (!this.config.botSystem.intelligence.adaptToSituation) return;\n    \n    // Adapt bot behaviors based on current situation\n    for (const bot of this.bots.values()) {\n      if (this.coordinationData.threatLevel > 2) {\n        // High threat - switch to defensive behaviors\n        if (bot.behavior === BotBehavior.FARMING || bot.behavior === BotBehavior.EXPLORATION) {\n          bot.behavior = BotBehavior.PROTECTION;\n        }\n      } else if (this.coordinationData.threatLevel === 0) {\n        // No threats - focus on resource gathering\n        if (bot.behavior === BotBehavior.PROTECTION) {\n          bot.behavior = BotBehavior.FARMING;\n        }\n      }\n    }\n  }\n  \n  public getBots(): BotInstance[] {\n    return Array.from(this.bots.values());\n  }\n  \n  public getBotById(botId: string): BotInstance | null {\n    return this.bots.get(botId) || null;\n  }\n  \n  public getCoordinationData(): any {\n    return { ...this.coordinationData };\n  }\n  \n  public getStats(): any {\n    const totalBots = this.bots.size;\n    const activeBots = Array.from(this.bots.values()).filter(b => b.state !== BotState.DEAD).length;\n    const behaviorCounts: Record<string, number> = {};\n\n    for (const bot of this.bots.values()) {\n      behaviorCounts[bot.behavior] = (behaviorCounts[bot.behavior] || 0) + 1;\n    }\n    \n    return {\n      enabled: this.enabled,\n      totalBots,\n      activeBots,\n      maxBots: this.config.botSystem.maxBots,\n      behaviorCounts,\n      threatLevel: this.coordinationData.threatLevel,\n      sharedResources: this.coordinationData.sharedResources\n    };\n  }\n}\n", "// ==UserScript==\n// @name         Sploop.io Advanced Mod\n// @namespace    http://tampermonkey.net/\n// @version      1.0.0\n// @description  Advanced sploop.io mod with auto hats, auto buy, auto mills, and bot system\n// <AUTHOR> Mod Developer\n// @match        *://sploop.io/*\n// @match        *://*.sploop.io/*\n// @grant        none\n// @run-at       document-start\n// ==/UserScript==\n\nimport { SploopGameAPI } from '@/utils/gameApi';\nimport { ConfigManager, ModConfig } from '@/config';\nimport { AutoHatsModule } from '@/modules/autoHats';\nimport { AutoBuyModule } from '@/modules/autoBuy';\nimport { AutoMillsModule } from '@/modules/autoMills';\nimport { BotSystemModule } from '@/modules/botSystem';\nimport { EventEmitter } from 'eventemitter3';\n\ninterface ModuleStats {\n  autoHats: any;\n  autoBuy: any;\n  autoMills: any;\n  botSystem: any;\n}\n\nclass SploopAdvancedMod extends EventEmitter {\n  private gameApi: SploopGameAPI;\n  private configManager: ConfigManager;\n  private autoHats: AutoHatsModule;\n  private autoBuy: AutoBuyModule;\n  private autoMills: AutoMillsModule;\n  private botSystem: BotSystemModule;\n  \n  private ui: HTMLElement | null = null;\n  private isInitialized = false;\n  private emergencyStopActive = false;\n  \n  constructor() {\n    super();\n    \n    console.log('[SploopAdvancedMod] Initializing...');\n    \n    // Initialize core components\n    this.configManager = new ConfigManager();\n    this.gameApi = new SploopGameAPI();\n    \n    // Initialize modules\n    const config = this.configManager.getConfig();\n    this.autoHats = new AutoHatsModule(this.gameApi, config);\n    this.autoBuy = new AutoBuyModule(this.gameApi, config);\n    this.autoMills = new AutoMillsModule(this.gameApi, config);\n    this.botSystem = new BotSystemModule(this.gameApi, config);\n    \n    this.setupEventListeners();\n    this.setupEmergencyStop();\n    this.initialize();\n  }\n  \n  private async initialize(): Promise<void> {\n    try {\n      // Wait for game to load\n      await this.waitForGameLoad();\n      \n      // Create UI\n      this.createUI();\n      \n      // Start modules based on config\n      const config = this.configManager.getConfig();\n      this.startModules(config);\n      \n      this.isInitialized = true;\n      this.emit('initialized');\n      \n      console.log('[SploopAdvancedMod] Successfully initialized!');\n      \n    } catch (error) {\n      console.error('[SploopAdvancedMod] Initialization failed:', error);\n    }\n  }\n  \n  private waitForGameLoad(): Promise<void> {\n    return new Promise((resolve) => {\n      const checkGame = () => {\n        if (this.gameApi.isInGame()) {\n          resolve();\n        } else {\n          setTimeout(checkGame, 1000);\n        }\n      };\n      checkGame();\n    });\n  }\n  \n  private setupEventListeners(): void {\n    // Game API events\n    this.gameApi.on('gameLoaded', () => {\n      console.log('[SploopAdvancedMod] Game loaded');\n    });\n    \n    this.gameApi.on('stateUpdate', (gameState) => {\n      this.updateUI();\n    });\n    \n    // Module events\n    this.autoHats.on('hatEquipped', (data) => {\n      this.logAction('Auto Hats', `Equipped ${data.hatType}`);\n    });\n    \n    this.autoHats.on('hatPurchased', (data) => {\n      this.logAction('Auto Hats', `Purchased ${data.hatType} for ${data.cost} gold`);\n    });\n    \n    this.autoBuy.on('itemPurchased', (data) => {\n      this.logAction('Auto Buy', `Purchased ${data.itemName}`);\n    });\n    \n    this.autoMills.on('millPlaced', (data) => {\n      this.logAction('Auto Mills', `Placed ${data.resourceType} mill`);\n    });\n    \n    this.autoMills.on('millUpgraded', (data) => {\n      this.logAction('Auto Mills', `Upgraded mill to level ${data.level + 1}`);\n    });\n    \n    this.botSystem.on('botSpawned', (data) => {\n      this.logAction('Bot System', `Spawned bot ${data.name}`);\n    });\n    \n    this.botSystem.on('botAction', (data) => {\n      this.logAction('Bot System', `Bot ${data.botId} performed ${data.action}`);\n    });\n  }\n  \n  private setupEmergencyStop(): void {\n    document.addEventListener('keydown', (event) => {\n      const config = this.configManager.getConfig();\n      const stopKey = config.safety.emergencyStop;\n      \n      // Parse emergency stop key combination (e.g., \"Ctrl+Shift+X\")\n      const keys = stopKey.split('+');\n      let matches = true;\n      \n      for (const key of keys) {\n        switch (key.trim()) {\n          case 'Ctrl':\n            if (!event.ctrlKey) matches = false;\n            break;\n          case 'Shift':\n            if (!event.shiftKey) matches = false;\n            break;\n          case 'Alt':\n            if (!event.altKey) matches = false;\n            break;\n          default:\n            if (event.key !== key) matches = false;\n        }\n      }\n      \n      if (matches) {\n        this.emergencyStop();\n      }\n    });\n  }\n  \n  private emergencyStop(): void {\n    if (this.emergencyStopActive) return;\n    \n    this.emergencyStopActive = true;\n    \n    console.log('[SploopAdvancedMod] EMERGENCY STOP ACTIVATED!');\n    \n    // Stop all modules\n    this.autoHats.stop();\n    this.autoBuy.stop();\n    this.autoMills.stop();\n    this.botSystem.stop();\n    \n    // Show emergency stop notification\n    this.showNotification('EMERGENCY STOP ACTIVATED', 'error');\n    \n    // Update UI\n    this.updateUI();\n    \n    this.emit('emergencyStop');\n  }\n  \n  private startModules(config: ModConfig): void {\n    if (config.autoHats.enabled && !this.emergencyStopActive) {\n      this.autoHats.start();\n    }\n    \n    if (config.autoBuy.enabled && !this.emergencyStopActive) {\n      this.autoBuy.start();\n    }\n    \n    if (config.autoMills.enabled && !this.emergencyStopActive) {\n      this.autoMills.start();\n    }\n    \n    if (config.botSystem.enabled && !this.emergencyStopActive) {\n      this.botSystem.start();\n    }\n  }\n  \n  private stopModules(): void {\n    this.autoHats.stop();\n    this.autoBuy.stop();\n    this.autoMills.stop();\n    this.botSystem.stop();\n  }\n\n  private createUI(): void {\n    if (this.ui) return;\n\n    const config = this.configManager.getConfig();\n    if (!config.ui.showOverlay) return;\n\n    // Create main UI container\n    this.ui = document.createElement('div');\n    this.ui.id = 'sploop-advanced-mod-ui';\n    this.ui.style.cssText = `\n      position: fixed;\n      ${config.ui.overlayPosition.includes('top') ? 'top: 10px;' : 'bottom: 10px;'}\n      ${config.ui.overlayPosition.includes('right') ? 'right: 10px;' : 'left: 10px;'}\n      width: 300px;\n      background: rgba(0, 0, 0, ${config.ui.transparency});\n      border: 2px solid #4CAF50;\n      border-radius: 8px;\n      padding: 10px;\n      font-family: Arial, sans-serif;\n      font-size: 12px;\n      color: white;\n      z-index: 10000;\n      user-select: none;\n    `;\n\n    document.body.appendChild(this.ui);\n    this.updateUI();\n  }\n\n  private updateUI(): void {\n    if (!this.ui) return;\n\n    const config = this.configManager.getConfig();\n    const stats = this.getModuleStats();\n\n    this.ui.innerHTML = `\n      <div style=\"border-bottom: 1px solid #4CAF50; margin-bottom: 8px; padding-bottom: 5px;\">\n        <strong>🚀 Sploop Advanced Mod v1.0.0</strong>\n        ${this.emergencyStopActive ? '<span style=\"color: red; float: right;\">⚠️ STOPPED</span>' : '<span style=\"color: green; float: right;\">✅ ACTIVE</span>'}\n      </div>\n\n      ${config.ui.showStats ? this.renderStats(stats) : ''}\n\n      <div style=\"margin-top: 8px; font-size: 10px; opacity: 0.7;\">\n        Emergency Stop: ${config.safety.emergencyStop}\n      </div>\n    `;\n  }\n\n  private renderStats(stats: ModuleStats): string {\n    return `\n      <div style=\"display: grid; grid-template-columns: 1fr 1fr; gap: 5px; font-size: 10px;\">\n        <div>\n          <strong>🎩 Auto Hats</strong><br>\n          Status: ${stats.autoHats.enabled ? '✅' : '❌'}<br>\n          Owned: ${stats.autoHats.ownedHats}/${stats.autoHats.totalHats}<br>\n          Current: ${stats.autoHats.equippedHat}\n        </div>\n\n        <div>\n          <strong>💰 Auto Buy</strong><br>\n          Status: ${stats.autoBuy.enabled ? '✅' : '❌'}<br>\n          Purchases: ${stats.autoBuy.totalPurchases}<br>\n          Queue: ${stats.autoBuy.queueLength}\n        </div>\n\n        <div>\n          <strong>🏭 Auto Mills</strong><br>\n          Status: ${stats.autoMills.enabled ? '✅' : '❌'}<br>\n          Mills: ${stats.autoMills.totalMills}/${stats.autoMills.maxMills}<br>\n          Protected: ${stats.autoMills.protectedMills}\n        </div>\n\n        <div>\n          <strong>🤖 Bot System</strong><br>\n          Status: ${stats.botSystem.enabled ? '✅' : '❌'}<br>\n          Bots: ${stats.botSystem.activeBots}/${stats.botSystem.maxBots}<br>\n          Threat: ${stats.botSystem.threatLevel}\n        </div>\n      </div>\n    `;\n  }\n\n  private getModuleStats(): ModuleStats {\n    return {\n      autoHats: this.autoHats.getStats(),\n      autoBuy: this.autoBuy.getStats(),\n      autoMills: this.autoMills.getStats(),\n      botSystem: this.botSystem.getStats()\n    };\n  }\n\n  private logAction(module: string, action: string): void {\n    const config = this.configManager.getConfig();\n\n    if (config.ui.showLogs) {\n      console.log(`[${module}] ${action}`);\n    }\n\n    if (config.debug) {\n      console.debug(`[${module}] ${action}`);\n    }\n  }\n\n  private showNotification(message: string, type: 'info' | 'success' | 'warning' | 'error' = 'info'): void {\n    const notification = document.createElement('div');\n    notification.style.cssText = `\n      position: fixed;\n      top: 50%;\n      left: 50%;\n      transform: translate(-50%, -50%);\n      background: ${type === 'error' ? '#f44336' : type === 'warning' ? '#ff9800' : type === 'success' ? '#4CAF50' : '#2196F3'};\n      color: white;\n      padding: 15px 25px;\n      border-radius: 5px;\n      font-family: Arial, sans-serif;\n      font-size: 14px;\n      font-weight: bold;\n      z-index: 20000;\n      box-shadow: 0 4px 8px rgba(0,0,0,0.3);\n    `;\n    notification.textContent = message;\n\n    document.body.appendChild(notification);\n\n    setTimeout(() => {\n      if (notification.parentNode) {\n        notification.parentNode.removeChild(notification);\n      }\n    }, 3000);\n  }\n\n  // Public API methods\n  public getConfig(): ModConfig {\n    return this.configManager.getConfig();\n  }\n\n  public updateConfig(updates: Partial<ModConfig>): void {\n    this.configManager.updateConfig(updates);\n    const newConfig = this.configManager.getConfig();\n\n    // Update modules with new config\n    this.autoHats.updateConfig(newConfig);\n    this.autoBuy.updateConfig(newConfig);\n    this.autoMills.updateConfig(newConfig);\n    this.botSystem.updateConfig(newConfig);\n\n    // Restart modules if needed\n    this.stopModules();\n    this.startModules(newConfig);\n\n    this.updateUI();\n  }\n\n  public resetConfig(): void {\n    this.configManager.resetConfig();\n    this.updateConfig({});\n  }\n\n  public getStats(): ModuleStats {\n    return this.getModuleStats();\n  }\n\n  public toggleModule(moduleName: 'autoHats' | 'autoBuy' | 'autoMills' | 'botSystem'): void {\n    const config = this.getConfig();\n\n    switch (moduleName) {\n      case 'autoHats':\n        this.updateConfig({ autoHats: { ...config.autoHats, enabled: !config.autoHats.enabled } });\n        break;\n      case 'autoBuy':\n        this.updateConfig({ autoBuy: { ...config.autoBuy, enabled: !config.autoBuy.enabled } });\n        break;\n      case 'autoMills':\n        this.updateConfig({ autoMills: { ...config.autoMills, enabled: !config.autoMills.enabled } });\n        break;\n      case 'botSystem':\n        this.updateConfig({ botSystem: { ...config.botSystem, enabled: !config.botSystem.enabled } });\n        break;\n    }\n  }\n\n  public destroy(): void {\n    this.stopModules();\n\n    if (this.ui && this.ui.parentNode) {\n      this.ui.parentNode.removeChild(this.ui);\n    }\n\n    this.gameApi.destroy();\n    this.removeAllListeners();\n\n    console.log('[SploopAdvancedMod] Destroyed');\n  }\n}\n\n// Initialize the mod when the script loads\nlet modInstance: SploopAdvancedMod | null = null;\n\nfunction initializeMod(): void {\n  if (modInstance) {\n    modInstance.destroy();\n  }\n\n  modInstance = new SploopAdvancedMod();\n\n  // Expose to global scope for debugging\n  (window as any).SploopAdvancedMod = modInstance;\n}\n\n// Auto-initialize when page loads\nif (document.readyState === 'loading') {\n  document.addEventListener('DOMContentLoaded', initializeMod);\n} else {\n  initializeMod();\n}\n\n// Handle page navigation in SPAs\nlet lastUrl = location.href;\nnew MutationObserver(() => {\n  const url = location.href;\n  if (url !== lastUrl) {\n    lastUrl = url;\n    setTimeout(initializeMod, 1000); // Reinitialize after navigation\n  }\n}).observe(document, { subtree: true, childList: true });\n\nexport default SploopAdvancedMod;\n"], "names": [], "sourceRoot": ""}