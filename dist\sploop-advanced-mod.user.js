(function webpackUniversalModuleDefinition(root, factory) {
	if(typeof exports === 'object' && typeof module === 'object')
		module.exports = factory();
	else if(typeof define === 'function' && define.amd)
		define([], factory);
	else if(typeof exports === 'object')
		exports["SploopAdvancedMod"] = factory();
	else
		root["SploopAdvancedMod"] = factory();
})(this, () => {
return /******/ (() => { // webpackBootstrap
/******/ 	"use strict";
/******/ 	var __webpack_modules__ = ({

/***/ 228:
/***/ ((module) => {



var has = Object.prototype.hasOwnProperty
  , prefix = '~';

/**
 * Constructor to create a storage for our `EE` objects.
 * An `Events` instance is a plain object whose properties are event names.
 *
 * @constructor
 * @private
 */
function Events() {}

//
// We try to not inherit from `Object.prototype`. In some engines creating an
// instance in this way is faster than calling `Object.create(null)` directly.
// If `Object.create(null)` is not supported we prefix the event names with a
// character to make sure that the built-in object properties are not
// overridden or used as an attack vector.
//
if (Object.create) {
  Events.prototype = Object.create(null);

  //
  // This hack is needed because the `__proto__` property is still inherited in
  // some old browsers like Android 4, iPhone 5.1, Opera 11 and Safari 5.
  //
  if (!new Events().__proto__) prefix = false;
}

/**
 * Representation of a single event listener.
 *
 * @param {Function} fn The listener function.
 * @param {*} context The context to invoke the listener with.
 * @param {Boolean} [once=false] Specify if the listener is a one-time listener.
 * @constructor
 * @private
 */
function EE(fn, context, once) {
  this.fn = fn;
  this.context = context;
  this.once = once || false;
}

/**
 * Add a listener for a given event.
 *
 * @param {EventEmitter} emitter Reference to the `EventEmitter` instance.
 * @param {(String|Symbol)} event The event name.
 * @param {Function} fn The listener function.
 * @param {*} context The context to invoke the listener with.
 * @param {Boolean} once Specify if the listener is a one-time listener.
 * @returns {EventEmitter}
 * @private
 */
function addListener(emitter, event, fn, context, once) {
  if (typeof fn !== 'function') {
    throw new TypeError('The listener must be a function');
  }

  var listener = new EE(fn, context || emitter, once)
    , evt = prefix ? prefix + event : event;

  if (!emitter._events[evt]) emitter._events[evt] = listener, emitter._eventsCount++;
  else if (!emitter._events[evt].fn) emitter._events[evt].push(listener);
  else emitter._events[evt] = [emitter._events[evt], listener];

  return emitter;
}

/**
 * Clear event by name.
 *
 * @param {EventEmitter} emitter Reference to the `EventEmitter` instance.
 * @param {(String|Symbol)} evt The Event name.
 * @private
 */
function clearEvent(emitter, evt) {
  if (--emitter._eventsCount === 0) emitter._events = new Events();
  else delete emitter._events[evt];
}

/**
 * Minimal `EventEmitter` interface that is molded against the Node.js
 * `EventEmitter` interface.
 *
 * @constructor
 * @public
 */
function EventEmitter() {
  this._events = new Events();
  this._eventsCount = 0;
}

/**
 * Return an array listing the events for which the emitter has registered
 * listeners.
 *
 * @returns {Array}
 * @public
 */
EventEmitter.prototype.eventNames = function eventNames() {
  var names = []
    , events
    , name;

  if (this._eventsCount === 0) return names;

  for (name in (events = this._events)) {
    if (has.call(events, name)) names.push(prefix ? name.slice(1) : name);
  }

  if (Object.getOwnPropertySymbols) {
    return names.concat(Object.getOwnPropertySymbols(events));
  }

  return names;
};

/**
 * Return the listeners registered for a given event.
 *
 * @param {(String|Symbol)} event The event name.
 * @returns {Array} The registered listeners.
 * @public
 */
EventEmitter.prototype.listeners = function listeners(event) {
  var evt = prefix ? prefix + event : event
    , handlers = this._events[evt];

  if (!handlers) return [];
  if (handlers.fn) return [handlers.fn];

  for (var i = 0, l = handlers.length, ee = new Array(l); i < l; i++) {
    ee[i] = handlers[i].fn;
  }

  return ee;
};

/**
 * Return the number of listeners listening to a given event.
 *
 * @param {(String|Symbol)} event The event name.
 * @returns {Number} The number of listeners.
 * @public
 */
EventEmitter.prototype.listenerCount = function listenerCount(event) {
  var evt = prefix ? prefix + event : event
    , listeners = this._events[evt];

  if (!listeners) return 0;
  if (listeners.fn) return 1;
  return listeners.length;
};

/**
 * Calls each of the listeners registered for a given event.
 *
 * @param {(String|Symbol)} event The event name.
 * @returns {Boolean} `true` if the event had listeners, else `false`.
 * @public
 */
EventEmitter.prototype.emit = function emit(event, a1, a2, a3, a4, a5) {
  var evt = prefix ? prefix + event : event;

  if (!this._events[evt]) return false;

  var listeners = this._events[evt]
    , len = arguments.length
    , args
    , i;

  if (listeners.fn) {
    if (listeners.once) this.removeListener(event, listeners.fn, undefined, true);

    switch (len) {
      case 1: return listeners.fn.call(listeners.context), true;
      case 2: return listeners.fn.call(listeners.context, a1), true;
      case 3: return listeners.fn.call(listeners.context, a1, a2), true;
      case 4: return listeners.fn.call(listeners.context, a1, a2, a3), true;
      case 5: return listeners.fn.call(listeners.context, a1, a2, a3, a4), true;
      case 6: return listeners.fn.call(listeners.context, a1, a2, a3, a4, a5), true;
    }

    for (i = 1, args = new Array(len -1); i < len; i++) {
      args[i - 1] = arguments[i];
    }

    listeners.fn.apply(listeners.context, args);
  } else {
    var length = listeners.length
      , j;

    for (i = 0; i < length; i++) {
      if (listeners[i].once) this.removeListener(event, listeners[i].fn, undefined, true);

      switch (len) {
        case 1: listeners[i].fn.call(listeners[i].context); break;
        case 2: listeners[i].fn.call(listeners[i].context, a1); break;
        case 3: listeners[i].fn.call(listeners[i].context, a1, a2); break;
        case 4: listeners[i].fn.call(listeners[i].context, a1, a2, a3); break;
        default:
          if (!args) for (j = 1, args = new Array(len -1); j < len; j++) {
            args[j - 1] = arguments[j];
          }

          listeners[i].fn.apply(listeners[i].context, args);
      }
    }
  }

  return true;
};

/**
 * Add a listener for a given event.
 *
 * @param {(String|Symbol)} event The event name.
 * @param {Function} fn The listener function.
 * @param {*} [context=this] The context to invoke the listener with.
 * @returns {EventEmitter} `this`.
 * @public
 */
EventEmitter.prototype.on = function on(event, fn, context) {
  return addListener(this, event, fn, context, false);
};

/**
 * Add a one-time listener for a given event.
 *
 * @param {(String|Symbol)} event The event name.
 * @param {Function} fn The listener function.
 * @param {*} [context=this] The context to invoke the listener with.
 * @returns {EventEmitter} `this`.
 * @public
 */
EventEmitter.prototype.once = function once(event, fn, context) {
  return addListener(this, event, fn, context, true);
};

/**
 * Remove the listeners of a given event.
 *
 * @param {(String|Symbol)} event The event name.
 * @param {Function} fn Only remove the listeners that match this function.
 * @param {*} context Only remove the listeners that have this context.
 * @param {Boolean} once Only remove one-time listeners.
 * @returns {EventEmitter} `this`.
 * @public
 */
EventEmitter.prototype.removeListener = function removeListener(event, fn, context, once) {
  var evt = prefix ? prefix + event : event;

  if (!this._events[evt]) return this;
  if (!fn) {
    clearEvent(this, evt);
    return this;
  }

  var listeners = this._events[evt];

  if (listeners.fn) {
    if (
      listeners.fn === fn &&
      (!once || listeners.once) &&
      (!context || listeners.context === context)
    ) {
      clearEvent(this, evt);
    }
  } else {
    for (var i = 0, events = [], length = listeners.length; i < length; i++) {
      if (
        listeners[i].fn !== fn ||
        (once && !listeners[i].once) ||
        (context && listeners[i].context !== context)
      ) {
        events.push(listeners[i]);
      }
    }

    //
    // Reset the array, or remove it completely if we have no more listeners.
    //
    if (events.length) this._events[evt] = events.length === 1 ? events[0] : events;
    else clearEvent(this, evt);
  }

  return this;
};

/**
 * Remove all listeners, or those of the specified event.
 *
 * @param {(String|Symbol)} [event] The event name.
 * @returns {EventEmitter} `this`.
 * @public
 */
EventEmitter.prototype.removeAllListeners = function removeAllListeners(event) {
  var evt;

  if (event) {
    evt = prefix ? prefix + event : event;
    if (this._events[evt]) clearEvent(this, evt);
  } else {
    this._events = new Events();
    this._eventsCount = 0;
  }

  return this;
};

//
// Alias methods names because people roll like that.
//
EventEmitter.prototype.off = EventEmitter.prototype.removeListener;
EventEmitter.prototype.addListener = EventEmitter.prototype.on;

//
// Expose the prefix.
//
EventEmitter.prefixed = prefix;

//
// Allow `EventEmitter` to be imported as module namespace.
//
EventEmitter.EventEmitter = EventEmitter;

//
// Expose the module.
//
if (true) {
  module.exports = EventEmitter;
}


/***/ })

/******/ 	});
/************************************************************************/
/******/ 	// The module cache
/******/ 	var __webpack_module_cache__ = {};
/******/ 	
/******/ 	// The require function
/******/ 	function __webpack_require__(moduleId) {
/******/ 		// Check if module is in cache
/******/ 		var cachedModule = __webpack_module_cache__[moduleId];
/******/ 		if (cachedModule !== undefined) {
/******/ 			return cachedModule.exports;
/******/ 		}
/******/ 		// Create a new module (and put it into the cache)
/******/ 		var module = __webpack_module_cache__[moduleId] = {
/******/ 			// no module.id needed
/******/ 			// no module.loaded needed
/******/ 			exports: {}
/******/ 		};
/******/ 	
/******/ 		// Execute the module function
/******/ 		__webpack_modules__[moduleId](module, module.exports, __webpack_require__);
/******/ 	
/******/ 		// Return the exports of the module
/******/ 		return module.exports;
/******/ 	}
/******/ 	
/************************************************************************/
/******/ 	/* webpack/runtime/define property getters */
/******/ 	(() => {
/******/ 		// define getter functions for harmony exports
/******/ 		__webpack_require__.d = (exports, definition) => {
/******/ 			for(var key in definition) {
/******/ 				if(__webpack_require__.o(definition, key) && !__webpack_require__.o(exports, key)) {
/******/ 					Object.defineProperty(exports, key, { enumerable: true, get: definition[key] });
/******/ 				}
/******/ 			}
/******/ 		};
/******/ 	})();
/******/ 	
/******/ 	/* webpack/runtime/hasOwnProperty shorthand */
/******/ 	(() => {
/******/ 		__webpack_require__.o = (obj, prop) => (Object.prototype.hasOwnProperty.call(obj, prop))
/******/ 	})();
/******/ 	
/******/ 	/* webpack/runtime/make namespace object */
/******/ 	(() => {
/******/ 		// define __esModule on exports
/******/ 		__webpack_require__.r = (exports) => {
/******/ 			if(typeof Symbol !== 'undefined' && Symbol.toStringTag) {
/******/ 				Object.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });
/******/ 			}
/******/ 			Object.defineProperty(exports, '__esModule', { value: true });
/******/ 		};
/******/ 	})();
/******/ 	
/************************************************************************/
var __webpack_exports__ = {};
// ESM COMPAT FLAG
__webpack_require__.r(__webpack_exports__);

// EXPORTS
__webpack_require__.d(__webpack_exports__, {
  "default": () => (/* binding */ main)
});

;// ./src/types/game.ts
// Sploop.io Game Type Definitions
var ItemType;
(function (ItemType) {
    ItemType["WEAPON"] = "weapon";
    ItemType["HAT"] = "hat";
    ItemType["FOOD"] = "food";
    ItemType["MATERIAL"] = "material";
    ItemType["TOOL"] = "tool";
})(ItemType || (ItemType = {}));
var HatType;
(function (HatType) {
    HatType["NONE"] = "none";
    HatType["MARKSMAN"] = "marksman";
    HatType["BUSH"] = "bush";
    HatType["BERSERKER"] = "berserker";
    HatType["JUNGLE"] = "jungle";
    HatType["CRYSTAL"] = "crystal";
    HatType["SPACE"] = "space";
    HatType["CYBORG"] = "cyborg";
    HatType["MONKEY"] = "monkey";
    HatType["ELF"] = "elf";
    HatType["KNIGHT"] = "knight";
    HatType["SAMURAI"] = "samurai";
    HatType["ANGEL"] = "angel";
    HatType["DEVIL"] = "devil";
})(HatType || (HatType = {}));
var BuildingType;
(function (BuildingType) {
    BuildingType["MILL"] = "mill";
    BuildingType["SPIKE"] = "spike";
    BuildingType["WALL"] = "wall";
    BuildingType["WINDMILL"] = "windmill";
    BuildingType["MINE"] = "mine";
    BuildingType["PIT_TRAP"] = "pit_trap";
    BuildingType["BOOST_PAD"] = "boost_pad";
    BuildingType["TURRET"] = "turret";
    BuildingType["PLATFORM"] = "platform";
    BuildingType["HEALING_PAD"] = "healing_pad";
    BuildingType["SPAWN_PAD"] = "spawn_pad";
    BuildingType["BLOCKER"] = "blocker";
    BuildingType["TELEPORTER"] = "teleporter";
})(BuildingType || (BuildingType = {}));
var ResourceType;
(function (ResourceType) {
    ResourceType["WOOD"] = "wood";
    ResourceType["STONE"] = "stone";
    ResourceType["FOOD"] = "food";
    ResourceType["GOLD"] = "gold";
})(ResourceType || (ResourceType = {}));
var ActionType;
(function (ActionType) {
    ActionType["MOVE"] = "move";
    ActionType["ATTACK"] = "attack";
    ActionType["BUILD"] = "build";
    ActionType["UPGRADE"] = "upgrade";
    ActionType["BUY"] = "buy";
    ActionType["EQUIP"] = "equip";
    ActionType["USE_ITEM"] = "use_item";
    ActionType["CHAT"] = "chat";
    ActionType["JOIN_CLAN"] = "join_clan";
    ActionType["LEAVE_CLAN"] = "leave_clan";
})(ActionType || (ActionType = {}));

// EXTERNAL MODULE: ./node_modules/eventemitter3/index.js
var eventemitter3 = __webpack_require__(228);
;// ./node_modules/eventemitter3/index.mjs



/* harmony default export */ const node_modules_eventemitter3 = ((/* unused pure expression or super */ null && (EventEmitter)));

;// ./src/utils/gameApi.ts


class SploopGameAPI extends eventemitter3 {
    constructor() {
        super();
        this.gameState = null;
        this.updateInterval = null;
        this.lastActionTime = 0;
        this.actionQueue = [];
        this.initialize();
    }
    initialize() {
        // Wait for game to load
        const checkGame = () => {
            if (this.isGameLoaded()) {
                this.startMonitoring();
                super.emit('gameLoaded');
            }
            else {
                setTimeout(checkGame, 1000);
            }
        };
        checkGame();
    }
    isGameLoaded() {
        return typeof window !== 'undefined' &&
            window.game &&
            window.player &&
            window.socket;
    }
    startMonitoring() {
        if (this.updateInterval) {
            clearInterval(this.updateInterval);
        }
        this.updateInterval = window.setInterval(() => {
            this.updateGameState();
            this.processActionQueue();
        }, 50);
    }
    updateGameState() {
        if (!this.isGameLoaded())
            return;
        try {
            const newState = {
                player: this.parsePlayer(window.player),
                players: this.parsePlayers(window.players),
                buildings: this.parseBuildings(window.buildings),
                items: new Map(),
                gameObjects: new Map(),
                isInGame: this.isInGame(),
                gameMode: window.game?.gameMode || 'unknown',
                serverInfo: {
                    region: window.game?.region || 'unknown',
                    playerCount: window.players?.length || 0,
                    maxPlayers: window.game?.maxPlayers || 0,
                    gameMode: window.game?.gameMode || 'unknown',
                    mapSize: window.game?.mapSize || 0
                }
            };
            this.gameState = newState;
            super.emit('stateUpdate', newState);
        }
        catch (error) {
            console.error('Error updating game state:', error);
        }
    }
    parsePlayer(playerData) {
        return {
            id: playerData?.id || '',
            name: playerData?.name || '',
            position: { x: playerData?.x || 0, y: playerData?.y || 0 },
            health: playerData?.health || 0,
            maxHealth: playerData?.maxHealth || 100,
            resources: {
                wood: playerData?.wood || 0,
                stone: playerData?.stone || 0,
                food: playerData?.food || 0,
                gold: playerData?.gold || 0,
                points: playerData?.points || 0
            },
            inventory: playerData?.inventory || [],
            hat: playerData?.hat || null,
            weapon: playerData?.weapon || null,
            level: playerData?.level || 1,
            experience: playerData?.experience || 0,
            clan: playerData?.clan || null,
            isBot: false
        };
    }
    parsePlayers(playersData) {
        const players = new Map();
        if (Array.isArray(playersData)) {
            playersData.forEach((playerData) => {
                const player = this.parsePlayer(playerData);
                players.set(player.id, player);
            });
        }
        return players;
    }
    parseBuildings(buildingsData) {
        const buildings = new Map();
        if (Array.isArray(buildingsData)) {
            buildingsData.forEach((buildingData) => {
                const building = {
                    id: buildingData?.id || '',
                    type: buildingData?.type || 'unknown',
                    position: { x: buildingData?.x || 0, y: buildingData?.y || 0 },
                    health: buildingData?.health || 0,
                    maxHealth: buildingData?.maxHealth || 100,
                    owner: buildingData?.owner || '',
                    level: buildingData?.level || 1,
                    isActive: buildingData?.isActive || false
                };
                buildings.set(building.id, building);
            });
        }
        return buildings;
    }
    getPlayer() {
        return this.gameState?.player || null;
    }
    getPlayers() {
        return this.gameState?.players || new Map();
    }
    getBuildings() {
        return this.gameState?.buildings || new Map();
    }
    getGameState() {
        return this.gameState || {
            player: {},
            players: new Map(),
            buildings: new Map(),
            items: new Map(),
            gameObjects: new Map(),
            isInGame: false,
            gameMode: 'unknown',
            serverInfo: {
                region: 'unknown',
                playerCount: 0,
                maxPlayers: 0,
                gameMode: 'unknown',
                mapSize: 0
            }
        };
    }
    sendAction(action) {
        this.actionQueue.push(action);
    }
    processActionQueue() {
        if (this.actionQueue.length === 0)
            return;
        const now = Date.now();
        if (now - this.lastActionTime < 200)
            return; // Rate limiting
        const action = this.actionQueue.shift();
        if (!action)
            return;
        try {
            this.executeAction(action);
            this.lastActionTime = now;
        }
        catch (error) {
            console.error('Error executing action:', error);
        }
    }
    executeAction(action) {
        if (!this.isGameLoaded() || !window.socket)
            return;
        switch (action.type) {
            case ActionType.MOVE:
                this.move(action.data.x, action.data.y);
                break;
            case ActionType.ATTACK:
                this.attack(action.data.angle);
                break;
            case ActionType.BUILD:
                this.build(action.data.type, action.data.x, action.data.y);
                break;
            case ActionType.BUY:
                this.buy(action.data.itemId);
                break;
            case ActionType.EQUIP:
                this.equip(action.data.itemId);
                break;
            case ActionType.CHAT:
                this.sendChat(action.data.message);
                break;
            default:
                console.warn('Unknown action type:', action.type);
        }
    }
    move(x, y) {
        if (window.socket && window.socket.emit) {
            window.socket.emit('move', { x, y });
        }
    }
    attack(angle) {
        if (window.socket && window.socket.emit) {
            window.socket.emit('attack', { angle });
        }
    }
    build(type, x, y) {
        if (window.socket && window.socket.emit) {
            window.socket.emit('build', { type, x, y });
        }
    }
    buy(itemId) {
        if (window.socket && window.socket.emit) {
            window.socket.emit('buy', { itemId });
        }
    }
    equip(itemId) {
        if (window.socket && window.socket.emit) {
            window.socket.emit('equip', { itemId });
        }
    }
    sendChat(message) {
        if (window.socket && window.socket.emit) {
            window.socket.emit('chat', { message });
        }
    }
    isInGame() {
        return this.isGameLoaded() &&
            window.game?.inGame === true &&
            this.gameState?.player?.id !== '';
    }
    calculateDistance(pos1, pos2) {
        const dx = pos1.x - pos2.x;
        const dy = pos1.y - pos2.y;
        return Math.sqrt(dx * dx + dy * dy);
    }
    calculateAngle(from, to) {
        return Math.atan2(to.y - from.y, to.x - from.x);
    }
    destroy() {
        if (this.updateInterval) {
            clearInterval(this.updateInterval);
            this.updateInterval = null;
        }
        super.removeAllListeners();
    }
}

;// ./src/config.ts

const DEFAULT_CONFIG = {
    enabled: true,
    debug: false,
    safeMode: true,
    updateInterval: 100,
    autoHats: {
        enabled: true,
        priority: [
            HatType.ANGEL,
            HatType.DEVIL,
            HatType.SAMURAI,
            HatType.KNIGHT,
            HatType.CYBORG,
            HatType.CRYSTAL,
            HatType.BERSERKER,
            HatType.MARKSMAN
        ],
        autoUpgrade: true,
        switchBasedOnSituation: true,
        combatHat: HatType.BERSERKER,
        farmingHat: HatType.MONKEY,
        explorationHat: HatType.BUSH
    },
    autoBuy: {
        enabled: true,
        priorityItems: [
            'weapon_upgrade',
            'hat_upgrade',
            'health_potion',
            'speed_boost'
        ],
        maxSpendPercentage: 80,
        reserveResources: {
            wood: 100,
            stone: 100,
            food: 50,
            gold: 50
        },
        buyHats: true,
        buyWeapons: true,
        buyUpgrades: true
    },
    autoMills: {
        enabled: true,
        maxMills: 8,
        resourcePriority: [
            ResourceType.GOLD,
            ResourceType.STONE,
            ResourceType.WOOD,
            ResourceType.FOOD
        ],
        autoUpgrade: true,
        optimalPlacement: true,
        protectMills: true,
        millSpacing: 200,
        preferredLocations: {
            nearResources: true,
            nearBase: false,
            hiddenAreas: true
        }
    },
    botSystem: {
        enabled: false, // Disabled by default for safety
        maxBots: 3,
        botBehaviors: {
            farming: true,
            combat: false,
            building: true,
            exploration: true,
            protection: true
        },
        coordination: {
            shareResources: true,
            groupMovement: false,
            defendTogether: true
        },
        intelligence: {
            avoidPlayers: true,
            learnFromActions: true,
            adaptToSituation: true
        }
    },
    safety: {
        antiDetection: true,
        randomizeActions: true,
        humanLikeMovement: true,
        pauseOnDanger: true,
        emergencyStop: 'Ctrl+Shift+X',
        maxActionsPerSecond: 5
    },
    ui: {
        showOverlay: true,
        showStats: true,
        showLogs: false,
        overlayPosition: 'top-right',
        transparency: 0.8
    }
};
class ConfigManager {
    constructor() {
        this.STORAGE_KEY = 'sploop_advanced_mod_config';
        this.config = this.loadConfig();
    }
    getConfig() {
        return { ...this.config };
    }
    updateConfig(updates) {
        this.config = { ...this.config, ...updates };
        this.saveConfig();
    }
    resetConfig() {
        this.config = { ...DEFAULT_CONFIG };
        this.saveConfig();
    }
    loadConfig() {
        try {
            const stored = localStorage.getItem(this.STORAGE_KEY);
            if (stored) {
                const parsed = JSON.parse(stored);
                return { ...DEFAULT_CONFIG, ...parsed };
            }
        }
        catch (error) {
            console.warn('Failed to load config, using defaults:', error);
        }
        return { ...DEFAULT_CONFIG };
    }
    saveConfig() {
        try {
            localStorage.setItem(this.STORAGE_KEY, JSON.stringify(this.config));
        }
        catch (error) {
            console.error('Failed to save config:', error);
        }
    }
}

;// ./src/modules/autoHats.ts


class AutoHatsModule extends eventemitter3 {
    constructor(gameApi, config) {
        super();
        this.enabled = false;
        this.updateInterval = null;
        this.lastHatCheck = 0;
        this.currentSituation = 'idle';
        // Hat database with stats and costs
        this.HAT_DATABASE = {
            [HatType.NONE]: {
                id: 'hat_none',
                type: HatType.NONE,
                name: 'No Hat',
                cost: 0,
                unlockLevel: 0,
                owned: true,
                equipped: false,
                stats: { healthBonus: 0, speedBonus: 0, damageBonus: 0, resourceBonus: 0, experienceBonus: 0 }
            },
            [HatType.MARKSMAN]: {
                id: 'hat_marksman',
                type: HatType.MARKSMAN,
                name: 'Marksman Hat',
                cost: 7000,
                unlockLevel: 1,
                owned: false,
                equipped: false,
                stats: { healthBonus: 0, speedBonus: 0, damageBonus: 25, resourceBonus: 0, experienceBonus: 0 }
            },
            [HatType.BUSH]: {
                id: 'hat_bush',
                type: HatType.BUSH,
                name: 'Bush Hat',
                cost: 3000,
                unlockLevel: 1,
                owned: false,
                equipped: false,
                stats: { healthBonus: 20, speedBonus: 0, damageBonus: 0, resourceBonus: 0, experienceBonus: 0 }
            },
            [HatType.BERSERKER]: {
                id: 'hat_berserker',
                type: HatType.BERSERKER,
                name: 'Berserker Hat',
                cost: 12000,
                unlockLevel: 7,
                owned: false,
                equipped: false,
                stats: { healthBonus: 0, speedBonus: 15, damageBonus: 35, resourceBonus: 0, experienceBonus: 0 }
            },
            [HatType.JUNGLE]: {
                id: 'hat_jungle',
                type: HatType.JUNGLE,
                name: 'Jungle Hat',
                cost: 15000,
                unlockLevel: 6,
                owned: false,
                equipped: false,
                stats: { healthBonus: 30, speedBonus: 0, damageBonus: 0, resourceBonus: 15, experienceBonus: 0 }
            },
            [HatType.CRYSTAL]: {
                id: 'hat_crystal',
                type: HatType.CRYSTAL,
                name: 'Crystal Hat',
                cost: 25000,
                unlockLevel: 12,
                owned: false,
                equipped: false,
                stats: { healthBonus: 0, speedBonus: 0, damageBonus: 0, resourceBonus: 25, experienceBonus: 25 }
            },
            [HatType.SPACE]: {
                id: 'hat_space',
                type: HatType.SPACE,
                name: 'Space Hat',
                cost: 30000,
                unlockLevel: 15,
                owned: false,
                equipped: false,
                stats: { healthBonus: 40, speedBonus: 20, damageBonus: 0, resourceBonus: 0, experienceBonus: 0 }
            },
            [HatType.CYBORG]: {
                id: 'hat_cyborg',
                type: HatType.CYBORG,
                name: 'Cyborg Hat',
                cost: 50000,
                unlockLevel: 18,
                owned: false,
                equipped: false,
                stats: { healthBonus: 25, speedBonus: 0, damageBonus: 40, resourceBonus: 0, experienceBonus: 15 }
            },
            [HatType.MONKEY]: {
                id: 'hat_monkey',
                type: HatType.MONKEY,
                name: 'Monkey Hat',
                cost: 8000,
                unlockLevel: 4,
                owned: false,
                equipped: false,
                stats: { healthBonus: 0, speedBonus: 25, damageBonus: 0, resourceBonus: 20, experienceBonus: 0 }
            },
            [HatType.ELF]: {
                id: 'hat_elf',
                type: HatType.ELF,
                name: 'Elf Hat',
                cost: 20000,
                unlockLevel: 9,
                owned: false,
                equipped: false,
                stats: { healthBonus: 0, speedBonus: 0, damageBonus: 20, resourceBonus: 30, experienceBonus: 0 }
            },
            [HatType.KNIGHT]: {
                id: 'hat_knight',
                type: HatType.KNIGHT,
                name: 'Knight Hat',
                cost: 35000,
                unlockLevel: 14,
                owned: false,
                equipped: false,
                stats: { healthBonus: 50, speedBonus: 0, damageBonus: 15, resourceBonus: 0, experienceBonus: 0 }
            },
            [HatType.SAMURAI]: {
                id: 'hat_samurai',
                type: HatType.SAMURAI,
                name: 'Samurai Hat',
                cost: 70000,
                unlockLevel: 20,
                owned: false,
                equipped: false,
                stats: { healthBonus: 35, speedBonus: 10, damageBonus: 50, resourceBonus: 0, experienceBonus: 0 }
            },
            [HatType.ANGEL]: {
                id: 'hat_angel',
                type: HatType.ANGEL,
                name: 'Angel Hat',
                cost: 100000,
                unlockLevel: 25,
                owned: false,
                equipped: false,
                stats: { healthBonus: 60, speedBonus: 15, damageBonus: 30, resourceBonus: 20, experienceBonus: 30 }
            },
            [HatType.DEVIL]: {
                id: 'hat_devil',
                type: HatType.DEVIL,
                name: 'Devil Hat',
                cost: 150000,
                unlockLevel: 30,
                owned: false,
                equipped: false,
                stats: { healthBonus: 40, speedBonus: 20, damageBonus: 60, resourceBonus: 15, experienceBonus: 25 }
            }
        };
        this.gameApi = gameApi;
        this.config = config;
        this.gameApi.on('stateUpdate', this.onGameStateUpdate.bind(this));
    }
    start() {
        if (this.enabled)
            return;
        this.enabled = true;
        this.updateInterval = window.setInterval(() => {
            this.update();
        }, this.config.updateInterval);
        this.emit('started');
        console.log('[AutoHats] Module started');
    }
    stop() {
        if (!this.enabled)
            return;
        this.enabled = false;
        if (this.updateInterval) {
            clearInterval(this.updateInterval);
            this.updateInterval = null;
        }
        this.emit('stopped');
        console.log('[AutoHats] Module stopped');
    }
    updateConfig(config) {
        this.config = config;
    }
    onGameStateUpdate() {
        if (!this.enabled || !this.config.autoHats.enabled)
            return;
        this.updateHatOwnership();
        this.analyzeSituation();
    }
    update() {
        if (!this.enabled || !this.config.autoHats.enabled || !this.gameApi.isInGame())
            return;
        const now = Date.now();
        if (now - this.lastHatCheck < 2000)
            return; // Check every 2 seconds
        this.lastHatCheck = now;
        try {
            this.processAutoHats();
        }
        catch (error) {
            console.error('[AutoHats] Error in update:', error);
        }
    }
    updateHatOwnership() {
        const player = this.gameApi.getPlayer();
        if (!player)
            return;
        // Update owned hats based on player inventory
        Object.values(this.HAT_DATABASE).forEach(hat => {
            hat.owned = this.isHatOwned(hat.type, player);
            hat.equipped = this.isHatEquipped(hat.type, player);
        });
    }
    isHatOwned(hatType, player) {
        if (hatType === HatType.NONE)
            return true;
        // Check if hat is in inventory
        return player.inventory.some(item => item.type === 'hat' && item.id.includes(hatType));
    }
    isHatEquipped(hatType, player) {
        if (!player.hat)
            return hatType === HatType.NONE;
        return player.hat.type === hatType;
    }
    analyzeSituation() {
        const player = this.gameApi.getPlayer();
        const players = this.gameApi.getPlayers();
        if (!player)
            return;
        // Determine current situation based on game state
        const nearbyEnemies = Array.from(players.values()).filter(p => p.id !== player.id &&
            this.gameApi.calculateDistance(player.position, p.position) < 300);
        const isLowHealth = player.health < player.maxHealth * 0.5;
        const hasResources = Object.values(player.resources).some(r => r > 100);
        if (nearbyEnemies.length > 0 || isLowHealth) {
            this.currentSituation = 'combat';
        }
        else if (hasResources) {
            this.currentSituation = 'farming';
        }
        else {
            this.currentSituation = 'exploration';
        }
    }
    processAutoHats() {
        const player = this.gameApi.getPlayer();
        if (!player)
            return;
        let targetHat;
        if (this.config.autoHats.switchBasedOnSituation) {
            targetHat = this.getOptimalHatForSituation();
        }
        else {
            targetHat = this.getBestAvailableHat();
        }
        // Check if we need to buy the target hat
        if (this.config.autoHats.autoUpgrade && !this.HAT_DATABASE[targetHat].owned) {
            this.tryBuyHat(targetHat);
        }
        // Equip the target hat if we own it and it's not already equipped
        if (this.HAT_DATABASE[targetHat].owned && !this.HAT_DATABASE[targetHat].equipped) {
            this.equipHat(targetHat);
        }
    }
    getOptimalHatForSituation() {
        switch (this.currentSituation) {
            case 'combat':
                return this.getBestOwnedHat([this.config.autoHats.combatHat]) ||
                    this.getBestAvailableHat();
            case 'farming':
                return this.getBestOwnedHat([this.config.autoHats.farmingHat]) ||
                    this.getBestAvailableHat();
            case 'exploration':
                return this.getBestOwnedHat([this.config.autoHats.explorationHat]) ||
                    this.getBestAvailableHat();
            default:
                return this.getBestAvailableHat();
        }
    }
    getBestOwnedHat(preferred = []) {
        // First check preferred hats
        for (const hatType of preferred) {
            if (this.HAT_DATABASE[hatType].owned) {
                return hatType;
            }
        }
        // Then check priority list
        for (const hatType of this.config.autoHats.priority) {
            if (this.HAT_DATABASE[hatType].owned) {
                return hatType;
            }
        }
        return null;
    }
    getBestAvailableHat() {
        const player = this.gameApi.getPlayer();
        if (!player)
            return HatType.NONE;
        // Find the best hat we can afford and have unlocked
        for (const hatType of this.config.autoHats.priority) {
            const hat = this.HAT_DATABASE[hatType];
            if (hat.owned ||
                (player.level >= hat.unlockLevel && player.resources.gold >= hat.cost)) {
                return hatType;
            }
        }
        return HatType.NONE;
    }
    tryBuyHat(hatType) {
        const player = this.gameApi.getPlayer();
        const hat = this.HAT_DATABASE[hatType];
        if (!player || hat.owned)
            return;
        if (player.level >= hat.unlockLevel && player.resources.gold >= hat.cost) {
            this.gameApi.sendAction({
                type: ActionType.BUY,
                data: { itemId: hat.id },
                timestamp: Date.now()
            });
            this.emit('hatPurchased', { hatType, cost: hat.cost });
            console.log(`[AutoHats] Purchased ${hat.name} for ${hat.cost} gold`);
        }
    }
    equipHat(hatType) {
        const hat = this.HAT_DATABASE[hatType];
        this.gameApi.sendAction({
            type: ActionType.EQUIP,
            data: { itemId: hat.id },
            timestamp: Date.now()
        });
        this.emit('hatEquipped', { hatType });
        console.log(`[AutoHats] Equipped ${hat.name}`);
    }
    getHatInfo(hatType) {
        return { ...this.HAT_DATABASE[hatType] };
    }
    getAllHats() {
        return Object.values(this.HAT_DATABASE).map(hat => ({ ...hat }));
    }
    getCurrentSituation() {
        return this.currentSituation;
    }
    getStats() {
        const ownedHats = Object.values(this.HAT_DATABASE).filter(h => h.owned).length;
        const totalHats = Object.values(this.HAT_DATABASE).length;
        return {
            enabled: this.enabled,
            ownedHats,
            totalHats,
            currentSituation: this.currentSituation,
            equippedHat: Object.values(this.HAT_DATABASE).find(h => h.equipped)?.name || 'None'
        };
    }
}

;// ./src/modules/autoBuy.ts


class AutoBuyModule extends eventemitter3 {
    constructor(gameApi, config) {
        super();
        this.enabled = false;
        this.updateInterval = null;
        this.lastBuyCheck = 0;
        this.purchaseHistory = [];
        this.buyQueue = [];
        // Shop items database
        this.SHOP_ITEMS = {
            // Weapons
            'weapon_wood_sword': {
                id: 'weapon_wood_sword',
                name: 'Wood Sword',
                type: 'weapon',
                cost: { wood: 0, stone: 0, food: 0, gold: 100, points: 0 },
                unlockLevel: 1,
                priority: 5,
                description: 'Basic wooden sword',
                effects: { damage: 25 }
            },
            'weapon_stone_sword': {
                id: 'weapon_stone_sword',
                name: 'Stone Sword',
                type: 'weapon',
                cost: { wood: 0, stone: 0, food: 0, gold: 400, points: 0 },
                unlockLevel: 2,
                priority: 6,
                description: 'Stronger stone sword',
                effects: { damage: 35 }
            },
            'weapon_gold_sword': {
                id: 'weapon_gold_sword',
                name: 'Gold Sword',
                type: 'weapon',
                cost: { wood: 0, stone: 0, food: 0, gold: 3000, points: 0 },
                unlockLevel: 5,
                priority: 7,
                description: 'Powerful gold sword',
                effects: { damage: 50 }
            },
            'weapon_diamond_sword': {
                id: 'weapon_diamond_sword',
                name: 'Diamond Sword',
                type: 'weapon',
                cost: { wood: 0, stone: 0, food: 0, gold: 15000, points: 0 },
                unlockLevel: 10,
                priority: 8,
                description: 'Elite diamond sword',
                effects: { damage: 65 }
            },
            // Upgrades
            'upgrade_damage_1': {
                id: 'upgrade_damage_1',
                name: 'Damage Upgrade I',
                type: 'upgrade',
                cost: { wood: 0, stone: 0, food: 0, gold: 1000, points: 0 },
                unlockLevel: 2,
                priority: 9,
                description: 'Increases weapon damage',
                effects: { damageMultiplier: 1.1 }
            },
            'upgrade_damage_2': {
                id: 'upgrade_damage_2',
                name: 'Damage Upgrade II',
                type: 'upgrade',
                cost: { wood: 0, stone: 0, food: 0, gold: 3000, points: 0 },
                unlockLevel: 5,
                priority: 9,
                description: 'Further increases weapon damage',
                effects: { damageMultiplier: 1.2 }
            },
            'upgrade_speed_1': {
                id: 'upgrade_speed_1',
                name: 'Speed Upgrade I',
                type: 'upgrade',
                cost: { wood: 0, stone: 0, food: 0, gold: 1500, points: 0 },
                unlockLevel: 3,
                priority: 8,
                description: 'Increases movement speed',
                effects: { speedMultiplier: 1.15 }
            },
            'upgrade_health_1': {
                id: 'upgrade_health_1',
                name: 'Health Upgrade I',
                type: 'upgrade',
                cost: { wood: 0, stone: 0, food: 0, gold: 2000, points: 0 },
                unlockLevel: 4,
                priority: 8,
                description: 'Increases maximum health',
                effects: { healthBonus: 20 }
            },
            // Consumables
            'consumable_health_potion': {
                id: 'consumable_health_potion',
                name: 'Health Potion',
                type: 'consumable',
                cost: { wood: 0, stone: 0, food: 0, gold: 200, points: 0 },
                unlockLevel: 1,
                priority: 6,
                description: 'Restores health instantly',
                effects: { healthRestore: 50 }
            },
            'consumable_speed_boost': {
                id: 'consumable_speed_boost',
                name: 'Speed Boost',
                type: 'consumable',
                cost: { wood: 0, stone: 0, food: 0, gold: 300, points: 0 },
                unlockLevel: 2,
                priority: 5,
                description: 'Temporary speed increase',
                effects: { speedBoost: 30, duration: 10000 }
            },
            // Buildings
            'building_mill': {
                id: 'building_mill',
                name: 'Windmill',
                type: 'building',
                cost: { wood: 5, stone: 5, food: 0, gold: 0, points: 0 },
                unlockLevel: 1,
                priority: 10,
                description: 'Generates resources automatically',
                effects: { resourceGeneration: 1 }
            },
            'building_spike': {
                id: 'building_spike',
                name: 'Spike',
                type: 'building',
                cost: { wood: 10, stone: 0, food: 0, gold: 0, points: 0 },
                unlockLevel: 1,
                priority: 7,
                description: 'Defensive structure that damages enemies',
                effects: { damage: 25 }
            }
        };
        this.gameApi = gameApi;
        this.config = config;
        this.gameApi.on('stateUpdate', this.onGameStateUpdate.bind(this));
    }
    start() {
        if (this.enabled)
            return;
        this.enabled = true;
        this.updateInterval = window.setInterval(() => {
            this.update();
        }, this.config.updateInterval * 2); // Run less frequently than other modules
        this.emit('started');
        console.log('[AutoBuy] Module started');
    }
    stop() {
        if (!this.enabled)
            return;
        this.enabled = false;
        if (this.updateInterval) {
            clearInterval(this.updateInterval);
            this.updateInterval = null;
        }
        this.emit('stopped');
        console.log('[AutoBuy] Module stopped');
    }
    updateConfig(config) {
        this.config = config;
    }
    onGameStateUpdate() {
        if (!this.enabled || !this.config.autoBuy.enabled)
            return;
        // Process any queued purchases
        this.processBuyQueue();
    }
    update() {
        if (!this.enabled || !this.config.autoBuy.enabled || !this.gameApi.isInGame())
            return;
        const now = Date.now();
        if (now - this.lastBuyCheck < 5000)
            return; // Check every 5 seconds
        this.lastBuyCheck = now;
        try {
            this.processAutoBuy();
        }
        catch (error) {
            console.error('[AutoBuy] Error in update:', error);
        }
    }
    processAutoBuy() {
        const player = this.gameApi.getPlayer();
        if (!player)
            return;
        // Get available items to buy
        const availableItems = this.getAvailableItems(player);
        // Sort by priority (higher priority first)
        availableItems.sort((a, b) => b.priority - a.priority);
        // Try to buy the highest priority item we can afford
        for (const item of availableItems) {
            if (this.canAffordItem(player, item) && this.shouldBuyItem(player, item)) {
                this.queuePurchase(item.id);
                break; // Only buy one item per cycle
            }
        }
    }
    getAvailableItems(player) {
        return Object.values(this.SHOP_ITEMS).filter(item => {
            // Check if player has unlocked this item
            if (player.level < item.unlockLevel)
                return false;
            // Check if item type is enabled in config
            switch (item.type) {
                case 'hat':
                    return this.config.autoBuy.buyHats;
                case 'weapon':
                    return this.config.autoBuy.buyWeapons;
                case 'upgrade':
                    return this.config.autoBuy.buyUpgrades;
                default:
                    return true;
            }
        });
    }
    canAffordItem(player, item) {
        const reserves = this.config.autoBuy.reserveResources;
        return (player.resources.wood >= item.cost.wood + reserves.wood &&
            player.resources.stone >= item.cost.stone + reserves.stone &&
            player.resources.food >= item.cost.food + reserves.food &&
            player.resources.gold >= item.cost.gold + reserves.gold);
    }
    shouldBuyItem(player, item) {
        // Check if item is in priority list
        if (this.config.autoBuy.priorityItems.length > 0) {
            if (!this.config.autoBuy.priorityItems.includes(item.id)) {
                return false;
            }
        }
        // Check spending limits
        const totalResources = Object.values(player.resources).reduce((sum, val) => sum + val, 0);
        const itemCost = Object.values(item.cost).reduce((sum, val) => sum + val, 0);
        const spendPercentage = (itemCost / totalResources) * 100;
        if (spendPercentage > this.config.autoBuy.maxSpendPercentage) {
            return false;
        }
        // Check if we already own this item (for non-consumables)
        if (item.type !== 'consumable') {
            const alreadyOwned = player.inventory.some(invItem => invItem.id === item.id);
            if (alreadyOwned)
                return false;
        }
        // Additional logic for specific item types
        switch (item.type) {
            case 'consumable':
                return this.shouldBuyConsumable(player, item);
            case 'weapon':
                return this.shouldBuyWeapon(player, item);
            case 'upgrade':
                return this.shouldBuyUpgrade(player, item);
            default:
                return true;
        }
    }
    shouldBuyConsumable(player, item) {
        // Buy health potions if health is low
        if (item.id === 'consumable_health_potion') {
            return player.health < player.maxHealth * 0.7;
        }
        // Buy speed boosts occasionally
        if (item.id === 'consumable_speed_boost') {
            return Math.random() < 0.1; // 10% chance
        }
        return true;
    }
    shouldBuyWeapon(player, item) {
        // Only buy if it's better than current weapon
        if (!player.weapon)
            return true;
        const currentDamage = player.weapon.damage;
        const newDamage = item.effects.damage || 0;
        return newDamage > currentDamage;
    }
    shouldBuyUpgrade(player, item) {
        // Always buy upgrades if we can afford them
        return true;
    }
    queuePurchase(itemId) {
        if (!this.buyQueue.includes(itemId)) {
            this.buyQueue.push(itemId);
        }
    }
    processBuyQueue() {
        if (this.buyQueue.length === 0)
            return;
        const itemId = this.buyQueue.shift();
        if (!itemId)
            return;
        const item = this.SHOP_ITEMS[itemId];
        if (!item)
            return;
        const player = this.gameApi.getPlayer();
        if (!player || !this.canAffordItem(player, item)) {
            return; // Skip this purchase
        }
        // Execute the purchase
        this.gameApi.sendAction({
            type: ActionType.BUY,
            data: { itemId: item.id },
            timestamp: Date.now()
        });
        // Record the purchase
        const purchase = {
            itemId: item.id,
            itemName: item.name,
            cost: { ...item.cost },
            timestamp: Date.now(),
            success: true // We'll assume success for now
        };
        this.purchaseHistory.push(purchase);
        // Keep only last 50 purchases
        if (this.purchaseHistory.length > 50) {
            this.purchaseHistory = this.purchaseHistory.slice(-50);
        }
        this.emit('itemPurchased', purchase);
        console.log(`[AutoBuy] Purchased ${item.name} for`, item.cost);
    }
    getShopItems() {
        return Object.values(this.SHOP_ITEMS).map(item => ({ ...item }));
    }
    getPurchaseHistory() {
        return [...this.purchaseHistory];
    }
    clearPurchaseHistory() {
        this.purchaseHistory = [];
    }
    addCustomItem(item) {
        this.SHOP_ITEMS[item.id] = { ...item };
    }
    getStats() {
        const totalPurchases = this.purchaseHistory.length;
        const totalSpent = this.purchaseHistory.reduce((sum, purchase) => {
            return sum + Object.values(purchase.cost).reduce((costSum, val) => costSum + val, 0);
        }, 0);
        const recentPurchases = this.purchaseHistory.filter(p => Date.now() - p.timestamp < 300000 // Last 5 minutes
        ).length;
        return {
            enabled: this.enabled,
            totalPurchases,
            totalSpent,
            recentPurchases,
            queueLength: this.buyQueue.length,
            availableItems: Object.keys(this.SHOP_ITEMS).length
        };
    }
}

;// ./src/modules/autoMills.ts


class AutoMillsModule extends eventemitter3 {
    constructor(gameApi, config) {
        super();
        this.enabled = false;
        this.updateInterval = null;
        this.lastMillCheck = 0;
        this.lastPlacementCheck = 0;
        this.ownedMills = new Map();
        this.resourceNodes = new Map();
        this.dangerZones = [];
        // Mill costs and stats
        this.MILL_COSTS = {
            [ResourceType.WOOD]: { wood: 5, stone: 5, food: 0, gold: 0 },
            [ResourceType.STONE]: { wood: 5, stone: 5, food: 0, gold: 0 },
            [ResourceType.FOOD]: { wood: 10, stone: 0, food: 0, gold: 0 },
            [ResourceType.GOLD]: { wood: 20, stone: 10, food: 0, gold: 0 }
        };
        this.UPGRADE_COSTS = {
            1: { wood: 15, stone: 15, food: 0, gold: 0 },
            2: { wood: 25, stone: 25, food: 0, gold: 0 },
            3: { wood: 35, stone: 35, food: 0, gold: 0 }
        };
        this.gameApi = gameApi;
        this.config = config;
        this.gameApi.on('stateUpdate', this.onGameStateUpdate.bind(this));
    }
    start() {
        if (this.enabled)
            return;
        this.enabled = true;
        this.updateInterval = window.setInterval(() => {
            this.update();
        }, this.config.updateInterval);
        this.emit('started');
        console.log('[AutoMills] Module started');
    }
    stop() {
        if (!this.enabled)
            return;
        this.enabled = false;
        if (this.updateInterval) {
            clearInterval(this.updateInterval);
            this.updateInterval = null;
        }
        this.emit('stopped');
        console.log('[AutoMills] Module stopped');
    }
    updateConfig(config) {
        this.config = config;
    }
    onGameStateUpdate() {
        if (!this.enabled || !this.config.autoMills.enabled)
            return;
        this.updateMillTracking();
        this.updateResourceNodes();
        this.updateDangerZones();
    }
    update() {
        if (!this.enabled || !this.config.autoMills.enabled || !this.gameApi.isInGame())
            return;
        const now = Date.now();
        // Check for mill upgrades every 3 seconds
        if (now - this.lastMillCheck > 3000) {
            this.lastMillCheck = now;
            this.processMillUpgrades();
        }
        // Check for new mill placements every 10 seconds
        if (now - this.lastPlacementCheck > 10000) {
            this.lastPlacementCheck = now;
            this.processMillPlacement();
        }
        // Protect mills if enabled
        if (this.config.autoMills.protectMills) {
            this.protectMills();
        }
    }
    updateMillTracking() {
        const buildings = this.gameApi.getBuildings();
        const player = this.gameApi.getPlayer();
        if (!player)
            return;
        // Update owned mills
        this.ownedMills.clear();
        buildings.forEach((building, id) => {
            if (building.type === BuildingType.MILL && building.owner === player.id) {
                const mill = building;
                const millStats = {
                    id: mill.id,
                    position: mill.position,
                    resourceType: mill.resourceType,
                    level: mill.level,
                    production: mill.productionRate,
                    storage: mill.storage,
                    maxStorage: mill.maxStorage,
                    lastUpgrade: 0, // We'll track this separately
                    isProtected: this.isMillProtected(mill.position)
                };
                this.ownedMills.set(id, millStats);
            }
        });
    }
    updateResourceNodes() {
        // In a real implementation, this would scan the map for resource nodes
        // For now, we'll use some example positions
        this.resourceNodes.clear();
        // These would be detected from the game map
        const exampleNodes = [
            { type: ResourceType.WOOD, pos: { x: 100, y: 100 } },
            { type: ResourceType.STONE, pos: { x: 200, y: 150 } },
            { type: ResourceType.FOOD, pos: { x: 150, y: 200 } },
            { type: ResourceType.GOLD, pos: { x: 300, y: 250 } }
        ];
        exampleNodes.forEach((node, index) => {
            this.resourceNodes.set(`${node.type}_${index}`, node.pos);
        });
    }
    updateDangerZones() {
        const players = this.gameApi.getPlayers();
        const player = this.gameApi.getPlayer();
        if (!player)
            return;
        this.dangerZones = [];
        // Mark areas near enemy players as dangerous
        players.forEach(otherPlayer => {
            if (otherPlayer.id !== player.id) {
                this.dangerZones.push(otherPlayer.position);
            }
        });
    }
    processMillPlacement() {
        const player = this.gameApi.getPlayer();
        if (!player)
            return;
        const currentMillCount = this.ownedMills.size;
        if (currentMillCount >= this.config.autoMills.maxMills) {
            return; // Already at max mills
        }
        // Find optimal placement for next mill
        const optimalPlacement = this.findOptimalMillPlacement();
        if (optimalPlacement && this.canAffordMill(player, optimalPlacement.resourceType)) {
            this.placeMill(optimalPlacement);
        }
    }
    findOptimalMillPlacement() {
        const player = this.gameApi.getPlayer();
        if (!player)
            return null;
        const placements = [];
        // Evaluate potential positions around resource nodes
        this.resourceNodes.forEach((nodePos, nodeId) => {
            const resourceType = this.getResourceTypeFromNodeId(nodeId);
            // Generate positions around the resource node
            for (let angle = 0; angle < 360; angle += 45) {
                const distance = this.config.autoMills.millSpacing;
                const x = nodePos.x + Math.cos(angle * Math.PI / 180) * distance;
                const y = nodePos.y + Math.sin(angle * Math.PI / 180) * distance;
                const position = { x, y };
                if (this.isValidMillPosition(position)) {
                    const placement = {
                        position,
                        resourceType,
                        priority: this.calculatePlacementPriority(resourceType),
                        safety: this.calculateSafety(position),
                        efficiency: this.calculateEfficiency(position, resourceType)
                    };
                    placements.push(placement);
                }
            }
        });
        // Sort by overall score (priority + safety + efficiency)
        placements.sort((a, b) => {
            const scoreA = a.priority + a.safety + a.efficiency;
            const scoreB = b.priority + b.safety + b.efficiency;
            return scoreB - scoreA;
        });
        return placements[0] || null;
    }
    getResourceTypeFromNodeId(nodeId) {
        if (nodeId.includes('wood'))
            return ResourceType.WOOD;
        if (nodeId.includes('stone'))
            return ResourceType.STONE;
        if (nodeId.includes('food'))
            return ResourceType.FOOD;
        if (nodeId.includes('gold'))
            return ResourceType.GOLD;
        return ResourceType.WOOD; // Default
    }
    isValidMillPosition(position) {
        // Check if position is too close to existing mills
        for (const mill of this.ownedMills.values()) {
            const distance = this.gameApi.calculateDistance(position, mill.position);
            if (distance < this.config.autoMills.millSpacing) {
                return false;
            }
        }
        // Check if position is in a danger zone
        for (const dangerPos of this.dangerZones) {
            const distance = this.gameApi.calculateDistance(position, dangerPos);
            if (distance < 150) { // Too close to enemies
                return false;
            }
        }
        return true;
    }
    calculatePlacementPriority(resourceType) {
        const priorityIndex = this.config.autoMills.resourcePriority.indexOf(resourceType);
        return priorityIndex >= 0 ? (10 - priorityIndex) : 1;
    }
    calculateSafety(position) {
        let safety = 10;
        // Reduce safety based on proximity to danger zones
        for (const dangerPos of this.dangerZones) {
            const distance = this.gameApi.calculateDistance(position, dangerPos);
            if (distance < 300) {
                safety -= (300 - distance) / 30;
            }
        }
        return Math.max(0, safety);
    }
    calculateEfficiency(position, resourceType) {
        let efficiency = 5;
        // Increase efficiency based on proximity to resource nodes
        this.resourceNodes.forEach((nodePos, nodeId) => {
            if (this.getResourceTypeFromNodeId(nodeId) === resourceType) {
                const distance = this.gameApi.calculateDistance(position, nodePos);
                if (distance < 200) {
                    efficiency += (200 - distance) / 20;
                }
            }
        });
        return efficiency;
    }
    canAffordMill(player, resourceType) {
        const cost = this.MILL_COSTS[resourceType];
        return (player.resources.wood >= cost.wood &&
            player.resources.stone >= cost.stone &&
            player.resources.food >= cost.food &&
            player.resources.gold >= cost.gold);
    }
    placeMill(placement) {
        this.gameApi.sendAction({
            type: ActionType.BUILD,
            data: {
                type: 'mill',
                x: placement.position.x,
                y: placement.position.y,
                resourceType: placement.resourceType
            },
            timestamp: Date.now()
        });
        this.emit('millPlaced', placement);
        console.log(`[AutoMills] Placed ${placement.resourceType} mill at (${placement.position.x}, ${placement.position.y})`);
    }
    processMillUpgrades() {
        if (!this.config.autoMills.autoUpgrade)
            return;
        const player = this.gameApi.getPlayer();
        if (!player)
            return;
        // Find mills that can be upgraded
        for (const mill of this.ownedMills.values()) {
            if (mill.level < 4 && this.canAffordUpgrade(player, mill.level)) {
                this.upgradeMill(mill);
                break; // Only upgrade one mill per cycle
            }
        }
    }
    canAffordUpgrade(player, currentLevel) {
        const cost = this.UPGRADE_COSTS[currentLevel];
        if (!cost)
            return false;
        return (player.resources.wood >= cost.wood &&
            player.resources.stone >= cost.stone &&
            player.resources.food >= cost.food &&
            player.resources.gold >= cost.gold);
    }
    upgradeMill(mill) {
        this.gameApi.sendAction({
            type: ActionType.UPGRADE,
            data: { buildingId: mill.id },
            timestamp: Date.now()
        });
        this.emit('millUpgraded', mill);
        console.log(`[AutoMills] Upgraded mill ${mill.id} to level ${mill.level + 1}`);
    }
    isMillProtected(position) {
        // Check if there are defensive structures nearby
        const buildings = this.gameApi.getBuildings();
        for (const building of buildings.values()) {
            if (building.type === BuildingType.SPIKE || building.type === BuildingType.WALL) {
                const distance = this.gameApi.calculateDistance(position, building.position);
                if (distance < 100) {
                    return true;
                }
            }
        }
        return false;
    }
    protectMills() {
        const player = this.gameApi.getPlayer();
        if (!player)
            return;
        // Find unprotected mills
        for (const mill of this.ownedMills.values()) {
            if (!mill.isProtected && this.canAffordSpike(player)) {
                this.placeProtection(mill);
                break; // Only protect one mill per cycle
            }
        }
    }
    canAffordSpike(player) {
        return player.resources.wood >= 10;
    }
    placeProtection(mill) {
        // Place spikes around the mill
        const angles = [0, 90, 180, 270];
        const distance = 80;
        for (const angle of angles) {
            const x = mill.position.x + Math.cos(angle * Math.PI / 180) * distance;
            const y = mill.position.y + Math.sin(angle * Math.PI / 180) * distance;
            this.gameApi.sendAction({
                type: ActionType.BUILD,
                data: {
                    type: 'spike',
                    x,
                    y
                },
                timestamp: Date.now()
            });
        }
        this.emit('millProtected', mill);
        console.log(`[AutoMills] Protected mill ${mill.id} with spikes`);
    }
    getOwnedMills() {
        return Array.from(this.ownedMills.values());
    }
    getStats() {
        const totalMills = this.ownedMills.size;
        const protectedMills = Array.from(this.ownedMills.values()).filter(m => m.isProtected).length;
        const totalProduction = Array.from(this.ownedMills.values()).reduce((sum, mill) => sum + mill.production, 0);
        return {
            enabled: this.enabled,
            totalMills,
            maxMills: this.config.autoMills.maxMills,
            protectedMills,
            totalProduction,
            resourceNodes: this.resourceNodes.size,
            dangerZones: this.dangerZones.length
        };
    }
}

;// ./src/modules/botSystem.ts


var BotBehavior;
(function (BotBehavior) {
    BotBehavior["FARMING"] = "farming";
    BotBehavior["COMBAT"] = "combat";
    BotBehavior["BUILDING"] = "building";
    BotBehavior["EXPLORATION"] = "exploration";
    BotBehavior["PROTECTION"] = "protection";
    BotBehavior["IDLE"] = "idle";
})(BotBehavior || (BotBehavior = {}));
var BotState;
(function (BotState) {
    BotState["SPAWNING"] = "spawning";
    BotState["MOVING"] = "moving";
    BotState["WORKING"] = "working";
    BotState["FIGHTING"] = "fighting";
    BotState["FLEEING"] = "fleeing";
    BotState["DEAD"] = "dead";
    BotState["IDLE"] = "idle";
})(BotState || (BotState = {}));
class BotSystemModule extends eventemitter3 {
    constructor(gameApi, config) {
        super();
        this.enabled = false;
        this.updateInterval = null;
        this.bots = new Map();
        this.taskQueue = [];
        this.lastBotSpawn = 0;
        this.coordinationData = {
            sharedResources: { wood: 0, stone: 0, food: 0, gold: 0 },
            groupTarget: null,
            threatLevel: 0
        };
        this.gameApi = gameApi;
        this.config = config;
        this.gameApi.on('stateUpdate', this.onGameStateUpdate.bind(this));
    }
    start() {
        if (this.enabled)
            return;
        this.enabled = true;
        this.updateInterval = window.setInterval(() => {
            this.update();
        }, this.config.updateInterval);
        this.emit('started');
        console.log('[BotSystem] Module started');
    }
    stop() {
        if (!this.enabled)
            return;
        this.enabled = false;
        if (this.updateInterval) {
            clearInterval(this.updateInterval);
            this.updateInterval = null;
        }
        // Despawn all bots
        this.bots.clear();
        this.emit('stopped');
        console.log('[BotSystem] Module stopped');
    }
    updateConfig(config) {
        this.config = config;
    }
    onGameStateUpdate() {
        if (!this.enabled || !this.config.botSystem.enabled)
            return;
        this.updateBotStates();
        this.updateCoordination();
    }
    update() {
        if (!this.enabled || !this.config.botSystem.enabled || !this.gameApi.isInGame())
            return;
        try {
            this.manageBotSpawning();
            this.processBotActions();
            this.assignTasks();
            this.updateBotBehaviors();
        }
        catch (error) {
            console.error('[BotSystem] Error in update:', error);
        }
    }
    manageBotSpawning() {
        const now = Date.now();
        const currentBotCount = this.bots.size;
        // Spawn new bots if needed
        if (currentBotCount < this.config.botSystem.maxBots &&
            now - this.lastBotSpawn > 10000) { // 10 second cooldown
            this.spawnBot();
            this.lastBotSpawn = now;
        }
        // Remove dead bots
        for (const [botId, bot] of this.bots.entries()) {
            if (bot.state === BotState.DEAD) {
                this.bots.delete(botId);
                this.emit('botDespawned', { botId, reason: 'death' });
            }
        }
    }
    spawnBot() {
        const player = this.gameApi.getPlayer();
        if (!player)
            return;
        const botId = `bot_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
        const spawnPosition = this.findSafeSpawnPosition(player.position);
        const bot = {
            id: botId,
            name: `Bot_${this.bots.size + 1}`,
            position: spawnPosition,
            target: null,
            behavior: this.selectBotBehavior(),
            state: BotState.SPAWNING,
            lastAction: Date.now(),
            resources: { wood: 0, stone: 0, food: 0, gold: 0 },
            health: 100,
            level: 1,
            task: null
        };
        this.bots.set(botId, bot);
        this.emit('botSpawned', bot);
        console.log(`[BotSystem] Spawned bot ${bot.name} with behavior ${bot.behavior}`);
    }
    findSafeSpawnPosition(playerPos) {
        // Spawn bots near the player but not too close
        const angle = Math.random() * 2 * Math.PI;
        const distance = 100 + Math.random() * 100; // 100-200 units away
        return {
            x: playerPos.x + Math.cos(angle) * distance,
            y: playerPos.y + Math.sin(angle) * distance
        };
    }
    selectBotBehavior() {
        const behaviors = this.config.botSystem.botBehaviors;
        const availableBehaviors = [];
        if (behaviors.farming)
            availableBehaviors.push(BotBehavior.FARMING);
        if (behaviors.combat)
            availableBehaviors.push(BotBehavior.COMBAT);
        if (behaviors.building)
            availableBehaviors.push(BotBehavior.BUILDING);
        if (behaviors.exploration)
            availableBehaviors.push(BotBehavior.EXPLORATION);
        if (behaviors.protection)
            availableBehaviors.push(BotBehavior.PROTECTION);
        if (availableBehaviors.length === 0) {
            return BotBehavior.IDLE;
        }
        return availableBehaviors[Math.floor(Math.random() * availableBehaviors.length)] || BotBehavior.IDLE;
    }
    updateBotStates() {
        const players = this.gameApi.getPlayers();
        for (const bot of this.bots.values()) {
            // Check for nearby threats
            const nearbyEnemies = Array.from(players.values()).filter(p => !p.isBot &&
                this.gameApi.calculateDistance(bot.position, p.position) < 200);
            // Update bot state based on situation
            if (nearbyEnemies.length > 0 && this.config.botSystem.intelligence.avoidPlayers) {
                if (bot.behavior !== BotBehavior.COMBAT) {
                    bot.state = BotState.FLEEING;
                }
                else {
                    bot.state = BotState.FIGHTING;
                }
            }
            else if (bot.task) {
                bot.state = BotState.WORKING;
            }
            else {
                bot.state = BotState.IDLE;
            }
        }
    }
    updateCoordination() {
        if (!this.config.botSystem.coordination.shareResources)
            return;
        // Calculate shared resources
        this.coordinationData.sharedResources = { wood: 0, stone: 0, food: 0, gold: 0 };
        for (const bot of this.bots.values()) {
            this.coordinationData.sharedResources.wood += bot.resources.wood;
            this.coordinationData.sharedResources.stone += bot.resources.stone;
            this.coordinationData.sharedResources.food += bot.resources.food;
            this.coordinationData.sharedResources.gold += bot.resources.gold;
        }
        // Update threat level
        const players = this.gameApi.getPlayers();
        const nearbyEnemies = Array.from(players.values()).filter(p => {
            if (p.isBot)
                return false;
            for (const bot of this.bots.values()) {
                if (this.gameApi.calculateDistance(bot.position, p.position) < 300) {
                    return true;
                }
            }
            return false;
        });
        this.coordinationData.threatLevel = nearbyEnemies.length;
    }
    assignTasks() {
        // Generate new tasks based on bot behaviors
        for (const bot of this.bots.values()) {
            if (bot.task && Date.now() - bot.task.startTime < bot.task.estimatedDuration) {
                continue; // Bot is still working on current task
            }
            bot.task = this.generateTaskForBot(bot);
        }
    }
    generateTaskForBot(bot) {
        switch (bot.behavior) {
            case BotBehavior.FARMING:
                return this.generateFarmingTask(bot);
            case BotBehavior.BUILDING:
                return this.generateBuildingTask(bot);
            case BotBehavior.EXPLORATION:
                return this.generateExplorationTask(bot);
            case BotBehavior.PROTECTION:
                return this.generateProtectionTask(bot);
            default:
                return null;
        }
    }
    generateFarmingTask(bot) {
        // Find nearest resource node
        const resourceTypes = [ResourceType.WOOD, ResourceType.STONE, ResourceType.FOOD];
        const targetResource = resourceTypes[Math.floor(Math.random() * resourceTypes.length)];
        // Generate a position near a resource (simplified)
        const angle = Math.random() * 2 * Math.PI;
        const distance = 50 + Math.random() * 100;
        const target = {
            x: bot.position.x + Math.cos(angle) * distance,
            y: bot.position.y + Math.sin(angle) * distance
        };
        return {
            type: 'farm',
            target,
            priority: 5,
            startTime: Date.now(),
            estimatedDuration: 15000 // 15 seconds
        };
    }
    generateBuildingTask(bot) {
        // Find a good position to build
        const angle = Math.random() * 2 * Math.PI;
        const distance = 100 + Math.random() * 150;
        const target = {
            x: bot.position.x + Math.cos(angle) * distance,
            y: bot.position.y + Math.sin(angle) * distance
        };
        return {
            type: 'build',
            target,
            priority: 6,
            startTime: Date.now(),
            estimatedDuration: 20000 // 20 seconds
        };
    }
    generateExplorationTask(bot) {
        // Generate random exploration target
        const angle = Math.random() * 2 * Math.PI;
        const distance = 200 + Math.random() * 300;
        const target = {
            x: bot.position.x + Math.cos(angle) * distance,
            y: bot.position.y + Math.sin(angle) * distance
        };
        return {
            type: 'explore',
            target,
            priority: 3,
            startTime: Date.now(),
            estimatedDuration: 25000 // 25 seconds
        };
    }
    generateProtectionTask(bot) {
        const player = this.gameApi.getPlayer();
        if (!player)
            return this.generateExplorationTask(bot);
        // Protect the main player
        return {
            type: 'defend',
            target: player.position,
            priority: 8,
            startTime: Date.now(),
            estimatedDuration: 30000 // 30 seconds
        };
    }
    processBotActions() {
        const now = Date.now();
        for (const bot of this.bots.values()) {
            if (now - bot.lastAction < 500)
                continue; // Rate limiting
            this.executeBotAction(bot);
            bot.lastAction = now;
        }
    }
    executeBotAction(bot) {
        if (!bot.task)
            return;
        switch (bot.state) {
            case BotState.MOVING:
                this.moveBotToTarget(bot);
                break;
            case BotState.WORKING:
                this.executeBotTask(bot);
                break;
            case BotState.FLEEING:
                this.fleeFromDanger(bot);
                break;
            case BotState.FIGHTING:
                this.engageInCombat(bot);
                break;
        }
    }
    moveBotToTarget(bot) {
        if (!bot.task)
            return;
        const distance = this.gameApi.calculateDistance(bot.position, bot.task.target);
        if (distance < 50) {
            bot.state = BotState.WORKING;
            return;
        }
        // Move towards target
        const angle = this.gameApi.calculateAngle(bot.position, bot.task.target);
        const speed = 2; // Bot movement speed
        bot.position.x += Math.cos(angle) * speed;
        bot.position.y += Math.sin(angle) * speed;
        // Send movement action (this would be sent to the game)
        this.gameApi.sendAction({
            type: ActionType.MOVE,
            data: { x: bot.position.x, y: bot.position.y },
            timestamp: Date.now()
        });
    }
    executeBotTask(bot) {
        if (!bot.task)
            return;
        switch (bot.task.type) {
            case 'farm':
                this.executeFarmingAction(bot);
                break;
            case 'build':
                this.executeBuildingAction(bot);
                break;
            case 'explore':
                this.executeExplorationAction(bot);
                break;
            case 'defend':
                this.executeDefenseAction(bot);
                break;
        }
    }
    executeFarmingAction(bot) {
        // Simulate resource gathering
        const resourceGain = Math.floor(Math.random() * 5) + 1;
        const resourceType = ['wood', 'stone', 'food'][Math.floor(Math.random() * 3)];
        bot.resources[resourceType] += resourceGain;
        this.emit('botAction', {
            botId: bot.id,
            action: 'farm',
            result: { [resourceType]: resourceGain }
        });
    }
    executeBuildingAction(bot) {
        // Simulate building placement
        if (bot.resources.wood >= 5 && bot.resources.stone >= 5) {
            bot.resources.wood -= 5;
            bot.resources.stone -= 5;
            this.gameApi.sendAction({
                type: ActionType.BUILD,
                data: {
                    type: 'mill',
                    x: bot.task.target.x,
                    y: bot.task.target.y
                },
                timestamp: Date.now()
            });
            this.emit('botAction', {
                botId: bot.id,
                action: 'build',
                result: { building: 'mill' }
            });
        }
    }
    executeExplorationAction(bot) {
        // Exploration increases bot level/experience
        bot.level += 0.1;
        this.emit('botAction', {
            botId: bot.id,
            action: 'explore',
            result: { experience: 0.1 }
        });
    }
    executeDefenseAction(bot) {
        // Stay near the target and be ready to defend
        const player = this.gameApi.getPlayer();
        if (player) {
            bot.task.target = player.position;
        }
    }
    fleeFromDanger(bot) {
        const players = this.gameApi.getPlayers();
        // Find nearest enemy
        let nearestEnemy = null;
        let minDistance = Infinity;
        for (const player of players.values()) {
            if (!player.isBot) {
                const distance = this.gameApi.calculateDistance(bot.position, player.position);
                if (distance < minDistance) {
                    minDistance = distance;
                    nearestEnemy = player;
                }
            }
        }
        if (nearestEnemy) {
            // Move away from enemy
            const angle = this.gameApi.calculateAngle(nearestEnemy.position, bot.position);
            const speed = 3; // Faster when fleeing
            bot.position.x += Math.cos(angle) * speed;
            bot.position.y += Math.sin(angle) * speed;
        }
    }
    engageInCombat(bot) {
        // Simple combat AI - attack nearest enemy
        const players = this.gameApi.getPlayers();
        for (const player of players.values()) {
            if (!player.isBot) {
                const distance = this.gameApi.calculateDistance(bot.position, player.position);
                if (distance < 100) {
                    const angle = this.gameApi.calculateAngle(bot.position, player.position);
                    this.gameApi.sendAction({
                        type: ActionType.ATTACK,
                        data: { angle },
                        timestamp: Date.now()
                    });
                    break;
                }
            }
        }
    }
    updateBotBehaviors() {
        if (!this.config.botSystem.intelligence.adaptToSituation)
            return;
        // Adapt bot behaviors based on current situation
        for (const bot of this.bots.values()) {
            if (this.coordinationData.threatLevel > 2) {
                // High threat - switch to defensive behaviors
                if (bot.behavior === BotBehavior.FARMING || bot.behavior === BotBehavior.EXPLORATION) {
                    bot.behavior = BotBehavior.PROTECTION;
                }
            }
            else if (this.coordinationData.threatLevel === 0) {
                // No threats - focus on resource gathering
                if (bot.behavior === BotBehavior.PROTECTION) {
                    bot.behavior = BotBehavior.FARMING;
                }
            }
        }
    }
    getBots() {
        return Array.from(this.bots.values());
    }
    getBotById(botId) {
        return this.bots.get(botId) || null;
    }
    getCoordinationData() {
        return { ...this.coordinationData };
    }
    getStats() {
        const totalBots = this.bots.size;
        const activeBots = Array.from(this.bots.values()).filter(b => b.state !== BotState.DEAD).length;
        const behaviorCounts = {};
        for (const bot of this.bots.values()) {
            behaviorCounts[bot.behavior] = (behaviorCounts[bot.behavior] || 0) + 1;
        }
        return {
            enabled: this.enabled,
            totalBots,
            activeBots,
            maxBots: this.config.botSystem.maxBots,
            behaviorCounts,
            threatLevel: this.coordinationData.threatLevel,
            sharedResources: this.coordinationData.sharedResources
        };
    }
}

;// ./src/main.ts
// ==UserScript==
// @name         Sploop.io Advanced Mod
// @namespace    http://tampermonkey.net/
// @version      1.0.0
// @description  Advanced sploop.io mod with auto hats, auto buy, auto mills, and bot system
// <AUTHOR> Mod Developer
// @match        *://sploop.io/*
// @match        *://*.sploop.io/*
// @grant        none
// @run-at       document-start
// ==/UserScript==







class SploopAdvancedMod extends eventemitter3 {
    constructor() {
        super();
        this.ui = null;
        this.isInitialized = false;
        this.emergencyStopActive = false;
        console.log('[SploopAdvancedMod] Initializing...');
        // Initialize core components
        this.configManager = new ConfigManager();
        this.gameApi = new SploopGameAPI();
        // Initialize modules
        const config = this.configManager.getConfig();
        this.autoHats = new AutoHatsModule(this.gameApi, config);
        this.autoBuy = new AutoBuyModule(this.gameApi, config);
        this.autoMills = new AutoMillsModule(this.gameApi, config);
        this.botSystem = new BotSystemModule(this.gameApi, config);
        this.setupEventListeners();
        this.setupEmergencyStop();
        this.initialize();
    }
    async initialize() {
        try {
            // Wait for game to load
            await this.waitForGameLoad();
            // Create UI
            this.createUI();
            // Start modules based on config
            const config = this.configManager.getConfig();
            this.startModules(config);
            this.isInitialized = true;
            this.emit('initialized');
            console.log('[SploopAdvancedMod] Successfully initialized!');
        }
        catch (error) {
            console.error('[SploopAdvancedMod] Initialization failed:', error);
        }
    }
    waitForGameLoad() {
        return new Promise((resolve) => {
            const checkGame = () => {
                if (this.gameApi.isInGame()) {
                    resolve();
                }
                else {
                    setTimeout(checkGame, 1000);
                }
            };
            checkGame();
        });
    }
    setupEventListeners() {
        // Game API events
        this.gameApi.on('gameLoaded', () => {
            console.log('[SploopAdvancedMod] Game loaded');
        });
        this.gameApi.on('stateUpdate', (gameState) => {
            this.updateUI();
        });
        // Module events
        this.autoHats.on('hatEquipped', (data) => {
            this.logAction('Auto Hats', `Equipped ${data.hatType}`);
        });
        this.autoHats.on('hatPurchased', (data) => {
            this.logAction('Auto Hats', `Purchased ${data.hatType} for ${data.cost} gold`);
        });
        this.autoBuy.on('itemPurchased', (data) => {
            this.logAction('Auto Buy', `Purchased ${data.itemName}`);
        });
        this.autoMills.on('millPlaced', (data) => {
            this.logAction('Auto Mills', `Placed ${data.resourceType} mill`);
        });
        this.autoMills.on('millUpgraded', (data) => {
            this.logAction('Auto Mills', `Upgraded mill to level ${data.level + 1}`);
        });
        this.botSystem.on('botSpawned', (data) => {
            this.logAction('Bot System', `Spawned bot ${data.name}`);
        });
        this.botSystem.on('botAction', (data) => {
            this.logAction('Bot System', `Bot ${data.botId} performed ${data.action}`);
        });
    }
    setupEmergencyStop() {
        document.addEventListener('keydown', (event) => {
            const config = this.configManager.getConfig();
            const stopKey = config.safety.emergencyStop;
            // Parse emergency stop key combination (e.g., "Ctrl+Shift+X")
            const keys = stopKey.split('+');
            let matches = true;
            for (const key of keys) {
                switch (key.trim()) {
                    case 'Ctrl':
                        if (!event.ctrlKey)
                            matches = false;
                        break;
                    case 'Shift':
                        if (!event.shiftKey)
                            matches = false;
                        break;
                    case 'Alt':
                        if (!event.altKey)
                            matches = false;
                        break;
                    default:
                        if (event.key !== key)
                            matches = false;
                }
            }
            if (matches) {
                this.emergencyStop();
            }
        });
    }
    emergencyStop() {
        if (this.emergencyStopActive)
            return;
        this.emergencyStopActive = true;
        console.log('[SploopAdvancedMod] EMERGENCY STOP ACTIVATED!');
        // Stop all modules
        this.autoHats.stop();
        this.autoBuy.stop();
        this.autoMills.stop();
        this.botSystem.stop();
        // Show emergency stop notification
        this.showNotification('EMERGENCY STOP ACTIVATED', 'error');
        // Update UI
        this.updateUI();
        this.emit('emergencyStop');
    }
    startModules(config) {
        if (config.autoHats.enabled && !this.emergencyStopActive) {
            this.autoHats.start();
        }
        if (config.autoBuy.enabled && !this.emergencyStopActive) {
            this.autoBuy.start();
        }
        if (config.autoMills.enabled && !this.emergencyStopActive) {
            this.autoMills.start();
        }
        if (config.botSystem.enabled && !this.emergencyStopActive) {
            this.botSystem.start();
        }
    }
    stopModules() {
        this.autoHats.stop();
        this.autoBuy.stop();
        this.autoMills.stop();
        this.botSystem.stop();
    }
    createUI() {
        if (this.ui)
            return;
        const config = this.configManager.getConfig();
        if (!config.ui.showOverlay)
            return;
        // Create main UI container
        this.ui = document.createElement('div');
        this.ui.id = 'sploop-advanced-mod-ui';
        this.ui.style.cssText = `
      position: fixed;
      ${config.ui.overlayPosition.includes('top') ? 'top: 10px;' : 'bottom: 10px;'}
      ${config.ui.overlayPosition.includes('right') ? 'right: 10px;' : 'left: 10px;'}
      width: 300px;
      background: rgba(0, 0, 0, ${config.ui.transparency});
      border: 2px solid #4CAF50;
      border-radius: 8px;
      padding: 10px;
      font-family: Arial, sans-serif;
      font-size: 12px;
      color: white;
      z-index: 10000;
      user-select: none;
    `;
        document.body.appendChild(this.ui);
        this.updateUI();
    }
    updateUI() {
        if (!this.ui)
            return;
        const config = this.configManager.getConfig();
        const stats = this.getModuleStats();
        this.ui.innerHTML = `
      <div style="border-bottom: 1px solid #4CAF50; margin-bottom: 8px; padding-bottom: 5px;">
        <strong>🚀 Sploop Advanced Mod v1.0.0</strong>
        ${this.emergencyStopActive ? '<span style="color: red; float: right;">⚠️ STOPPED</span>' : '<span style="color: green; float: right;">✅ ACTIVE</span>'}
      </div>

      ${config.ui.showStats ? this.renderStats(stats) : ''}

      <div style="margin-top: 8px; font-size: 10px; opacity: 0.7;">
        Emergency Stop: ${config.safety.emergencyStop}
      </div>
    `;
    }
    renderStats(stats) {
        return `
      <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 5px; font-size: 10px;">
        <div>
          <strong>🎩 Auto Hats</strong><br>
          Status: ${stats.autoHats.enabled ? '✅' : '❌'}<br>
          Owned: ${stats.autoHats.ownedHats}/${stats.autoHats.totalHats}<br>
          Current: ${stats.autoHats.equippedHat}
        </div>

        <div>
          <strong>💰 Auto Buy</strong><br>
          Status: ${stats.autoBuy.enabled ? '✅' : '❌'}<br>
          Purchases: ${stats.autoBuy.totalPurchases}<br>
          Queue: ${stats.autoBuy.queueLength}
        </div>

        <div>
          <strong>🏭 Auto Mills</strong><br>
          Status: ${stats.autoMills.enabled ? '✅' : '❌'}<br>
          Mills: ${stats.autoMills.totalMills}/${stats.autoMills.maxMills}<br>
          Protected: ${stats.autoMills.protectedMills}
        </div>

        <div>
          <strong>🤖 Bot System</strong><br>
          Status: ${stats.botSystem.enabled ? '✅' : '❌'}<br>
          Bots: ${stats.botSystem.activeBots}/${stats.botSystem.maxBots}<br>
          Threat: ${stats.botSystem.threatLevel}
        </div>
      </div>
    `;
    }
    getModuleStats() {
        return {
            autoHats: this.autoHats.getStats(),
            autoBuy: this.autoBuy.getStats(),
            autoMills: this.autoMills.getStats(),
            botSystem: this.botSystem.getStats()
        };
    }
    logAction(module, action) {
        const config = this.configManager.getConfig();
        if (config.ui.showLogs) {
            console.log(`[${module}] ${action}`);
        }
        if (config.debug) {
            console.debug(`[${module}] ${action}`);
        }
    }
    showNotification(message, type = 'info') {
        const notification = document.createElement('div');
        notification.style.cssText = `
      position: fixed;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      background: ${type === 'error' ? '#f44336' : type === 'warning' ? '#ff9800' : type === 'success' ? '#4CAF50' : '#2196F3'};
      color: white;
      padding: 15px 25px;
      border-radius: 5px;
      font-family: Arial, sans-serif;
      font-size: 14px;
      font-weight: bold;
      z-index: 20000;
      box-shadow: 0 4px 8px rgba(0,0,0,0.3);
    `;
        notification.textContent = message;
        document.body.appendChild(notification);
        setTimeout(() => {
            if (notification.parentNode) {
                notification.parentNode.removeChild(notification);
            }
        }, 3000);
    }
    // Public API methods
    getConfig() {
        return this.configManager.getConfig();
    }
    updateConfig(updates) {
        this.configManager.updateConfig(updates);
        const newConfig = this.configManager.getConfig();
        // Update modules with new config
        this.autoHats.updateConfig(newConfig);
        this.autoBuy.updateConfig(newConfig);
        this.autoMills.updateConfig(newConfig);
        this.botSystem.updateConfig(newConfig);
        // Restart modules if needed
        this.stopModules();
        this.startModules(newConfig);
        this.updateUI();
    }
    resetConfig() {
        this.configManager.resetConfig();
        this.updateConfig({});
    }
    getStats() {
        return this.getModuleStats();
    }
    toggleModule(moduleName) {
        const config = this.getConfig();
        switch (moduleName) {
            case 'autoHats':
                this.updateConfig({ autoHats: { ...config.autoHats, enabled: !config.autoHats.enabled } });
                break;
            case 'autoBuy':
                this.updateConfig({ autoBuy: { ...config.autoBuy, enabled: !config.autoBuy.enabled } });
                break;
            case 'autoMills':
                this.updateConfig({ autoMills: { ...config.autoMills, enabled: !config.autoMills.enabled } });
                break;
            case 'botSystem':
                this.updateConfig({ botSystem: { ...config.botSystem, enabled: !config.botSystem.enabled } });
                break;
        }
    }
    destroy() {
        this.stopModules();
        if (this.ui && this.ui.parentNode) {
            this.ui.parentNode.removeChild(this.ui);
        }
        this.gameApi.destroy();
        this.removeAllListeners();
        console.log('[SploopAdvancedMod] Destroyed');
    }
}
// Initialize the mod when the script loads
let modInstance = null;
function initializeMod() {
    if (modInstance) {
        modInstance.destroy();
    }
    modInstance = new SploopAdvancedMod();
    // Expose to global scope for debugging
    window.SploopAdvancedMod = modInstance;
}
// Auto-initialize when page loads
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', initializeMod);
}
else {
    initializeMod();
}
// Handle page navigation in SPAs
let lastUrl = location.href;
new MutationObserver(() => {
    const url = location.href;
    if (url !== lastUrl) {
        lastUrl = url;
        setTimeout(initializeMod, 1000); // Reinitialize after navigation
    }
}).observe(document, { subtree: true, childList: true });
/* harmony default export */ const main = (SploopAdvancedMod);

/******/ 	return __webpack_exports__;
/******/ })()
;
});
//# sourceMappingURL=sploop-advanced-mod.user.js.map