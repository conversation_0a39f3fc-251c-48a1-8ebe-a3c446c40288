import { SploopGameAPI } from '@/utils/gameApi';
import { ModConfig } from '@/config';
import { Player, Vector2, ResourceType, ActionType } from '@/types/game';
import { AdvancedPathfinding } from '@/utils/pathfinding';
import { EventEmitter } from 'eventemitter3';

export interface ResourceNode {
  id: string;
  type: ResourceType;
  position: Vector2;
  abundance: number;
  safety: number;
  accessibility: number;
  lastVisited: number;
  estimatedYield: number;
}

export interface ResourceRoute {
  nodes: ResourceNode[];
  totalDistance: number;
  estimatedTime: number;
  efficiency: number;
  safety: number;
}

export interface OptimizationStrategy {
  name: string;
  prioritizeType: ResourceType | 'balanced';
  maxTravelDistance: number;
  safetyWeight: number;
  efficiencyWeight: number;
  diversificationBonus: number;
}

export class ResourceOptimizerModule extends EventEmitter {
  private gameApi: SploopGameAPI;
  private config: ModConfig;
  private pathfinding: AdvancedPathfinding;
  private enabled = false;
  private updateInterval: number | null = null;
  
  private resourceNodes: Map<string, ResourceNode> = new Map();
  private currentRoute: ResourceRoute | null = null;
  private currentNodeIndex = 0;
  private lastOptimization = 0;
  private gatheringStartTime = 0;
  private isGathering = false;
  
  // Optimization strategies
  private readonly STRATEGIES: Record<string, OptimizationStrategy> = {
    balanced: {
      name: 'Balanced',
      prioritizeType: 'balanced',
      maxTravelDistance: 300,
      safetyWeight: 0.3,
      efficiencyWeight: 0.5,
      diversificationBonus: 0.2
    },
    goldFocus: {
      name: 'Gold Focus',
      prioritizeType: ResourceType.GOLD,
      maxTravelDistance: 500,
      safetyWeight: 0.2,
      efficiencyWeight: 0.7,
      diversificationBonus: 0.1
    },
    safeFarming: {
      name: 'Safe Farming',
      prioritizeType: 'balanced',
      maxTravelDistance: 200,
      safetyWeight: 0.6,
      efficiencyWeight: 0.3,
      diversificationBonus: 0.1
    },
    speedRun: {
      name: 'Speed Run',
      prioritizeType: ResourceType.WOOD,
      maxTravelDistance: 150,
      safetyWeight: 0.1,
      efficiencyWeight: 0.8,
      diversificationBonus: 0.1
    }
  };
  
  private currentStrategy: OptimizationStrategy = this.STRATEGIES.balanced;
  
  constructor(gameApi: SploopGameAPI, config: ModConfig) {
    super();
    this.gameApi = gameApi;
    this.config = config;
    this.pathfinding = new AdvancedPathfinding(2000, 2000); // Assuming map size
    
    this.gameApi.on('stateUpdate', this.onGameStateUpdate.bind(this));
  }
  
  public start(): void {
    if (this.enabled) return;
    
    this.enabled = true;
    this.updateInterval = window.setInterval(() => {
      this.update();
    }, this.config.updateInterval);
    
    this.emit('started');
    console.log('[ResourceOptimizer] Module started');
  }
  
  public stop(): void {
    if (!this.enabled) return;
    
    this.enabled = false;
    if (this.updateInterval) {
      clearInterval(this.updateInterval);
      this.updateInterval = null;
    }
    
    this.currentRoute = null;
    this.isGathering = false;
    
    this.emit('stopped');
    console.log('[ResourceOptimizer] Module stopped');
  }
  
  public updateConfig(config: ModConfig): void {
    this.config = config;
  }
  
  public setStrategy(strategyName: string): void {
    const strategy = this.STRATEGIES[strategyName];
    if (strategy) {
      this.currentStrategy = strategy;
      this.currentRoute = null; // Force re-optimization
      this.emit('strategyChanged', { strategy: strategyName });
      console.log(`[ResourceOptimizer] Strategy changed to: ${strategy.name}`);
    }
  }
  
  private onGameStateUpdate(): void {
    if (!this.enabled) return;
    
    this.scanForResourceNodes();
    this.updateNodeSafety();
  }
  
  private update(): void {
    if (!this.enabled || !this.gameApi.isInGame()) return;
    
    const now = Date.now();
    
    try {
      // Re-optimize route periodically
      if (now - this.lastOptimization > 30000) { // Every 30 seconds
        this.optimizeResourceRoute();
        this.lastOptimization = now;
      }
      
      // Execute current route
      this.executeRoute();
      
    } catch (error) {
      console.error('[ResourceOptimizer] Error in update:', error);
    }
  }
  
  private scanForResourceNodes(): void {
    // In a real implementation, this would scan the visible map for resource nodes
    // For now, we'll simulate some resource nodes
    const player = this.gameApi.getPlayer();
    if (!player) return;
    
    // Simulate discovering resource nodes around the player
    const simulatedNodes = this.generateSimulatedNodes(player.position);
    
    simulatedNodes.forEach(node => {
      if (!this.resourceNodes.has(node.id)) {
        this.resourceNodes.set(node.id, node);
        this.emit('nodeDiscovered', node);
      }
    });
    
    // Remove old nodes that haven't been visited recently
    const cutoffTime = Date.now() - 300000; // 5 minutes
    for (const [id, node] of this.resourceNodes.entries()) {
      if (node.lastVisited < cutoffTime) {
        this.resourceNodes.delete(id);
        this.emit('nodeExpired', { id });
      }
    }
  }
  
  private generateSimulatedNodes(playerPos: Vector2): ResourceNode[] {
    const nodes: ResourceNode[] = [];
    const nodeCount = 8;
    
    for (let i = 0; i < nodeCount; i++) {
      const angle = (i / nodeCount) * 2 * Math.PI;
      const distance = 100 + Math.random() * 200;
      const position = {
        x: playerPos.x + Math.cos(angle) * distance,
        y: playerPos.y + Math.sin(angle) * distance
      };
      
      const resourceTypes = [ResourceType.WOOD, ResourceType.STONE, ResourceType.FOOD, ResourceType.GOLD];
      const type = resourceTypes[Math.floor(Math.random() * resourceTypes.length)];
      
      nodes.push({
        id: `node_${position.x}_${position.y}`,
        type,
        position,
        abundance: 50 + Math.random() * 100,
        safety: 0.5 + Math.random() * 0.5,
        accessibility: 0.7 + Math.random() * 0.3,
        lastVisited: Date.now() - Math.random() * 60000,
        estimatedYield: 20 + Math.random() * 30
      });
    }
    
    return nodes;
  }
  
  private updateNodeSafety(): void {
    const players = this.gameApi.getPlayers();
    const player = this.gameApi.getPlayer();
    if (!player) return;
    
    // Update safety ratings based on enemy proximity
    for (const node of this.resourceNodes.values()) {
      let safety = 1.0;
      
      for (const otherPlayer of players.values()) {
        if (otherPlayer.id === player.id || otherPlayer.isBot) continue;
        
        const distance = this.gameApi.calculateDistance(node.position, otherPlayer.position);
        if (distance < 200) {
          safety *= Math.max(0.1, distance / 200);
        }
      }
      
      node.safety = safety;
    }
  }
  
  private optimizeResourceRoute(): void {
    const player = this.gameApi.getPlayer();
    if (!player) return;
    
    const availableNodes = Array.from(this.resourceNodes.values())
      .filter(node => {
        const distance = this.gameApi.calculateDistance(player.position, node.position);
        return distance <= this.currentStrategy.maxTravelDistance;
      });
    
    if (availableNodes.length === 0) {
      this.currentRoute = null;
      return;
    }
    
    // Score and sort nodes
    const scoredNodes = availableNodes.map(node => ({
      node,
      score: this.calculateNodeScore(node, player.position)
    })).sort((a, b) => b.score - a.score);
    
    // Create optimized route
    const routeNodes = this.createOptimalRoute(scoredNodes.slice(0, 6), player.position);
    
    if (routeNodes.length > 0) {
      this.currentRoute = {
        nodes: routeNodes,
        totalDistance: this.calculateRouteDistance(routeNodes, player.position),
        estimatedTime: this.estimateRouteTime(routeNodes),
        efficiency: this.calculateRouteEfficiency(routeNodes),
        safety: this.calculateRouteSafety(routeNodes)
      };
      
      this.currentNodeIndex = 0;
      this.emit('routeOptimized', this.currentRoute);
    }
  }
  
  private calculateNodeScore(node: ResourceNode, playerPos: Vector2): number {
    let score = 0;
    
    // Base yield score
    score += node.estimatedYield * node.abundance / 100;
    
    // Resource type priority
    if (this.currentStrategy.prioritizeType !== 'balanced') {
      if (node.type === this.currentStrategy.prioritizeType) {
        score *= 1.5;
      } else {
        score *= 0.7;
      }
    }
    
    // Distance penalty
    const distance = this.gameApi.calculateDistance(playerPos, node.position);
    const distancePenalty = Math.max(0, 1 - (distance / this.currentStrategy.maxTravelDistance));
    score *= distancePenalty;
    
    // Safety factor
    score *= (node.safety * this.currentStrategy.safetyWeight + 
              (1 - this.currentStrategy.safetyWeight));
    
    // Accessibility factor
    score *= node.accessibility;
    
    // Freshness bonus (recently discovered nodes might have more resources)
    const timeSinceVisit = Date.now() - node.lastVisited;
    const freshnessBonus = Math.min(1.2, 1 + (timeSinceVisit / 300000) * 0.2);
    score *= freshnessBonus;
    
    return score;
  }
  
  private createOptimalRoute(scoredNodes: { node: ResourceNode; score: number }[], startPos: Vector2): ResourceNode[] {
    if (scoredNodes.length === 0) return [];
    
    // Simple greedy approach with some optimization
    const route: ResourceNode[] = [];
    const remaining = [...scoredNodes];
    let currentPos = startPos;
    
    while (remaining.length > 0 && route.length < 5) {
      let bestIndex = 0;
      let bestScore = -Infinity;
      
      for (let i = 0; i < remaining.length; i++) {
        const node = remaining[i].node;
        const distance = this.gameApi.calculateDistance(currentPos, node.position);
        
        // Combine node score with travel efficiency
        const travelEfficiency = Math.max(0.1, 1 - (distance / 300));
        const combinedScore = remaining[i].score * travelEfficiency;
        
        if (combinedScore > bestScore) {
          bestScore = combinedScore;
          bestIndex = i;
        }
      }
      
      const selectedNode = remaining[bestIndex].node;
      route.push(selectedNode);
      currentPos = selectedNode.position;
      remaining.splice(bestIndex, 1);
    }
    
    return route;
  }
  
  private calculateRouteDistance(nodes: ResourceNode[], startPos: Vector2): number {
    let totalDistance = 0;
    let currentPos = startPos;
    
    for (const node of nodes) {
      totalDistance += this.gameApi.calculateDistance(currentPos, node.position);
      currentPos = node.position;
    }
    
    return totalDistance;
  }
  
  private estimateRouteTime(nodes: ResourceNode[]): number {
    // Estimate time based on travel + gathering
    const travelTime = this.calculateRouteDistance(nodes, { x: 0, y: 0 }) / 50; // Assuming 50 units/second
    const gatheringTime = nodes.length * 10; // 10 seconds per node
    return travelTime + gatheringTime;
  }
  
  private calculateRouteEfficiency(nodes: ResourceNode[]): number {
    const totalYield = nodes.reduce((sum, node) => sum + node.estimatedYield, 0);
    const totalTime = this.estimateRouteTime(nodes);
    return totalYield / Math.max(1, totalTime);
  }
  
  private calculateRouteSafety(nodes: ResourceNode[]): number {
    return nodes.reduce((sum, node) => sum + node.safety, 0) / Math.max(1, nodes.length);
  }
  
  private executeRoute(): void {
    if (!this.currentRoute || this.currentRoute.nodes.length === 0) return;
    
    const player = this.gameApi.getPlayer();
    if (!player) return;
    
    const currentNode = this.currentRoute.nodes[this.currentNodeIndex];
    if (!currentNode) return;
    
    const distance = this.gameApi.calculateDistance(player.position, currentNode.position);
    
    if (distance > 50) {
      // Move to node
      this.moveToNode(player.position, currentNode.position);
    } else {
      // Gather at node
      this.gatherAtNode(currentNode);
    }
  }
  
  private moveToNode(fromPos: Vector2, toPos: Vector2): void {
    // Use pathfinding for optimal movement
    const path = this.pathfinding.findPath(fromPos, toPos);
    
    if (path.length > 1) {
      const nextWaypoint = path[1]; // Skip current position
      
      this.gameApi.sendAction({
        type: ActionType.MOVE,
        data: { x: nextWaypoint.x, y: nextWaypoint.y },
        timestamp: Date.now()
      });
    }
  }
  
  private gatherAtNode(node: ResourceNode): void {
    if (!this.isGathering) {
      this.isGathering = true;
      this.gatheringStartTime = Date.now();
      this.emit('gatheringStarted', { node });
    }
    
    // Simulate gathering (in real implementation, this would interact with resource)
    const gatheringTime = 5000; // 5 seconds
    
    if (Date.now() - this.gatheringStartTime > gatheringTime) {
      // Finished gathering at this node
      this.isGathering = false;
      node.lastVisited = Date.now();
      node.abundance = Math.max(0, node.abundance - 20); // Reduce abundance
      
      this.emit('gatheringCompleted', { node, yield: node.estimatedYield });
      
      // Move to next node
      this.currentNodeIndex++;
      
      if (this.currentNodeIndex >= this.currentRoute.nodes.length) {
        // Route completed
        this.currentRoute = null;
        this.currentNodeIndex = 0;
        this.emit('routeCompleted');
      }
    }
  }
  
  public getCurrentRoute(): ResourceRoute | null {
    return this.currentRoute;
  }
  
  public getResourceNodes(): ResourceNode[] {
    return Array.from(this.resourceNodes.values());
  }
  
  public getAvailableStrategies(): Record<string, OptimizationStrategy> {
    return { ...this.STRATEGIES };
  }
  
  public getCurrentStrategy(): OptimizationStrategy {
    return this.currentStrategy;
  }
  
  public getStats(): any {
    return {
      enabled: this.enabled,
      currentStrategy: this.currentStrategy.name,
      knownNodes: this.resourceNodes.size,
      hasRoute: !!this.currentRoute,
      routeProgress: this.currentRoute ? `${this.currentNodeIndex}/${this.currentRoute.nodes.length}` : 'N/A',
      isGathering: this.isGathering,
      routeEfficiency: this.currentRoute?.efficiency || 0,
      routeSafety: this.currentRoute?.safety || 0
    };
  }
}
