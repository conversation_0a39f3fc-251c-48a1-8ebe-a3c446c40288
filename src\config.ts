import { HatType, ResourceType } from '@/types/game';

export interface ModConfig {
  // General settings
  enabled: boolean;
  debug: boolean;
  safeMode: boolean;
  updateInterval: number;
  
  // Auto Hat settings
  autoHats: {
    enabled: boolean;
    priority: HatType[];
    autoUpgrade: boolean;
    switchBasedOnSituation: boolean;
    combatHat: HatType;
    farmingHat: HatType;
    explorationHat: HatType;
  };
  
  // Auto Buy settings
  autoBuy: {
    enabled: boolean;
    priorityItems: string[];
    maxSpendPercentage: number;
    reserveResources: {
      wood: number;
      stone: number;
      food: number;
      gold: number;
    };
    buyHats: boolean;
    buyWeapons: boolean;
    buyUpgrades: boolean;
  };
  
  // Auto Mills settings
  autoMills: {
    enabled: boolean;
    maxMills: number;
    resourcePriority: ResourceType[];
    autoUpgrade: boolean;
    optimalPlacement: boolean;
    protectMills: boolean;
    millSpacing: number;
    preferredLocations: {
      nearResources: boolean;
      nearBase: boolean;
      hiddenAreas: boolean;
    };
  };
  
  // Bot System settings
  botSystem: {
    enabled: boolean;
    maxBots: number;
    botBehaviors: {
      farming: boolean;
      combat: boolean;
      building: boolean;
      exploration: boolean;
      protection: boolean;
    };
    coordination: {
      shareResources: boolean;
      groupMovement: boolean;
      defendTogether: boolean;
    };
    intelligence: {
      avoidPlayers: boolean;
      learnFromActions: boolean;
      adaptToSituation: boolean;
    };
  };
  
  // Safety settings
  safety: {
    antiDetection: boolean;
    randomizeActions: boolean;
    humanLikeMovement: boolean;
    pauseOnDanger: boolean;
    emergencyStop: string; // Key combination
    maxActionsPerSecond: number;
  };
  
  // UI settings
  ui: {
    showOverlay: boolean;
    showStats: boolean;
    showLogs: boolean;
    overlayPosition: 'top-left' | 'top-right' | 'bottom-left' | 'bottom-right';
    transparency: number;
  };
}

export const DEFAULT_CONFIG: ModConfig = {
  enabled: true,
  debug: false,
  safeMode: true,
  updateInterval: 100,
  
  autoHats: {
    enabled: true,
    priority: [
      HatType.ANGEL,
      HatType.DEVIL,
      HatType.SAMURAI,
      HatType.KNIGHT,
      HatType.CYBORG,
      HatType.CRYSTAL,
      HatType.BERSERKER,
      HatType.MARKSMAN
    ],
    autoUpgrade: true,
    switchBasedOnSituation: true,
    combatHat: HatType.BERSERKER,
    farmingHat: HatType.MONKEY,
    explorationHat: HatType.BUSH
  },
  
  autoBuy: {
    enabled: true,
    priorityItems: [
      'weapon_upgrade',
      'hat_upgrade',
      'health_potion',
      'speed_boost'
    ],
    maxSpendPercentage: 80,
    reserveResources: {
      wood: 100,
      stone: 100,
      food: 50,
      gold: 50
    },
    buyHats: true,
    buyWeapons: true,
    buyUpgrades: true
  },
  
  autoMills: {
    enabled: true,
    maxMills: 8,
    resourcePriority: [
      ResourceType.GOLD,
      ResourceType.STONE,
      ResourceType.WOOD,
      ResourceType.FOOD
    ],
    autoUpgrade: true,
    optimalPlacement: true,
    protectMills: true,
    millSpacing: 200,
    preferredLocations: {
      nearResources: true,
      nearBase: false,
      hiddenAreas: true
    }
  },
  
  botSystem: {
    enabled: false, // Disabled by default for safety
    maxBots: 3,
    botBehaviors: {
      farming: true,
      combat: false,
      building: true,
      exploration: true,
      protection: true
    },
    coordination: {
      shareResources: true,
      groupMovement: false,
      defendTogether: true
    },
    intelligence: {
      avoidPlayers: true,
      learnFromActions: true,
      adaptToSituation: true
    }
  },
  
  safety: {
    antiDetection: true,
    randomizeActions: true,
    humanLikeMovement: true,
    pauseOnDanger: true,
    emergencyStop: 'Ctrl+Shift+X',
    maxActionsPerSecond: 5
  },
  
  ui: {
    showOverlay: true,
    showStats: true,
    showLogs: false,
    overlayPosition: 'top-right',
    transparency: 0.8
  }
};

export class ConfigManager {
  private config: ModConfig;
  private readonly STORAGE_KEY = 'sploop_advanced_mod_config';
  
  constructor() {
    this.config = this.loadConfig();
  }
  
  public getConfig(): ModConfig {
    return { ...this.config };
  }
  
  public updateConfig(updates: Partial<ModConfig>): void {
    this.config = { ...this.config, ...updates };
    this.saveConfig();
  }
  
  public resetConfig(): void {
    this.config = { ...DEFAULT_CONFIG };
    this.saveConfig();
  }
  
  private loadConfig(): ModConfig {
    try {
      const stored = localStorage.getItem(this.STORAGE_KEY);
      if (stored) {
        const parsed = JSON.parse(stored);
        return { ...DEFAULT_CONFIG, ...parsed };
      }
    } catch (error) {
      console.warn('Failed to load config, using defaults:', error);
    }
    return { ...DEFAULT_CONFIG };
  }
  
  private saveConfig(): void {
    try {
      localStorage.setItem(this.STORAGE_KEY, JSON.stringify(this.config));
    } catch (error) {
      console.error('Failed to save config:', error);
    }
  }
}
