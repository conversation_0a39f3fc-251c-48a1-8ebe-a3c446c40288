const fs = require('fs');
const path = require('path');

// Read the built file
const distPath = path.join(__dirname, '../dist/sploop-advanced-mod.user.js');
const outputPath = path.join(__dirname, '../dist/sploop-advanced-mod-final.user.js');

if (!fs.existsSync(distPath)) {
  console.error('Built file not found. Run "npm run build" first.');
  process.exit(1);
}

let content = fs.readFileSync(distPath, 'utf8');

// Add userscript header if not present
const userscriptHeader = `// ==UserScript==
// @name         Sploop.io Advanced Mod
// @namespace    http://tampermonkey.net/
// @version      1.0.0
// @description  Advanced sploop.io mod with auto hats, auto buy, auto mills, and bot system
// <AUTHOR> Mod Developer
// @match        *://sploop.io/*
// @match        *://*.sploop.io/*
// @grant        none
// @run-at       document-start
// @updateURL    https://raw.githubusercontent.com/your-repo/sploop-advanced-mod/main/dist/sploop-advanced-mod-final.user.js
// @downloadURL  https://raw.githubusercontent.com/your-repo/sploop-advanced-mod/main/dist/sploop-advanced-mod-final.user.js
// ==/UserScript==

(function() {
'use strict';

`;

const userscriptFooter = `
})();`;

// Check if header already exists
if (!content.includes('==UserScript==')) {
  content = userscriptHeader + content + userscriptFooter;
}

// Write the final userscript
fs.writeFileSync(outputPath, content);

console.log('✅ Userscript built successfully!');
console.log(`📁 Output: ${outputPath}`);
console.log('📋 Copy the contents of this file to your userscript manager.');
