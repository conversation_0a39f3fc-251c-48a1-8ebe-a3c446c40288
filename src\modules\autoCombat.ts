import { SploopGameAPI } from '@/utils/gameApi';
import { ModConfig } from '@/config';
import { Player, Vector2, ActionType } from '@/types/game';
import { EventEmitter } from 'eventemitter3';

export interface CombatTarget {
  player: Player;
  priority: number;
  distance: number;
  threat: number;
  lastSeen: number;
}

export interface CombatStrategy {
  name: string;
  description: string;
  aggressive: boolean;
  targetPriority: 'closest' | 'weakest' | 'strongest' | 'threat';
  engageDistance: number;
  retreatDistance: number;
  useRangedWeapons: boolean;
  kiteEnemies: boolean;
  groupFighting: boolean;
}

export class AutoCombatModule extends EventEmitter {
  private gameApi: SploopGameAPI;
  private config: ModConfig;
  private enabled = false;
  private updateInterval: number | null = null;
  private lastCombatAction = 0;
  private currentTarget: CombatTarget | null = null;
  private combatMode: 'defensive' | 'aggressive' | 'passive' = 'defensive';
  private lastHealthCheck = 0;
  private retreating = false;
  
  // Combat strategies
  private readonly STRATEGIES: Record<string, CombatStrategy> = {
    defensive: {
      name: 'Defensive',
      description: 'Only fight when attacked, prioritize survival',
      aggressive: false,
      targetPriority: 'closest',
      engageDistance: 100,
      retreatDistance: 150,
      useRangedWeapons: true,
      kiteEnemies: true,
      groupFighting: false
    },
    aggressive: {
      name: 'Aggressive',
      description: 'Actively hunt enemies, high risk/reward',
      aggressive: true,
      targetPriority: 'weakest',
      engageDistance: 200,
      retreatDistance: 80,
      useRangedWeapons: false,
      kiteEnemies: false,
      groupFighting: true
    },
    balanced: {
      name: 'Balanced',
      description: 'Moderate aggression with tactical retreats',
      aggressive: true,
      targetPriority: 'threat',
      engageDistance: 150,
      retreatDistance: 120,
      useRangedWeapons: true,
      kiteEnemies: true,
      groupFighting: true
    },
    sniper: {
      name: 'Sniper',
      description: 'Long-range combat, avoid close encounters',
      aggressive: false,
      targetPriority: 'closest',
      engageDistance: 250,
      retreatDistance: 200,
      useRangedWeapons: true,
      kiteEnemies: true,
      groupFighting: false
    }
  };
  
  private currentStrategy: CombatStrategy = this.STRATEGIES.defensive;
  
  constructor(gameApi: SploopGameAPI, config: ModConfig) {
    super();
    this.gameApi = gameApi;
    this.config = config;
    
    this.gameApi.on('stateUpdate', this.onGameStateUpdate.bind(this));
  }
  
  public start(): void {
    if (this.enabled) return;
    
    this.enabled = true;
    this.updateInterval = window.setInterval(() => {
      this.update();
    }, this.config.updateInterval);
    
    this.emit('started');
    console.log('[AutoCombat] Module started');
  }
  
  public stop(): void {
    if (!this.enabled) return;
    
    this.enabled = false;
    if (this.updateInterval) {
      clearInterval(this.updateInterval);
      this.updateInterval = null;
    }
    
    this.currentTarget = null;
    this.retreating = false;
    
    this.emit('stopped');
    console.log('[AutoCombat] Module stopped');
  }
  
  public updateConfig(config: ModConfig): void {
    this.config = config;
  }
  
  public setStrategy(strategyName: string): void {
    const strategy = this.STRATEGIES[strategyName];
    if (strategy) {
      this.currentStrategy = strategy;
      this.emit('strategyChanged', { strategy: strategyName });
      console.log(`[AutoCombat] Strategy changed to: ${strategy.name}`);
    }
  }
  
  public setCombatMode(mode: 'defensive' | 'aggressive' | 'passive'): void {
    this.combatMode = mode;
    this.emit('modeChanged', { mode });
    console.log(`[AutoCombat] Combat mode changed to: ${mode}`);
  }
  
  private onGameStateUpdate(): void {
    if (!this.enabled) return;
    
    this.updateHealthStatus();
    this.scanForThreats();
  }
  
  private update(): void {
    if (!this.enabled || !this.gameApi.isInGame()) return;
    
    const now = Date.now();
    if (now - this.lastCombatAction < 200) return; // Rate limiting
    
    try {
      this.processCombat();
    } catch (error) {
      console.error('[AutoCombat] Error in update:', error);
    }
  }
  
  private updateHealthStatus(): void {
    const player = this.gameApi.getPlayer();
    if (!player) return;
    
    const healthPercentage = player.health / player.maxHealth;
    
    // Auto-retreat if health is low
    if (healthPercentage < 0.3 && !this.retreating) {
      this.initiateRetreat();
    } else if (healthPercentage > 0.7 && this.retreating) {
      this.retreating = false;
      this.emit('retreatEnded');
    }
  }
  
  private scanForThreats(): void {
    const player = this.gameApi.getPlayer();
    const players = this.gameApi.getPlayers();
    
    if (!player) return;
    
    const threats: CombatTarget[] = [];
    
    players.forEach(otherPlayer => {
      if (otherPlayer.id === player.id || otherPlayer.isBot) return;
      
      const distance = this.gameApi.calculateDistance(player.position, otherPlayer.position);
      
      // Only consider players within detection range
      if (distance <= 300) {
        const threat = this.calculateThreatLevel(otherPlayer, distance);
        const priority = this.calculateTargetPriority(otherPlayer, distance, threat);
        
        threats.push({
          player: otherPlayer,
          priority,
          distance,
          threat,
          lastSeen: Date.now()
        });
      }
    });
    
    // Update current target based on strategy
    this.updateTarget(threats);
  }
  
  private calculateThreatLevel(enemy: Player, distance: number): number {
    let threat = 0;
    
    // Distance factor (closer = more threatening)
    threat += Math.max(0, 100 - distance);
    
    // Health factor (higher health = more threatening)
    threat += enemy.health / 2;
    
    // Level factor
    threat += enemy.level * 5;
    
    // Weapon factor (if we can detect it)
    if (enemy.weapon) {
      threat += enemy.weapon.damage || 0;
    }
    
    return threat;
  }
  
  private calculateTargetPriority(enemy: Player, distance: number, threat: number): number {
    let priority = 0;
    
    switch (this.currentStrategy.targetPriority) {
      case 'closest':
        priority = 300 - distance;
        break;
      case 'weakest':
        priority = 200 - enemy.health;
        break;
      case 'strongest':
        priority = enemy.health + (enemy.level * 10);
        break;
      case 'threat':
        priority = threat;
        break;
    }
    
    return priority;
  }
  
  private updateTarget(threats: CombatTarget[]): void {
    if (threats.length === 0) {
      this.currentTarget = null;
      return;
    }
    
    // Sort by priority
    threats.sort((a, b) => b.priority - a.priority);
    
    // Select target based on strategy
    const potentialTarget = threats[0];
    
    // Only engage if within engagement distance or already engaged
    if (potentialTarget.distance <= this.currentStrategy.engageDistance || 
        (this.currentTarget && potentialTarget.player.id === this.currentTarget.player.id)) {
      this.currentTarget = potentialTarget;
    } else if (this.currentTarget && potentialTarget.distance > this.currentStrategy.retreatDistance) {
      this.currentTarget = null;
    }
  }
  
  private processCombat(): void {
    const player = this.gameApi.getPlayer();
    if (!player) return;
    
    // Handle different combat modes
    switch (this.combatMode) {
      case 'passive':
        return; // Do nothing
      case 'defensive':
        this.processDefensiveCombat(player);
        break;
      case 'aggressive':
        this.processAggressiveCombat(player);
        break;
    }
  }
  
  private processDefensiveCombat(player: Player): void {
    if (!this.currentTarget) return;
    
    const distance = this.gameApi.calculateDistance(player.position, this.currentTarget.player.position);
    
    // Retreat if too close and strategy suggests it
    if (distance < this.currentStrategy.retreatDistance && this.currentStrategy.kiteEnemies) {
      this.executeRetreat(player, this.currentTarget.player.position);
      return;
    }
    
    // Attack if in range
    if (distance <= this.currentStrategy.engageDistance) {
      this.executeAttack(player, this.currentTarget.player.position);
    }
  }
  
  private processAggressiveCombat(player: Player): void {
    if (!this.currentTarget) return;
    
    const distance = this.gameApi.calculateDistance(player.position, this.currentTarget.player.position);
    
    // Move closer if too far
    if (distance > this.currentStrategy.engageDistance) {
      this.moveTowards(player.position, this.currentTarget.player.position);
    } else {
      // Attack
      this.executeAttack(player, this.currentTarget.player.position);
      
      // Kite if strategy suggests it
      if (this.currentStrategy.kiteEnemies && distance < 80) {
        this.executeRetreat(player, this.currentTarget.player.position);
      }
    }
  }
  
  private executeAttack(playerPos: Vector2, targetPos: Vector2): void {
    const angle = this.gameApi.calculateAngle(playerPos, targetPos);
    
    this.gameApi.sendAction({
      type: ActionType.ATTACK,
      data: { angle },
      timestamp: Date.now()
    });
    
    this.lastCombatAction = Date.now();
    this.emit('attacked', { target: this.currentTarget?.player.id, angle });
  }
  
  private executeRetreat(playerPos: Vector2, threatPos: Vector2): void {
    // Move away from threat
    const angle = this.gameApi.calculateAngle(threatPos, playerPos);
    const retreatDistance = 50;
    
    const newX = playerPos.x + Math.cos(angle) * retreatDistance;
    const newY = playerPos.y + Math.sin(angle) * retreatDistance;
    
    this.gameApi.sendAction({
      type: ActionType.MOVE,
      data: { x: newX, y: newY },
      timestamp: Date.now()
    });
    
    this.lastCombatAction = Date.now();
    this.emit('retreated', { from: threatPos, to: { x: newX, y: newY } });
  }
  
  private moveTowards(fromPos: Vector2, toPos: Vector2): void {
    const angle = this.gameApi.calculateAngle(fromPos, toPos);
    const moveDistance = 30;
    
    const newX = fromPos.x + Math.cos(angle) * moveDistance;
    const newY = fromPos.y + Math.sin(angle) * moveDistance;
    
    this.gameApi.sendAction({
      type: ActionType.MOVE,
      data: { x: newX, y: newY },
      timestamp: Date.now()
    });
    
    this.lastCombatAction = Date.now();
  }
  
  private initiateRetreat(): void {
    this.retreating = true;
    this.currentTarget = null;
    this.emit('retreatStarted');
    console.log('[AutoCombat] Initiating emergency retreat due to low health');
  }
  
  public getCurrentTarget(): CombatTarget | null {
    return this.currentTarget;
  }
  
  public getCurrentStrategy(): CombatStrategy {
    return this.currentStrategy;
  }
  
  public getAvailableStrategies(): Record<string, CombatStrategy> {
    return { ...this.STRATEGIES };
  }
  
  public getStats(): any {
    return {
      enabled: this.enabled,
      combatMode: this.combatMode,
      currentStrategy: this.currentStrategy.name,
      hasTarget: !!this.currentTarget,
      targetId: this.currentTarget?.player.id || null,
      retreating: this.retreating,
      lastAction: this.lastCombatAction
    };
  }
}
