import { SploopG<PERSON>API } from '@/utils/gameApi';
import { ModConfig } from '@/config';
import { HatType } from '@/types/game';
import { EventEmitter } from 'eventemitter3';
export interface HatInfo {
    id: string;
    type: HatType;
    name: string;
    cost: number;
    unlockLevel: number;
    owned: boolean;
    equipped: boolean;
    stats: {
        healthBonus: number;
        speedBonus: number;
        damageBonus: number;
        resourceBonus: number;
        experienceBonus: number;
    };
}
export declare class AutoHatsModule extends EventEmitter {
    private gameApi;
    private config;
    private enabled;
    private updateInterval;
    private lastHatCheck;
    private currentSituation;
    private readonly HAT_DATABASE;
    constructor(gameApi: SploopGameAPI, config: ModConfig);
    start(): void;
    stop(): void;
    updateConfig(config: ModConfig): void;
    private onGameStateUpdate;
    private update;
    private updateHatOwnership;
    private isHatOwned;
    private isHatEquipped;
    private analyzeSituation;
    private processAutoHats;
    private getOptimalHatForSituation;
    private getBestOwnedHat;
    private getBestAvailableHat;
    private tryBuyHat;
    private equipHat;
    getHatInfo(hatType: HatType): HatInfo;
    getAllHats(): HatInfo[];
    getCurrentSituation(): string;
    getStats(): any;
}
//# sourceMappingURL=autoHats.d.ts.map