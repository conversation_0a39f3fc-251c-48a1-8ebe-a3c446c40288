import { Vector2 } from '@/types/game';

export interface PathNode {
  x: number;
  y: number;
  g: number; // Cost from start
  h: number; // Heuristic cost to goal
  f: number; // Total cost (g + h)
  parent: PathNode | null;
  walkable: boolean;
}

export interface PathfindingOptions {
  allowDiagonal: boolean;
  heuristicWeight: number;
  maxIterations: number;
  nodeSize: number;
  avoidObstacles: boolean;
  avoidEnemies: boolean;
  enemyAvoidanceRadius: number;
}

export class AdvancedPathfinding {
  private grid: PathNode[][] = [];
  private gridWidth = 0;
  private gridHeight = 0;
  private nodeSize = 20;
  private obstacles: Set<string> = new Set();
  private enemyPositions: Vector2[] = [];
  
  constructor(
    private mapWidth: number,
    private mapHeight: number,
    private options: PathfindingOptions = {
      allowDiagonal: true,
      heuristicWeight: 1.0,
      maxIterations: 1000,
      nodeSize: 20,
      avoidObstacles: true,
      avoidEnemies: true,
      enemyAvoidanceRadius: 100
    }
  ) {
    this.nodeSize = options.nodeSize;
    this.initializeGrid();
  }
  
  private initializeGrid(): void {
    this.gridWidth = Math.ceil(this.mapWidth / this.nodeSize);
    this.gridHeight = Math.ceil(this.mapHeight / this.nodeSize);
    
    this.grid = [];
    for (let x = 0; x < this.gridWidth; x++) {
      this.grid[x] = [];
      for (let y = 0; y < this.gridHeight; y++) {
        this.grid[x][y] = {
          x,
          y,
          g: 0,
          h: 0,
          f: 0,
          parent: null,
          walkable: true
        };
      }
    }
  }
  
  public updateObstacles(obstacles: Vector2[]): void {
    this.obstacles.clear();
    
    obstacles.forEach(obstacle => {
      const gridX = Math.floor(obstacle.x / this.nodeSize);
      const gridY = Math.floor(obstacle.y / this.nodeSize);
      
      // Mark obstacle and surrounding area as unwalkable
      for (let dx = -1; dx <= 1; dx++) {
        for (let dy = -1; dy <= 1; dy++) {
          const x = gridX + dx;
          const y = gridY + dy;
          
          if (this.isValidGridPosition(x, y)) {
            this.obstacles.add(`${x},${y}`);
            this.grid[x][y].walkable = false;
          }
        }
      }
    });
  }
  
  public updateEnemyPositions(enemies: Vector2[]): void {
    this.enemyPositions = [...enemies];
  }
  
  public findPath(start: Vector2, goal: Vector2): Vector2[] {
    const startNode = this.worldToGrid(start);
    const goalNode = this.worldToGrid(goal);
    
    if (!this.isValidGridPosition(startNode.x, startNode.y) || 
        !this.isValidGridPosition(goalNode.x, goalNode.y)) {
      return [];
    }
    
    // Reset grid
    this.resetGrid();
    
    const openList: PathNode[] = [];
    const closedList: Set<string> = new Set();
    
    const startGridNode = this.grid[startNode.x][startNode.y];
    startGridNode.g = 0;
    startGridNode.h = this.calculateHeuristic(startGridNode, goalNode);
    startGridNode.f = startGridNode.g + startGridNode.h;
    
    openList.push(startGridNode);
    
    let iterations = 0;
    
    while (openList.length > 0 && iterations < this.options.maxIterations) {
      iterations++;
      
      // Find node with lowest f cost
      openList.sort((a, b) => a.f - b.f);
      const currentNode = openList.shift()!;
      
      const currentKey = `${currentNode.x},${currentNode.y}`;
      closedList.add(currentKey);
      
      // Check if we reached the goal
      if (currentNode.x === goalNode.x && currentNode.y === goalNode.y) {
        return this.reconstructPath(currentNode);
      }
      
      // Check neighbors
      const neighbors = this.getNeighbors(currentNode);
      
      for (const neighbor of neighbors) {
        const neighborKey = `${neighbor.x},${neighbor.y}`;
        
        if (closedList.has(neighborKey) || !neighbor.walkable) {
          continue;
        }
        
        // Calculate costs
        const tentativeG = currentNode.g + this.calculateDistance(currentNode, neighbor);
        
        // Add enemy avoidance cost
        if (this.options.avoidEnemies) {
          tentativeG += this.calculateEnemyAvoidanceCost(neighbor);
        }
        
        const existingInOpen = openList.find(n => n.x === neighbor.x && n.y === neighbor.y);
        
        if (!existingInOpen) {
          neighbor.g = tentativeG;
          neighbor.h = this.calculateHeuristic(neighbor, goalNode);
          neighbor.f = neighbor.g + neighbor.h * this.options.heuristicWeight;
          neighbor.parent = currentNode;
          openList.push(neighbor);
        } else if (tentativeG < existingInOpen.g) {
          existingInOpen.g = tentativeG;
          existingInOpen.f = existingInOpen.g + existingInOpen.h * this.options.heuristicWeight;
          existingInOpen.parent = currentNode;
        }
      }
    }
    
    // No path found, return direct line or empty array
    return this.createDirectPath(start, goal);
  }
  
  private getNeighbors(node: PathNode): PathNode[] {
    const neighbors: PathNode[] = [];
    const directions = this.options.allowDiagonal 
      ? [[-1,-1], [-1,0], [-1,1], [0,-1], [0,1], [1,-1], [1,0], [1,1]]
      : [[-1,0], [0,-1], [0,1], [1,0]];
    
    for (const [dx, dy] of directions) {
      const x = node.x + dx;
      const y = node.y + dy;
      
      if (this.isValidGridPosition(x, y)) {
        neighbors.push(this.grid[x][y]);
      }
    }
    
    return neighbors;
  }
  
  private calculateHeuristic(node: PathNode, goal: Vector2): number {
    // Manhattan distance for grid-based pathfinding
    const dx = Math.abs(node.x - goal.x);
    const dy = Math.abs(node.y - goal.y);
    
    if (this.options.allowDiagonal) {
      // Diagonal distance
      return Math.sqrt(dx * dx + dy * dy) * this.nodeSize;
    } else {
      // Manhattan distance
      return (dx + dy) * this.nodeSize;
    }
  }
  
  private calculateDistance(nodeA: PathNode, nodeB: PathNode): number {
    const dx = Math.abs(nodeA.x - nodeB.x);
    const dy = Math.abs(nodeA.y - nodeB.y);
    
    if (dx === 1 && dy === 1) {
      // Diagonal movement
      return this.nodeSize * 1.414; // sqrt(2)
    } else {
      // Straight movement
      return this.nodeSize;
    }
  }
  
  private calculateEnemyAvoidanceCost(node: PathNode): number {
    let cost = 0;
    const worldPos = this.gridToWorld(node);
    
    for (const enemy of this.enemyPositions) {
      const distance = Math.sqrt(
        Math.pow(worldPos.x - enemy.x, 2) + 
        Math.pow(worldPos.y - enemy.y, 2)
      );
      
      if (distance < this.options.enemyAvoidanceRadius) {
        // Higher cost for being closer to enemies
        const avoidanceFactor = 1 - (distance / this.options.enemyAvoidanceRadius);
        cost += avoidanceFactor * 100;
      }
    }
    
    return cost;
  }
  
  private reconstructPath(goalNode: PathNode): Vector2[] {
    const path: Vector2[] = [];
    let currentNode: PathNode | null = goalNode;
    
    while (currentNode) {
      const worldPos = this.gridToWorld(currentNode);
      path.unshift(worldPos);
      currentNode = currentNode.parent;
    }
    
    // Smooth the path
    return this.smoothPath(path);
  }
  
  private smoothPath(path: Vector2[]): Vector2[] {
    if (path.length <= 2) return path;
    
    const smoothed: Vector2[] = [path[0]];
    
    for (let i = 1; i < path.length - 1; i++) {
      const prev = path[i - 1];
      const current = path[i];
      const next = path[i + 1];
      
      // Check if we can skip the current point
      if (!this.hasObstacleBetween(prev, next)) {
        continue; // Skip this point
      }
      
      smoothed.push(current);
    }
    
    smoothed.push(path[path.length - 1]);
    return smoothed;
  }
  
  private hasObstacleBetween(start: Vector2, end: Vector2): boolean {
    const steps = Math.max(
      Math.abs(end.x - start.x),
      Math.abs(end.y - start.y)
    ) / this.nodeSize;
    
    for (let i = 0; i <= steps; i++) {
      const t = i / steps;
      const x = start.x + (end.x - start.x) * t;
      const y = start.y + (end.y - start.y) * t;
      
      const gridPos = this.worldToGrid({ x, y });
      if (this.obstacles.has(`${gridPos.x},${gridPos.y}`)) {
        return true;
      }
    }
    
    return false;
  }
  
  private createDirectPath(start: Vector2, goal: Vector2): Vector2[] {
    // If no path found, create a simple direct path with waypoints
    const distance = Math.sqrt(
      Math.pow(goal.x - start.x, 2) + 
      Math.pow(goal.y - start.y, 2)
    );
    
    const steps = Math.ceil(distance / (this.nodeSize * 2));
    const path: Vector2[] = [];
    
    for (let i = 0; i <= steps; i++) {
      const t = i / steps;
      const x = start.x + (goal.x - start.x) * t;
      const y = start.y + (goal.y - start.y) * t;
      path.push({ x, y });
    }
    
    return path;
  }
  
  private worldToGrid(worldPos: Vector2): Vector2 {
    return {
      x: Math.floor(worldPos.x / this.nodeSize),
      y: Math.floor(worldPos.y / this.nodeSize)
    };
  }
  
  private gridToWorld(gridPos: PathNode): Vector2 {
    return {
      x: gridPos.x * this.nodeSize + this.nodeSize / 2,
      y: gridPos.y * this.nodeSize + this.nodeSize / 2
    };
  }
  
  private isValidGridPosition(x: number, y: number): boolean {
    return x >= 0 && x < this.gridWidth && y >= 0 && y < this.gridHeight;
  }
  
  private resetGrid(): void {
    for (let x = 0; x < this.gridWidth; x++) {
      for (let y = 0; y < this.gridHeight; y++) {
        const node = this.grid[x][y];
        node.g = 0;
        node.h = 0;
        node.f = 0;
        node.parent = null;
        node.walkable = !this.obstacles.has(`${x},${y}`);
      }
    }
  }
  
  // Utility methods
  public getOptimalPosition(currentPos: Vector2, targets: Vector2[], avoidPositions: Vector2[] = []): Vector2 {
    let bestPosition = currentPos;
    let bestScore = -Infinity;
    
    // Generate candidate positions in a circle around current position
    const radius = 100;
    const candidates = 16;
    
    for (let i = 0; i < candidates; i++) {
      const angle = (i / candidates) * 2 * Math.PI;
      const candidate = {
        x: currentPos.x + Math.cos(angle) * radius,
        y: currentPos.y + Math.sin(angle) * radius
      };
      
      let score = 0;
      
      // Score based on distance to targets (closer is better)
      for (const target of targets) {
        const distance = Math.sqrt(
          Math.pow(candidate.x - target.x, 2) + 
          Math.pow(candidate.y - target.y, 2)
        );
        score += Math.max(0, 200 - distance);
      }
      
      // Penalty for being close to avoid positions
      for (const avoid of avoidPositions) {
        const distance = Math.sqrt(
          Math.pow(candidate.x - avoid.x, 2) + 
          Math.pow(candidate.y - avoid.y, 2)
        );
        if (distance < 150) {
          score -= (150 - distance) * 2;
        }
      }
      
      // Check if position is walkable
      const gridPos = this.worldToGrid(candidate);
      if (this.obstacles.has(`${gridPos.x},${gridPos.y}`)) {
        score -= 1000;
      }
      
      if (score > bestScore) {
        bestScore = score;
        bestPosition = candidate;
      }
    }
    
    return bestPosition;
  }
  
  public findSafePosition(currentPos: Vector2, threats: Vector2[], minDistance = 200): Vector2 {
    return this.getOptimalPosition(currentPos, [], threats);
  }
}
