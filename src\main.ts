// ==UserScript==
// @name         Sploop.io Advanced Mod
// @namespace    http://tampermonkey.net/
// @version      1.0.0
// @description  Advanced sploop.io mod with auto hats, auto buy, auto mills, and bot system
// <AUTHOR> Mod Developer
// @match        *://sploop.io/*
// @match        *://*.sploop.io/*
// @grant        none
// @run-at       document-start
// ==/UserScript==

import { SploopGameAPI } from './utils/gameApi';
import { ConfigManager, ModConfig } from './config';
import { AutoHatsModule } from './modules/autoHats';
import { AutoBuyModule } from './modules/autoBuy';
import { AutoMillsModule } from './modules/autoMills';
import { BotSystemModule } from './modules/botSystem';
import { AutoCombatModule } from './modules/autoCombat';
import { ResourceOptimizerModule } from './modules/resourceOptimizer';
import { ModMenuSystem } from './ui/modMenu';
import { EventEmitter } from 'eventemitter3';

interface ModuleStats {
  autoHats: any;
  autoBuy: any;
  autoMills: any;
  botSystem: any;
  autoCombat: any;
  resourceOptimizer: any;
}

class SploopAdvancedMod extends EventEmitter {
  private gameApi: SploopGameAPI;
  private configManager: ConfigManager;
  private autoHats: AutoHatsModule;
  private autoBuy: AutoBuyModule;
  private autoMills: AutoMillsModule;
  private botSystem: BotSystemModule;
  private autoCombat: AutoCombatModule;
  private resourceOptimizer: ResourceOptimizerModule;
  private modMenu: ModMenuSystem;

  private ui: HTMLElement | null = null;
  private isInitialized = false;
  private emergencyStopActive = false;
  
  constructor() {
    super();
    
    console.log('[SploopAdvancedMod] Initializing...');
    
    // Initialize core components
    this.configManager = new ConfigManager();
    this.gameApi = new SploopGameAPI();
    
    // Initialize modules
    const config = this.configManager.getConfig();
    this.autoHats = new AutoHatsModule(this.gameApi, config);
    this.autoBuy = new AutoBuyModule(this.gameApi, config);
    this.autoMills = new AutoMillsModule(this.gameApi, config);
    this.botSystem = new BotSystemModule(this.gameApi, config);
    this.autoCombat = new AutoCombatModule(this.gameApi, config);
    this.resourceOptimizer = new ResourceOptimizerModule(this.gameApi, config);
    this.modMenu = new ModMenuSystem(config);
    
    this.setupEventListeners();
    this.setupEmergencyStop();
    this.initialize();
  }
  
  private async initialize(): Promise<void> {
    try {
      // Wait for game to load
      await this.waitForGameLoad();
      
      // Create UI
      this.createUI();
      
      // Start modules based on config
      const config = this.configManager.getConfig();
      this.startModules(config);
      
      this.isInitialized = true;
      this.emit('initialized');
      
      console.log('[SploopAdvancedMod] Successfully initialized!');
      
    } catch (error) {
      console.error('[SploopAdvancedMod] Initialization failed:', error);
    }
  }
  
  private waitForGameLoad(): Promise<void> {
    return new Promise((resolve) => {
      const checkGame = () => {
        if (this.gameApi.isInGame()) {
          resolve();
        } else {
          setTimeout(checkGame, 1000);
        }
      };
      checkGame();
    });
  }
  
  private setupEventListeners(): void {
    // Game API events
    this.gameApi.on('gameLoaded', () => {
      console.log('[SploopAdvancedMod] Game loaded');
    });
    
    this.gameApi.on('stateUpdate', (gameState) => {
      this.updateUI();
    });
    
    // Module events
    this.autoHats.on('hatEquipped', (data) => {
      this.logAction('Auto Hats', `Equipped ${data.hatType}`);
    });
    
    this.autoHats.on('hatPurchased', (data) => {
      this.logAction('Auto Hats', `Purchased ${data.hatType} for ${data.cost} gold`);
    });
    
    this.autoBuy.on('itemPurchased', (data) => {
      this.logAction('Auto Buy', `Purchased ${data.itemName}`);
    });
    
    this.autoMills.on('millPlaced', (data) => {
      this.logAction('Auto Mills', `Placed ${data.resourceType} mill`);
    });
    
    this.autoMills.on('millUpgraded', (data) => {
      this.logAction('Auto Mills', `Upgraded mill to level ${data.level + 1}`);
    });
    
    this.botSystem.on('botSpawned', (data) => {
      this.logAction('Bot System', `Spawned bot ${data.name}`);
    });
    
    this.botSystem.on('botAction', (data) => {
      this.logAction('Bot System', `Bot ${data.botId} performed ${data.action}`);
    });

    // Auto Combat events
    this.autoCombat.on('attacked', (data) => {
      this.logAction('Auto Combat', `Attacked target ${data.target}`);
    });

    this.autoCombat.on('retreated', (data) => {
      this.logAction('Auto Combat', 'Tactical retreat executed');
    });

    // Resource Optimizer events
    this.resourceOptimizer.on('routeOptimized', (data) => {
      this.logAction('Resource Optimizer', `New route with ${data.nodes.length} nodes`);
    });

    this.resourceOptimizer.on('gatheringCompleted', (data) => {
      this.logAction('Resource Optimizer', `Gathered ${data.yield} from ${data.node.type} node`);
    });
  }
  
  private setupEmergencyStop(): void {
    document.addEventListener('keydown', (event) => {
      const config = this.configManager.getConfig();

      // Emergency stop
      if (this.checkHotkey(event, config.safety.emergencyStop)) {
        this.emergencyStop();
        return;
      }

      // Quick toggle hotkeys
      if (this.checkHotkey(event, 'Ctrl+1')) {
        this.toggleModule('autoHats');
        this.showNotification('Auto Hats Toggled', 'info');
      } else if (this.checkHotkey(event, 'Ctrl+2')) {
        this.toggleModule('autoBuy');
        this.showNotification('Auto Buy Toggled', 'info');
      } else if (this.checkHotkey(event, 'Ctrl+3')) {
        this.toggleModule('autoMills');
        this.showNotification('Auto Mills Toggled', 'info');
      } else if (this.checkHotkey(event, 'Ctrl+4')) {
        this.toggleModule('botSystem');
        this.showNotification('Bot System Toggled', 'info');
      } else if (this.checkHotkey(event, 'Ctrl+5')) {
        this.toggleAutoCombat();
        this.showNotification('Auto Combat Toggled', 'info');
      } else if (this.checkHotkey(event, 'Ctrl+6')) {
        this.toggleResourceOptimizer();
        this.showNotification('Resource Optimizer Toggled', 'info');
      } else if (this.checkHotkey(event, 'Ctrl+M')) {
        this.toggleModMenu();
      }
    });
  }

  private checkHotkey(event: KeyboardEvent, hotkey: string): boolean {
    const keys = hotkey.split('+');
    let matches = true;

    for (const key of keys) {
      switch (key.trim()) {
        case 'Ctrl':
          if (!event.ctrlKey) matches = false;
          break;
        case 'Shift':
          if (!event.shiftKey) matches = false;
          break;
        case 'Alt':
          if (!event.altKey) matches = false;
          break;
        default:
          if (event.key !== key) matches = false;
      }
    }

    return matches;
  }
  
  private emergencyStop(): void {
    if (this.emergencyStopActive) return;
    
    this.emergencyStopActive = true;
    
    console.log('[SploopAdvancedMod] EMERGENCY STOP ACTIVATED!');
    
    // Stop all modules
    this.autoHats.stop();
    this.autoBuy.stop();
    this.autoMills.stop();
    this.botSystem.stop();
    this.autoCombat.stop();
    this.resourceOptimizer.stop();
    
    // Show emergency stop notification
    this.showNotification('EMERGENCY STOP ACTIVATED', 'error');
    
    // Update UI
    this.updateUI();
    
    this.emit('emergencyStop');
  }
  
  private startModules(config: ModConfig): void {
    if (config.autoHats.enabled && !this.emergencyStopActive) {
      this.autoHats.start();
    }
    
    if (config.autoBuy.enabled && !this.emergencyStopActive) {
      this.autoBuy.start();
    }
    
    if (config.autoMills.enabled && !this.emergencyStopActive) {
      this.autoMills.start();
    }
    
    if (config.botSystem.enabled && !this.emergencyStopActive) {
      this.botSystem.start();
    }

    // Note: Auto Combat and Resource Optimizer are controlled separately
    // They can be started/stopped via the mod menu or API calls
  }
  
  private stopModules(): void {
    this.autoHats.stop();
    this.autoBuy.stop();
    this.autoMills.stop();
    this.botSystem.stop();
    this.autoCombat.stop();
    this.resourceOptimizer.stop();
  }

  private createUI(): void {
    if (this.ui) return;

    const config = this.configManager.getConfig();
    if (!config.ui.showOverlay) return;

    // Create main UI container
    this.ui = document.createElement('div');
    this.ui.id = 'sploop-advanced-mod-ui';
    this.ui.style.cssText = `
      position: fixed;
      ${config.ui.overlayPosition.includes('top') ? 'top: 10px;' : 'bottom: 10px;'}
      ${config.ui.overlayPosition.includes('right') ? 'right: 10px;' : 'left: 10px;'}
      width: 300px;
      background: rgba(0, 0, 0, ${config.ui.transparency});
      border: 2px solid #4CAF50;
      border-radius: 8px;
      padding: 10px;
      font-family: Arial, sans-serif;
      font-size: 12px;
      color: white;
      z-index: 10000;
      user-select: none;
    `;

    document.body.appendChild(this.ui);
    this.updateUI();
  }

  private updateUI(): void {
    if (!this.ui) return;

    const config = this.configManager.getConfig();
    const stats = this.getModuleStats();

    this.ui.innerHTML = `
      <div style="border-bottom: 1px solid #4CAF50; margin-bottom: 8px; padding-bottom: 5px;">
        <strong>🚀 Sploop Advanced Mod v1.0.0</strong>
        ${this.emergencyStopActive ? '<span style="color: red; float: right;">⚠️ STOPPED</span>' : '<span style="color: green; float: right;">✅ ACTIVE</span>'}
      </div>

      ${config.ui.showStats ? this.renderStats(stats) : ''}

      <div style="margin-top: 8px; font-size: 10px; opacity: 0.7;">
        Emergency Stop: ${config.safety.emergencyStop}
      </div>
    `;
  }

  private renderStats(stats: ModuleStats): string {
    return `
      <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 5px; font-size: 10px;">
        <div>
          <strong>🎩 Auto Hats</strong><br>
          Status: ${stats.autoHats.enabled ? '✅' : '❌'}<br>
          Owned: ${stats.autoHats.ownedHats}/${stats.autoHats.totalHats}<br>
          Current: ${stats.autoHats.equippedHat}
        </div>

        <div>
          <strong>💰 Auto Buy</strong><br>
          Status: ${stats.autoBuy.enabled ? '✅' : '❌'}<br>
          Purchases: ${stats.autoBuy.totalPurchases}<br>
          Queue: ${stats.autoBuy.queueLength}
        </div>

        <div>
          <strong>🏭 Auto Mills</strong><br>
          Status: ${stats.autoMills.enabled ? '✅' : '❌'}<br>
          Mills: ${stats.autoMills.totalMills}/${stats.autoMills.maxMills}<br>
          Protected: ${stats.autoMills.protectedMills}
        </div>

        <div>
          <strong>🤖 Bot System</strong><br>
          Status: ${stats.botSystem.enabled ? '✅' : '❌'}<br>
          Bots: ${stats.botSystem.activeBots}/${stats.botSystem.maxBots}<br>
          Threat: ${stats.botSystem.threatLevel}
        </div>

        <div>
          <strong>⚔️ Auto Combat</strong><br>
          Status: ${stats.autoCombat.enabled ? '✅' : '❌'}<br>
          Mode: ${stats.autoCombat.combatMode}<br>
          Target: ${stats.autoCombat.hasTarget ? '🎯' : '❌'}
        </div>

        <div>
          <strong>📈 Resource Optimizer</strong><br>
          Status: ${stats.resourceOptimizer.enabled ? '✅' : '❌'}<br>
          Nodes: ${stats.resourceOptimizer.knownNodes}<br>
          Route: ${stats.resourceOptimizer.routeProgress}
        </div>
      </div>
    `;
  }

  private getModuleStats(): ModuleStats {
    return {
      autoHats: this.autoHats.getStats(),
      autoBuy: this.autoBuy.getStats(),
      autoMills: this.autoMills.getStats(),
      botSystem: this.botSystem.getStats(),
      autoCombat: this.autoCombat.getStats(),
      resourceOptimizer: this.resourceOptimizer.getStats()
    };
  }

  private logAction(module: string, action: string): void {
    const config = this.configManager.getConfig();

    if (config.ui.showLogs) {
      console.log(`[${module}] ${action}`);
    }

    if (config.debug) {
      console.debug(`[${module}] ${action}`);
    }
  }

  private showNotification(message: string, type: 'info' | 'success' | 'warning' | 'error' = 'info'): void {
    const notification = document.createElement('div');
    notification.style.cssText = `
      position: fixed;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      background: ${type === 'error' ? '#f44336' : type === 'warning' ? '#ff9800' : type === 'success' ? '#4CAF50' : '#2196F3'};
      color: white;
      padding: 15px 25px;
      border-radius: 5px;
      font-family: Arial, sans-serif;
      font-size: 14px;
      font-weight: bold;
      z-index: 20000;
      box-shadow: 0 4px 8px rgba(0,0,0,0.3);
    `;
    notification.textContent = message;

    document.body.appendChild(notification);

    setTimeout(() => {
      if (notification.parentNode) {
        notification.parentNode.removeChild(notification);
      }
    }, 3000);
  }

  // Public API methods
  public getConfig(): ModConfig {
    return this.configManager.getConfig();
  }

  public updateConfig(updates: Partial<ModConfig>): void {
    this.configManager.updateConfig(updates);
    const newConfig = this.configManager.getConfig();

    // Update modules with new config
    this.autoHats.updateConfig(newConfig);
    this.autoBuy.updateConfig(newConfig);
    this.autoMills.updateConfig(newConfig);
    this.botSystem.updateConfig(newConfig);

    // Restart modules if needed
    this.stopModules();
    this.startModules(newConfig);

    this.updateUI();
  }

  public resetConfig(): void {
    this.configManager.resetConfig();
    this.updateConfig({});
  }

  public getStats(): ModuleStats {
    return this.getModuleStats();
  }

  public toggleModule(moduleName: 'autoHats' | 'autoBuy' | 'autoMills' | 'botSystem'): void {
    const config = this.getConfig();

    switch (moduleName) {
      case 'autoHats':
        this.updateConfig({ autoHats: { ...config.autoHats, enabled: !config.autoHats.enabled } });
        break;
      case 'autoBuy':
        this.updateConfig({ autoBuy: { ...config.autoBuy, enabled: !config.autoBuy.enabled } });
        break;
      case 'autoMills':
        this.updateConfig({ autoMills: { ...config.autoMills, enabled: !config.autoMills.enabled } });
        break;
      case 'botSystem':
        this.updateConfig({ botSystem: { ...config.botSystem, enabled: !config.botSystem.enabled } });
        break;
    }
  }

  public toggleAutoCombat(): void {
    if (this.autoCombat.getStats().enabled) {
      this.autoCombat.stop();
    } else {
      this.autoCombat.start();
    }
  }

  public toggleResourceOptimizer(): void {
    if (this.resourceOptimizer.getStats().enabled) {
      this.resourceOptimizer.stop();
    } else {
      this.resourceOptimizer.start();
    }
  }

  public toggleModMenu(): void {
    // Toggle mod menu visibility (to be implemented)
    this.showNotification('Mod Menu: Ctrl+M (Feature coming soon)', 'info');
  }

  public setAutoCombatMode(mode: 'defensive' | 'aggressive' | 'passive'): void {
    this.autoCombat.setCombatMode(mode);
  }

  public setAutoCombatStrategy(strategy: string): void {
    this.autoCombat.setStrategy(strategy);
  }

  public setResourceOptimizerStrategy(strategy: string): void {
    this.resourceOptimizer.setStrategy(strategy);
  }

  public destroy(): void {
    this.stopModules();

    if (this.ui && this.ui.parentNode) {
      this.ui.parentNode.removeChild(this.ui);
    }

    this.gameApi.destroy();
    this.removeAllListeners();

    console.log('[SploopAdvancedMod] Destroyed');
  }
}

// Initialize the mod when the script loads
let modInstance: SploopAdvancedMod | null = null;

function initializeMod(): void {
  if (modInstance) {
    modInstance.destroy();
  }

  modInstance = new SploopAdvancedMod();

  // Expose to global scope for debugging
  (window as any).SploopAdvancedMod = modInstance;
}

// Auto-initialize when page loads
if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', initializeMod);
} else {
  initializeMod();
}

// Handle page navigation in SPAs
let lastUrl = location.href;
new MutationObserver(() => {
  const url = location.href;
  if (url !== lastUrl) {
    lastUrl = url;
    setTimeout(initializeMod, 1000); // Reinitialize after navigation
  }
}).observe(document, { subtree: true, childList: true });

export default SploopAdvancedMod;
