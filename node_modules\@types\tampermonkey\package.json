{"name": "@types/tampermonkey", "version": "4.20.4", "description": "TypeScript definitions for tampermonkey", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/tampermonkey", "license": "MIT", "contributors": [{"name": "<PERSON>", "githubUsername": "silverwzw", "url": "https://github.com/silverwzw"}, {"name": "<PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON>-b<PERSON><PERSON><PERSON>", "url": "https://github.com/niko<PERSON>-borzov"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/taozhiyu"}, {"name": "double-beep", "githubUsername": "double-beep", "url": "https://github.com/double-beep"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/tampermonkey"}, "scripts": {}, "dependencies": {}, "typesPublisherContentHash": "f2a27f2e72f7f175f9ad247588966515d7544a0d0a444872b0c4b5b31e18002c", "typeScriptVersion": "4.5", "nonNpm": true}