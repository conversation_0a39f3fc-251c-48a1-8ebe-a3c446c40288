module.exports = {
  parser: '@typescript-eslint/parser',
  parserOptions: {
    ecmaVersion: 2020,
    sourceType: 'module',
    project: './tsconfig.json',
  },
  plugins: ['@typescript-eslint'],
  extends: [
    'eslint:recommended',
    '@typescript-eslint/recommended',
  ],
  rules: {
    // TypeScript specific rules
    '@typescript-eslint/no-unused-vars': ['error', { argsIgnorePattern: '^_' }],
    '@typescript-eslint/no-explicit-any': 'warn',
    '@typescript-eslint/explicit-function-return-type': 'off',
    '@typescript-eslint/explicit-module-boundary-types': 'off',
    '@typescript-eslint/no-non-null-assertion': 'warn',
    
    // General rules
    'no-console': 'off', // Allow console for debugging
    'no-debugger': 'warn',
    'prefer-const': 'error',
    'no-var': 'error',
    'eqeqeq': 'error',
    'curly': 'error',
    
    // Style rules
    'indent': ['error', 2],
    'quotes': ['error', 'single'],
    'semi': ['error', 'always'],
    'comma-dangle': ['error', 'never'],
    'object-curly-spacing': ['error', 'always'],
    'array-bracket-spacing': ['error', 'never'],
  },
  env: {
    browser: true,
    es6: true,
    node: true,
  },
  globals: {
    // Tampermonkey globals
    'GM_info': 'readonly',
    'GM_getValue': 'readonly',
    'GM_setValue': 'readonly',
    'GM_deleteValue': 'readonly',
    'GM_listValues': 'readonly',
    'GM_addStyle': 'readonly',
    'GM_getResourceText': 'readonly',
    'GM_getResourceURL': 'readonly',
    'GM_registerMenuCommand': 'readonly',
    'GM_unregisterMenuCommand': 'readonly',
    'GM_openInTab': 'readonly',
    'GM_xmlhttpRequest': 'readonly',
    'GM_download': 'readonly',
    'GM_getTab': 'readonly',
    'GM_saveTab': 'readonly',
    'GM_getTabs': 'readonly',
    'GM_notification': 'readonly',
    'GM_setClipboard': 'readonly',
    'unsafeWindow': 'readonly',
  },
};
