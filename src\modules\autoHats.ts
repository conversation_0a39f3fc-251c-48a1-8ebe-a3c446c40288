import { SploopGameAPI } from '@/utils/gameApi';
import { ModConfig } from '@/config';
import { Hat, HatType, Player, ActionType } from '@/types/game';
import { EventEmitter } from 'eventemitter3';

export interface HatInfo {
  id: string;
  type: HatType;
  name: string;
  cost: number;
  unlockLevel: number;
  owned: boolean;
  equipped: boolean;
  stats: {
    healthBonus: number;
    speedBonus: number;
    damageBonus: number;
    resourceBonus: number;
    experienceBonus: number;
  };
}

export class AutoHatsModule extends EventEmitter {
  private gameApi: SploopGameAPI;
  private config: ModConfig;
  private enabled = false;
  private updateInterval: number | null = null;
  private lastHatCheck = 0;
  private currentSituation: 'combat' | 'farming' | 'exploration' | 'idle' = 'idle';
  
  // Hat database with stats and costs
  private readonly HAT_DATABASE: Record<HatType, HatInfo> = {
    [HatType.NONE]: {
      id: 'hat_none',
      type: HatType.NONE,
      name: 'No Hat',
      cost: 0,
      unlockLevel: 0,
      owned: true,
      equipped: false,
      stats: { healthBonus: 0, speedBonus: 0, damageBonus: 0, resourceBonus: 0, experienceBonus: 0 }
    },
    [HatType.MARKSMAN]: {
      id: 'hat_marksman',
      type: HatType.MARKSMAN,
      name: 'Marksman Hat',
      cost: 7000,
      unlockLevel: 1,
      owned: false,
      equipped: false,
      stats: { healthBonus: 0, speedBonus: 0, damageBonus: 25, resourceBonus: 0, experienceBonus: 0 }
    },
    [HatType.BUSH]: {
      id: 'hat_bush',
      type: HatType.BUSH,
      name: 'Bush Hat',
      cost: 3000,
      unlockLevel: 1,
      owned: false,
      equipped: false,
      stats: { healthBonus: 20, speedBonus: 0, damageBonus: 0, resourceBonus: 0, experienceBonus: 0 }
    },
    [HatType.BERSERKER]: {
      id: 'hat_berserker',
      type: HatType.BERSERKER,
      name: 'Berserker Hat',
      cost: 12000,
      unlockLevel: 7,
      owned: false,
      equipped: false,
      stats: { healthBonus: 0, speedBonus: 15, damageBonus: 35, resourceBonus: 0, experienceBonus: 0 }
    },
    [HatType.JUNGLE]: {
      id: 'hat_jungle',
      type: HatType.JUNGLE,
      name: 'Jungle Hat',
      cost: 15000,
      unlockLevel: 6,
      owned: false,
      equipped: false,
      stats: { healthBonus: 30, speedBonus: 0, damageBonus: 0, resourceBonus: 15, experienceBonus: 0 }
    },
    [HatType.CRYSTAL]: {
      id: 'hat_crystal',
      type: HatType.CRYSTAL,
      name: 'Crystal Hat',
      cost: 25000,
      unlockLevel: 12,
      owned: false,
      equipped: false,
      stats: { healthBonus: 0, speedBonus: 0, damageBonus: 0, resourceBonus: 25, experienceBonus: 25 }
    },
    [HatType.SPACE]: {
      id: 'hat_space',
      type: HatType.SPACE,
      name: 'Space Hat',
      cost: 30000,
      unlockLevel: 15,
      owned: false,
      equipped: false,
      stats: { healthBonus: 40, speedBonus: 20, damageBonus: 0, resourceBonus: 0, experienceBonus: 0 }
    },
    [HatType.CYBORG]: {
      id: 'hat_cyborg',
      type: HatType.CYBORG,
      name: 'Cyborg Hat',
      cost: 50000,
      unlockLevel: 18,
      owned: false,
      equipped: false,
      stats: { healthBonus: 25, speedBonus: 0, damageBonus: 40, resourceBonus: 0, experienceBonus: 15 }
    },
    [HatType.MONKEY]: {
      id: 'hat_monkey',
      type: HatType.MONKEY,
      name: 'Monkey Hat',
      cost: 8000,
      unlockLevel: 4,
      owned: false,
      equipped: false,
      stats: { healthBonus: 0, speedBonus: 25, damageBonus: 0, resourceBonus: 20, experienceBonus: 0 }
    },
    [HatType.ELF]: {
      id: 'hat_elf',
      type: HatType.ELF,
      name: 'Elf Hat',
      cost: 20000,
      unlockLevel: 9,
      owned: false,
      equipped: false,
      stats: { healthBonus: 0, speedBonus: 0, damageBonus: 20, resourceBonus: 30, experienceBonus: 0 }
    },
    [HatType.KNIGHT]: {
      id: 'hat_knight',
      type: HatType.KNIGHT,
      name: 'Knight Hat',
      cost: 35000,
      unlockLevel: 14,
      owned: false,
      equipped: false,
      stats: { healthBonus: 50, speedBonus: 0, damageBonus: 15, resourceBonus: 0, experienceBonus: 0 }
    },
    [HatType.SAMURAI]: {
      id: 'hat_samurai',
      type: HatType.SAMURAI,
      name: 'Samurai Hat',
      cost: 70000,
      unlockLevel: 20,
      owned: false,
      equipped: false,
      stats: { healthBonus: 35, speedBonus: 10, damageBonus: 50, resourceBonus: 0, experienceBonus: 0 }
    },
    [HatType.ANGEL]: {
      id: 'hat_angel',
      type: HatType.ANGEL,
      name: 'Angel Hat',
      cost: 100000,
      unlockLevel: 25,
      owned: false,
      equipped: false,
      stats: { healthBonus: 60, speedBonus: 15, damageBonus: 30, resourceBonus: 20, experienceBonus: 30 }
    },
    [HatType.DEVIL]: {
      id: 'hat_devil',
      type: HatType.DEVIL,
      name: 'Devil Hat',
      cost: 150000,
      unlockLevel: 30,
      owned: false,
      equipped: false,
      stats: { healthBonus: 40, speedBonus: 20, damageBonus: 60, resourceBonus: 15, experienceBonus: 25 }
    }
  };
  
  constructor(gameApi: SploopGameAPI, config: ModConfig) {
    super();
    this.gameApi = gameApi;
    this.config = config;
    
    this.gameApi.on('stateUpdate', this.onGameStateUpdate.bind(this));
  }
  
  public start(): void {
    if (this.enabled) return;
    
    this.enabled = true;
    this.updateInterval = window.setInterval(() => {
      this.update();
    }, this.config.updateInterval);
    
    this.emit('started');
    console.log('[AutoHats] Module started');
  }
  
  public stop(): void {
    if (!this.enabled) return;
    
    this.enabled = false;
    if (this.updateInterval) {
      clearInterval(this.updateInterval);
      this.updateInterval = null;
    }
    
    this.emit('stopped');
    console.log('[AutoHats] Module stopped');
  }
  
  public updateConfig(config: ModConfig): void {
    this.config = config;
  }
  
  private onGameStateUpdate(): void {
    if (!this.enabled || !this.config.autoHats.enabled) return;
    
    this.updateHatOwnership();
    this.analyzeSituation();
  }
  
  private update(): void {
    if (!this.enabled || !this.config.autoHats.enabled || !this.gameApi.isInGame()) return;
    
    const now = Date.now();
    if (now - this.lastHatCheck < 2000) return; // Check every 2 seconds
    
    this.lastHatCheck = now;
    
    try {
      this.processAutoHats();
    } catch (error) {
      console.error('[AutoHats] Error in update:', error);
    }
  }
  
  private updateHatOwnership(): void {
    const player = this.gameApi.getPlayer();
    if (!player) return;
    
    // Update owned hats based on player inventory
    Object.values(this.HAT_DATABASE).forEach(hat => {
      hat.owned = this.isHatOwned(hat.type, player);
      hat.equipped = this.isHatEquipped(hat.type, player);
    });
  }
  
  private isHatOwned(hatType: HatType, player: Player): boolean {
    if (hatType === HatType.NONE) return true;
    
    // Check if hat is in inventory
    return player.inventory.some(item => 
      item.type === 'hat' && item.id.includes(hatType)
    );
  }
  
  private isHatEquipped(hatType: HatType, player: Player): boolean {
    if (!player.hat) return hatType === HatType.NONE;
    return player.hat.type === hatType;
  }
  
  private analyzeSituation(): void {
    const player = this.gameApi.getPlayer();
    const players = this.gameApi.getPlayers();
    
    if (!player) return;
    
    // Determine current situation based on game state
    const nearbyEnemies = Array.from(players.values()).filter(p => 
      p.id !== player.id && 
      this.gameApi.calculateDistance(player.position, p.position) < 300
    );
    
    const isLowHealth = player.health < player.maxHealth * 0.5;
    const hasResources = Object.values(player.resources).some(r => r > 100);
    
    if (nearbyEnemies.length > 0 || isLowHealth) {
      this.currentSituation = 'combat';
    } else if (hasResources) {
      this.currentSituation = 'farming';
    } else {
      this.currentSituation = 'exploration';
    }
  }
  
  private processAutoHats(): void {
    const player = this.gameApi.getPlayer();
    if (!player) return;
    
    let targetHat: HatType;
    
    if (this.config.autoHats.switchBasedOnSituation) {
      targetHat = this.getOptimalHatForSituation();
    } else {
      targetHat = this.getBestAvailableHat();
    }
    
    // Check if we need to buy the target hat
    if (this.config.autoHats.autoUpgrade && !this.HAT_DATABASE[targetHat].owned) {
      this.tryBuyHat(targetHat);
    }
    
    // Equip the target hat if we own it and it's not already equipped
    if (this.HAT_DATABASE[targetHat].owned && !this.HAT_DATABASE[targetHat].equipped) {
      this.equipHat(targetHat);
    }
  }
  
  private getOptimalHatForSituation(): HatType {
    switch (this.currentSituation) {
      case 'combat':
        return this.getBestOwnedHat([this.config.autoHats.combatHat]) || 
               this.getBestAvailableHat();
      case 'farming':
        return this.getBestOwnedHat([this.config.autoHats.farmingHat]) || 
               this.getBestAvailableHat();
      case 'exploration':
        return this.getBestOwnedHat([this.config.autoHats.explorationHat]) || 
               this.getBestAvailableHat();
      default:
        return this.getBestAvailableHat();
    }
  }
  
  private getBestOwnedHat(preferred: HatType[] = []): HatType | null {
    // First check preferred hats
    for (const hatType of preferred) {
      if (this.HAT_DATABASE[hatType].owned) {
        return hatType;
      }
    }
    
    // Then check priority list
    for (const hatType of this.config.autoHats.priority) {
      if (this.HAT_DATABASE[hatType].owned) {
        return hatType;
      }
    }
    
    return null;
  }
  
  private getBestAvailableHat(): HatType {
    const player = this.gameApi.getPlayer();
    if (!player) return HatType.NONE;
    
    // Find the best hat we can afford and have unlocked
    for (const hatType of this.config.autoHats.priority) {
      const hat = this.HAT_DATABASE[hatType];
      if (hat.owned || 
          (player.level >= hat.unlockLevel && player.resources.gold >= hat.cost)) {
        return hatType;
      }
    }
    
    return HatType.NONE;
  }
  
  private tryBuyHat(hatType: HatType): void {
    const player = this.gameApi.getPlayer();
    const hat = this.HAT_DATABASE[hatType];
    
    if (!player || hat.owned) return;
    
    if (player.level >= hat.unlockLevel && player.resources.gold >= hat.cost) {
      this.gameApi.sendAction({
        type: ActionType.BUY,
        data: { itemId: hat.id },
        timestamp: Date.now()
      });
      
      this.emit('hatPurchased', { hatType, cost: hat.cost });
      console.log(`[AutoHats] Purchased ${hat.name} for ${hat.cost} gold`);
    }
  }
  
  private equipHat(hatType: HatType): void {
    const hat = this.HAT_DATABASE[hatType];
    
    this.gameApi.sendAction({
      type: ActionType.EQUIP,
      data: { itemId: hat.id },
      timestamp: Date.now()
    });
    
    this.emit('hatEquipped', { hatType });
    console.log(`[AutoHats] Equipped ${hat.name}`);
  }
  
  public getHatInfo(hatType: HatType): HatInfo {
    return { ...this.HAT_DATABASE[hatType] };
  }
  
  public getAllHats(): HatInfo[] {
    return Object.values(this.HAT_DATABASE).map(hat => ({ ...hat }));
  }
  
  public getCurrentSituation(): string {
    return this.currentSituation;
  }
  
  public getStats(): any {
    const ownedHats = Object.values(this.HAT_DATABASE).filter(h => h.owned).length;
    const totalHats = Object.values(this.HAT_DATABASE).length;
    
    return {
      enabled: this.enabled,
      ownedHats,
      totalHats,
      currentSituation: this.currentSituation,
      equippedHat: Object.values(this.HAT_DATABASE).find(h => h.equipped)?.name || 'None'
    };
  }
}
