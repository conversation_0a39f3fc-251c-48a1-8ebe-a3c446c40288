{"name": "sploop-advanced-mod", "version": "1.0.0", "description": "Advanced sploop.io mod with auto hats, auto buy, auto mills, and bot system", "main": "dist/main.js", "scripts": {"build": "webpack --mode production && node scripts/build-userscript.js", "build:webpack": "webpack --mode production", "dev": "webpack --mode development --watch", "clean": "<PERSON><PERSON><PERSON> dist", "lint": "eslint src/**/*.ts", "type-check": "tsc --noEmit", "userscript": "node scripts/build-userscript.js"}, "keywords": ["sploop.io", "mod", "automation", "typescript", "userscript"], "author": "Advanced Mod Developer", "license": "MIT", "devDependencies": {"@types/node": "^20.0.0", "@types/tampermonkey": "^4.0.0", "@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "eslint": "^8.0.0", "rimraf": "^5.0.0", "ts-loader": "^9.0.0", "typescript": "^5.0.0", "webpack": "^5.0.0", "webpack-cli": "^5.0.0"}, "dependencies": {"eventemitter3": "^5.0.0"}}