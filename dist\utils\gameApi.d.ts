import { Game<PERSON><PERSON>, GameState, Player, Building, GameAction, Vector2 } from '@/types/game';
import { EventEmitter } from 'eventemitter3';
declare global {
    interface Window {
        game: any;
        io: any;
        player: any;
        players: any;
        buildings: any;
        items: any;
        socket: any;
    }
}
export declare class SploopGameAPI extends EventEmitter<string | symbol, any> implements GameAPI {
    private gameState;
    private updateInterval;
    private lastActionTime;
    private actionQueue;
    constructor();
    private initialize;
    private isGameLoaded;
    private startMonitoring;
    private updateGameState;
    private parsePlayer;
    private parsePlayers;
    private parseBuildings;
    getPlayer(): Player | null;
    getPlayers(): Map<string, Player>;
    getBuildings(): Map<string, Building>;
    getGameState(): GameState;
    sendAction(action: GameAction): void;
    private processActionQueue;
    private executeAction;
    private move;
    private attack;
    private build;
    private buy;
    private equip;
    sendChat(message: string): void;
    isInGame(): boolean;
    calculateDistance(pos1: Vector2, pos2: Vector2): number;
    calculateAngle(from: Vector2, to: Vector2): number;
    destroy(): void;
}
//# sourceMappingURL=gameApi.d.ts.map