import { HatType, ResourceType } from '@/types/game';
export interface ModConfig {
    enabled: boolean;
    debug: boolean;
    safeMode: boolean;
    updateInterval: number;
    autoHats: {
        enabled: boolean;
        priority: HatType[];
        autoUpgrade: boolean;
        switchBasedOnSituation: boolean;
        combatHat: HatType;
        farmingHat: HatType;
        explorationHat: HatType;
    };
    autoBuy: {
        enabled: boolean;
        priorityItems: string[];
        maxSpendPercentage: number;
        reserveResources: {
            wood: number;
            stone: number;
            food: number;
            gold: number;
        };
        buyHats: boolean;
        buyWeapons: boolean;
        buyUpgrades: boolean;
    };
    autoMills: {
        enabled: boolean;
        maxMills: number;
        resourcePriority: ResourceType[];
        autoUpgrade: boolean;
        optimalPlacement: boolean;
        protectMills: boolean;
        millSpacing: number;
        preferredLocations: {
            nearResources: boolean;
            nearBase: boolean;
            hiddenAreas: boolean;
        };
    };
    botSystem: {
        enabled: boolean;
        maxBots: number;
        botBehaviors: {
            farming: boolean;
            combat: boolean;
            building: boolean;
            exploration: boolean;
            protection: boolean;
        };
        coordination: {
            shareResources: boolean;
            groupMovement: boolean;
            defendTogether: boolean;
        };
        intelligence: {
            avoidPlayers: boolean;
            learnFromActions: boolean;
            adaptToSituation: boolean;
        };
    };
    safety: {
        antiDetection: boolean;
        randomizeActions: boolean;
        humanLikeMovement: boolean;
        pauseOnDanger: boolean;
        emergencyStop: string;
        maxActionsPerSecond: number;
    };
    ui: {
        showOverlay: boolean;
        showStats: boolean;
        showLogs: boolean;
        overlayPosition: 'top-left' | 'top-right' | 'bottom-left' | 'bottom-right';
        transparency: number;
    };
}
export declare const DEFAULT_CONFIG: ModConfig;
export declare class ConfigManager {
    private config;
    private readonly STORAGE_KEY;
    constructor();
    getConfig(): ModConfig;
    updateConfig(updates: Partial<ModConfig>): void;
    resetConfig(): void;
    private loadConfig;
    private saveConfig;
}
//# sourceMappingURL=config.d.ts.map